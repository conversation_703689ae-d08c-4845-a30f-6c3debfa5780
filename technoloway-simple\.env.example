# Database
DATABASE_URL="postgresql://username:password@localhost:5432/technoloway_db"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secure-secret-key-here-change-in-production"

# OAuth Providers (optional)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
GITHUB_ID=""
GITHUB_SECRET=""

# File Upload Configuration
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"

# Email Configuration (for contact forms, notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Application Settings
APP_NAME="Technoloway"
APP_URL="http://localhost:3000"
ADMIN_EMAIL="<EMAIL>"

# Security
JWT_SECRET="your-jwt-secret-here-change-in-production"
ENCRYPTION_KEY="your-encryption-key-here-change-in-production"

# Analytics and Tracking
NEXT_PUBLIC_GA_ID=""  # Google Analytics 4 Measurement ID
NEXT_PUBLIC_GTM_ID=""  # Google Tag Manager ID

# reCAPTCHA (for contact forms)
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=""
RECAPTCHA_SECRET_KEY=""

# Monitoring and Error Tracking (optional)
SENTRY_DSN=""  # Sentry for error tracking
DATADOG_API_KEY=""  # DataDog for metrics
NEW_RELIC_LICENSE_KEY=""  # New Relic for APM

# Social Media Links
NEXT_PUBLIC_TWITTER_URL="https://twitter.com/technoloway"
NEXT_PUBLIC_LINKEDIN_URL="https://linkedin.com/company/technoloway"
NEXT_PUBLIC_GITHUB_URL="https://github.com/technoloway"
NEXT_PUBLIC_FACEBOOK_URL="https://facebook.com/technoloway"

# Development/Production Environment
NODE_ENV="development"

# Deployment (for CI/CD)
VERCEL_TOKEN=""  # For Vercel deployment
VERCEL_ORG_ID=""
VERCEL_PROJECT_ID=""
