# Database
# Replace with your actual PostgreSQL connection string
# Format: postgresql://username:password@host:port/database_name
DATABASE_URL="postgresql://postgres:password@localhost:5432/technoloway_db"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="technoloway-nextauth-secret-change-in-production-2024"

# Application Settings
APP_NAME="Technoloway"
APP_URL="http://localhost:3000"
ADMIN_EMAIL="<EMAIL>"

# Security
JWT_SECRET="technoloway-jwt-secret-change-in-production-2024"

# File Upload Configuration
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"

# Email Configuration (for contact forms and notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER=""  # Your email
SMTP_PASS=""  # Your app password

# Development
NODE_ENV="development"

# Optional: For production deployment
# VERCEL_URL=""
# RAILWAY_STATIC_URL=""
