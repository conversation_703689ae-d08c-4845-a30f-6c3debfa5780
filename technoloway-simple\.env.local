# Database
# Using SQLite for development (no server required)
# For production, use PostgreSQL: postgresql://username:password@host:port/database_name
DATABASE_URL="file:./dev.db"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="technoloway-nextauth-secret-change-in-production-2024"

# OAuth Providers (optional - add your credentials to enable)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
GITHUB_ID=""
GITHUB_SECRET=""

# Application Settings
APP_NAME="Technoloway"
APP_URL="http://localhost:3000"
ADMIN_EMAIL="<EMAIL>"

# Security
JWT_SECRET="technoloway-jwt-secret-change-in-production-2024"

# File Upload Configuration
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"

# Email Configuration (for contact forms and notifications)
SMTP_HOST="mail413.mailasp.net"
SMTP_PORT=25
SMTP_USER="<EMAIL>"  # Add your email here
SMTP_PASS="Technoloway@22"  # Add your app password here

# Analytics and Tracking
NEXT_PUBLIC_GA_ID=""  # Add your Google Analytics 4 Measurement ID
NEXT_PUBLIC_GTM_ID=""  # Add your Google Tag Manager ID (optional)

# reCAPTCHA (for contact forms - optional)
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=""
RECAPTCHA_SECRET_KEY=""

# Monitoring and Error Tracking (optional)
SENTRY_DSN=""  # Sentry for error tracking
DATADOG_API_KEY=""  # DataDog for metrics
NEW_RELIC_LICENSE_KEY=""  # New Relic for APM

# Social Media Links (used in branding)
NEXT_PUBLIC_TWITTER_URL="https://twitter.com/technoloway"
NEXT_PUBLIC_LINKEDIN_URL="https://linkedin.com/company/technoloway"
NEXT_PUBLIC_GITHUB_URL="https://github.com/technoloway"
NEXT_PUBLIC_FACEBOOK_URL="https://facebook.com/technoloway"

# Development
NODE_ENV="development"

# Deployment (for CI/CD - add when deploying)
VERCEL_TOKEN=""  # For Vercel deployment
VERCEL_ORG_ID=""
VERCEL_PROJECT_ID=""
