[{"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\about-pages\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\blog\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\chatbot\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\clients\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\contact-forms\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\data-upload\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\hero-sections\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\invoices\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\jobs\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx": "11", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\legal-pages\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\projects\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\services\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\settings\\page.tsx": "16", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\team-members\\page.tsx": "17", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\technologies\\page.tsx": "18", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\testimonials\\page.tsx": "19", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\users\\page.tsx": "20", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\clients\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\dashboard\\stats\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\[id]\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\[id]\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\blog\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\contact\\page.tsx": "28", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx": "29", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\portfolio\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\[slug]\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\[slug]\\page.tsx": "35", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\team\\page.tsx": "36", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\[slug]\\page.tsx": "38", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\footer.tsx": "39", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\header.tsx": "40", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\api-utils.ts": "41", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\prisma.ts": "42", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\validations.ts": "43"}, {"size": 13657, "mtime": 1749671010969, "results": "44", "hashOfConfig": "45"}, {"size": 17327, "mtime": 1749667378423, "results": "46", "hashOfConfig": "45"}, {"size": 17128, "mtime": 1749663650370, "results": "47", "hashOfConfig": "45"}, {"size": 26363, "mtime": 1749668075115, "results": "48", "hashOfConfig": "45"}, {"size": 16903, "mtime": 1749663720740, "results": "49", "hashOfConfig": "45"}, {"size": 20417, "mtime": 1749663877267, "results": "50", "hashOfConfig": "45"}, {"size": 22039, "mtime": 1749667991007, "results": "51", "hashOfConfig": "45"}, {"size": 15425, "mtime": 1749667317630, "results": "52", "hashOfConfig": "45"}, {"size": 25277, "mtime": 1749667909047, "results": "53", "hashOfConfig": "45"}, {"size": 26814, "mtime": 1749667819685, "results": "54", "hashOfConfig": "45"}, {"size": 11656, "mtime": 1749663410254, "results": "55", "hashOfConfig": "45"}, {"size": 23861, "mtime": 1749667720524, "results": "56", "hashOfConfig": "45"}, {"size": 13167, "mtime": 1749663463735, "results": "57", "hashOfConfig": "45"}, {"size": 16438, "mtime": 1749663582524, "results": "58", "hashOfConfig": "45"}, {"size": 22201, "mtime": 1749667451813, "results": "59", "hashOfConfig": "45"}, {"size": 17124, "mtime": 1749663781811, "results": "60", "hashOfConfig": "45"}, {"size": 14019, "mtime": 1749663520985, "results": "61", "hashOfConfig": "45"}, {"size": 26198, "mtime": 1749667537222, "results": "62", "hashOfConfig": "45"}, {"size": 24731, "mtime": 1749667624873, "results": "63", "hashOfConfig": "45"}, {"size": 24497, "mtime": 1749668155062, "results": "64", "hashOfConfig": "45"}, {"size": 5960, "mtime": 1749675735244, "results": "65", "hashOfConfig": "45"}, {"size": 5504, "mtime": 1749676202151, "results": "66", "hashOfConfig": "45"}, {"size": 5615, "mtime": 1749675675766, "results": "67", "hashOfConfig": "45"}, {"size": 8255, "mtime": 1749675706965, "results": "68", "hashOfConfig": "45"}, {"size": 4257, "mtime": 1749675626380, "results": "69", "hashOfConfig": "45"}, {"size": 5535, "mtime": 1749675649625, "results": "70", "hashOfConfig": "45"}, {"size": 12502, "mtime": 1749620380724, "results": "71", "hashOfConfig": "45"}, {"size": 22471, "mtime": 1749671507346, "results": "72", "hashOfConfig": "45"}, {"size": 1640, "mtime": 1749619132991, "results": "73", "hashOfConfig": "45"}, {"size": 22774, "mtime": 1749620619309, "results": "74", "hashOfConfig": "45"}, {"size": 17645, "mtime": 1749620425563, "results": "75", "hashOfConfig": "45"}, {"size": 19574, "mtime": 1749671208197, "results": "76", "hashOfConfig": "45"}, {"size": 20166, "mtime": 1749671283514, "results": "77", "hashOfConfig": "45"}, {"size": 16128, "mtime": 1749671069182, "results": "78", "hashOfConfig": "45"}, {"size": 19440, "mtime": 1749671137251, "results": "79", "hashOfConfig": "45"}, {"size": 20803, "mtime": 1749620525864, "results": "80", "hashOfConfig": "45"}, {"size": 23204, "mtime": 1749671363990, "results": "81", "hashOfConfig": "45"}, {"size": 19654, "mtime": 1749671433776, "results": "82", "hashOfConfig": "45"}, {"size": 2782, "mtime": 1749620348135, "results": "83", "hashOfConfig": "45"}, {"size": 5642, "mtime": 1749671671854, "results": "84", "hashOfConfig": "45"}, {"size": 6507, "mtime": 1749675606505, "results": "85", "hashOfConfig": "45"}, {"size": 279, "mtime": 1749675546287, "results": "86", "hashOfConfig": "45"}, {"size": 8128, "mtime": 1749675578619, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "12i8zce", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\about\\page.tsx", ["217", "218", "219"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\about-pages\\page.tsx", ["220", "221", "222", "223"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\blog\\page.tsx", ["224"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\chatbot\\page.tsx", ["225", "226"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\clients\\page.tsx", ["227", "228", "229"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\contact-forms\\page.tsx", ["230", "231", "232"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\data-upload\\page.tsx", ["233", "234"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\hero-sections\\page.tsx", ["235", "236", "237", "238", "239", "240", "241"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\invoices\\page.tsx", ["242", "243", "244", "245", "246"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\jobs\\page.tsx", ["247", "248", "249", "250"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx", ["251"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\legal-pages\\page.tsx", ["252", "253", "254"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\page.tsx", ["255", "256", "257", "258"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\services\\page.tsx", ["259", "260", "261", "262", "263", "264"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\settings\\page.tsx", ["265", "266", "267", "268", "269"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\team-members\\page.tsx", ["270", "271", "272"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\technologies\\page.tsx", ["273", "274", "275", "276", "277", "278"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\testimonials\\page.tsx", ["279", "280", "281", "282", "283", "284", "285", "286"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\users\\page.tsx", ["287", "288", "289", "290", "291"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\clients\\route.ts", ["292"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\dashboard\\stats\\route.ts", ["293"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\route.ts", ["294"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\[id]\\route.ts", ["295"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\route.ts", ["296"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\[id]\\route.ts", ["297", "298"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\contact\\page.tsx", ["299", "300", "301", "302", "303", "304"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\page.tsx", ["305", "306", "307", "308", "309", "310", "311", "312", "313", "314"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\portfolio\\page.tsx", ["315", "316"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\page.tsx", ["317", "318", "319", "320"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\[slug]\\page.tsx", ["321", "322", "323", "324", "325", "326", "327", "328"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\page.tsx", ["329", "330", "331", "332"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\[slug]\\page.tsx", ["333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\team\\page.tsx", ["350", "351", "352", "353", "354"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\page.tsx", ["355", "356", "357", "358", "359"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\[slug]\\page.tsx", ["360", "361", "362", "363", "364", "365", "366", "367"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\api-utils.ts", ["368", "369", "370", "371"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\validations.ts", [], [], {"ruleId": "372", "severity": 1, "message": "373", "line": 5, "column": 3, "nodeType": null, "messageId": "374", "endLine": 5, "endColumn": 16}, {"ruleId": "372", "severity": 1, "message": "375", "line": 9, "column": 3, "nodeType": null, "messageId": "374", "endLine": 9, "endColumn": 13}, {"ruleId": "376", "severity": 1, "message": "377", "line": 318, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "380"}, {"ruleId": "372", "severity": 1, "message": "381", "line": 12, "column": 3, "nodeType": null, "messageId": "374", "endLine": 12, "endColumn": 14}, {"ruleId": "382", "severity": 1, "message": "383", "line": 90, "column": 52, "nodeType": "384", "messageId": "385", "endLine": 90, "endColumn": 55, "suggestions": "386"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 91, "column": 10, "nodeType": null, "messageId": "374", "endLine": 91, "endColumn": 22}, {"ruleId": "376", "severity": 1, "message": "377", "line": 136, "column": 34, "nodeType": "378", "messageId": "379", "suggestions": "388"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 290, "column": 25, "nodeType": "391", "endLine": 294, "endColumn": 27}, {"ruleId": "382", "severity": 1, "message": "383", "line": 111, "column": 68, "nodeType": "384", "messageId": "385", "endLine": 111, "endColumn": 71, "suggestions": "392"}, {"ruleId": "372", "severity": 1, "message": "393", "line": 112, "column": 10, "nodeType": null, "messageId": "374", "endLine": 112, "endColumn": 26}, {"ruleId": "372", "severity": 1, "message": "394", "line": 15, "column": 3, "nodeType": null, "messageId": "374", "endLine": 15, "endColumn": 21}, {"ruleId": "372", "severity": 1, "message": "395", "line": 16, "column": 3, "nodeType": null, "messageId": "374", "endLine": 16, "endColumn": 16}, {"ruleId": "389", "severity": 1, "message": "390", "line": 307, "column": 23, "nodeType": "391", "endLine": 311, "endColumn": 25}, {"ruleId": "372", "severity": 1, "message": "396", "line": 8, "column": 3, "nodeType": null, "messageId": "374", "endLine": 8, "endColumn": 11}, {"ruleId": "372", "severity": 1, "message": "397", "line": 9, "column": 3, "nodeType": null, "messageId": "374", "endLine": 9, "endColumn": 15}, {"ruleId": "382", "severity": 1, "message": "383", "line": 118, "column": 52, "nodeType": "384", "messageId": "385", "endLine": 118, "endColumn": 55, "suggestions": "398"}, {"ruleId": "372", "severity": 1, "message": "399", "line": 9, "column": 3, "nodeType": null, "messageId": "374", "endLine": 9, "endColumn": 26}, {"ruleId": "382", "severity": 1, "message": "383", "line": 93, "column": 56, "nodeType": "384", "messageId": "385", "endLine": 93, "endColumn": 59, "suggestions": "400"}, {"ruleId": "372", "severity": 1, "message": "401", "line": 11, "column": 3, "nodeType": null, "messageId": "374", "endLine": 11, "endColumn": 11}, {"ruleId": "372", "severity": 1, "message": "387", "line": 70, "column": 10, "nodeType": null, "messageId": "374", "endLine": 70, "endColumn": 22}, {"ruleId": "372", "severity": 1, "message": "402", "line": 71, "column": 10, "nodeType": null, "messageId": "374", "endLine": 71, "endColumn": 24}, {"ruleId": "382", "severity": 1, "message": "383", "line": 71, "column": 56, "nodeType": "384", "messageId": "385", "endLine": 71, "endColumn": 59, "suggestions": "403"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 72, "column": 56, "nodeType": "384", "messageId": "385", "endLine": 72, "endColumn": 59, "suggestions": "404"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 194, "column": 17, "nodeType": "391", "endLine": 198, "endColumn": 19}, {"ruleId": "389", "severity": 1, "message": "390", "line": 311, "column": 19, "nodeType": "391", "endLine": 315, "endColumn": 21}, {"ruleId": "372", "severity": 1, "message": "394", "line": 11, "column": 3, "nodeType": null, "messageId": "374", "endLine": 11, "endColumn": 21}, {"ruleId": "382", "severity": 1, "message": "383", "line": 152, "column": 58, "nodeType": "384", "messageId": "385", "endLine": 152, "endColumn": 61, "suggestions": "405"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 153, "column": 10, "nodeType": null, "messageId": "374", "endLine": 153, "endColumn": 22}, {"ruleId": "372", "severity": 1, "message": "406", "line": 164, "column": 9, "nodeType": null, "messageId": "374", "endLine": 164, "endColumn": 27}, {"ruleId": "382", "severity": 1, "message": "383", "line": 506, "column": 67, "nodeType": "384", "messageId": "385", "endLine": 506, "endColumn": 70, "suggestions": "407"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 204, "column": 50, "nodeType": "384", "messageId": "385", "endLine": 204, "endColumn": 53, "suggestions": "408"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 205, "column": 10, "nodeType": null, "messageId": "374", "endLine": 205, "endColumn": 22}, {"ruleId": "372", "severity": 1, "message": "409", "line": 226, "column": 9, "nodeType": null, "messageId": "374", "endLine": 226, "endColumn": 29}, {"ruleId": "382", "severity": 1, "message": "383", "line": 240, "column": 33, "nodeType": "384", "messageId": "385", "endLine": 240, "endColumn": 36, "suggestions": "410"}, {"ruleId": "372", "severity": 1, "message": "411", "line": 12, "column": 3, "nodeType": null, "messageId": "374", "endLine": 12, "endColumn": 15}, {"ruleId": "372", "severity": 1, "message": "381", "line": 12, "column": 3, "nodeType": null, "messageId": "374", "endLine": 12, "endColumn": 14}, {"ruleId": "382", "severity": 1, "message": "383", "line": 185, "column": 52, "nodeType": "384", "messageId": "385", "endLine": 185, "endColumn": 55, "suggestions": "412"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 186, "column": 10, "nodeType": null, "messageId": "374", "endLine": 186, "endColumn": 22}, {"ruleId": "382", "severity": 1, "message": "383", "line": 38, "column": 83, "nodeType": "384", "messageId": "385", "endLine": 38, "endColumn": 86, "suggestions": "413"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 82, "column": 37, "nodeType": "384", "messageId": "385", "endLine": 82, "endColumn": 40, "suggestions": "414"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 137, "column": 33, "nodeType": "378", "messageId": "379", "suggestions": "415"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 137, "column": 40, "nodeType": "378", "messageId": "379", "suggestions": "416"}, {"ruleId": "372", "severity": 1, "message": "394", "line": 16, "column": 3, "nodeType": null, "messageId": "374", "endLine": 16, "endColumn": 21}, {"ruleId": "382", "severity": 1, "message": "383", "line": 139, "column": 33, "nodeType": "384", "messageId": "385", "endLine": 139, "endColumn": 36, "suggestions": "417"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 151, "column": 58, "nodeType": "384", "messageId": "385", "endLine": 151, "endColumn": 61, "suggestions": "418"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 152, "column": 10, "nodeType": null, "messageId": "374", "endLine": 152, "endColumn": 22}, {"ruleId": "372", "severity": 1, "message": "409", "line": 166, "column": 9, "nodeType": null, "messageId": "374", "endLine": 166, "endColumn": 29}, {"ruleId": "382", "severity": 1, "message": "383", "line": 180, "column": 33, "nodeType": "384", "messageId": "385", "endLine": 180, "endColumn": 36, "suggestions": "419"}, {"ruleId": "372", "severity": 1, "message": "420", "line": 10, "column": 3, "nodeType": null, "messageId": "374", "endLine": 10, "endColumn": 15}, {"ruleId": "372", "severity": 1, "message": "421", "line": 11, "column": 3, "nodeType": null, "messageId": "374", "endLine": 11, "endColumn": 15}, {"ruleId": "372", "severity": 1, "message": "422", "line": 12, "column": 3, "nodeType": null, "messageId": "374", "endLine": 12, "endColumn": 10}, {"ruleId": "372", "severity": 1, "message": "423", "line": 15, "column": 3, "nodeType": null, "messageId": "374", "endLine": 15, "endColumn": 19}, {"ruleId": "382", "severity": 1, "message": "383", "line": 93, "column": 52, "nodeType": "384", "messageId": "385", "endLine": 93, "endColumn": 55, "suggestions": "424"}, {"ruleId": "372", "severity": 1, "message": "425", "line": 10, "column": 3, "nodeType": null, "messageId": "374", "endLine": 10, "endColumn": 13}, {"ruleId": "372", "severity": 1, "message": "387", "line": 81, "column": 10, "nodeType": null, "messageId": "374", "endLine": 81, "endColumn": 22}, {"ruleId": "389", "severity": 1, "message": "390", "line": 178, "column": 25, "nodeType": "391", "endLine": 182, "endColumn": 27}, {"ruleId": "382", "severity": 1, "message": "383", "line": 181, "column": 52, "nodeType": "384", "messageId": "385", "endLine": 181, "endColumn": 55, "suggestions": "426"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 182, "column": 10, "nodeType": null, "messageId": "374", "endLine": 182, "endColumn": 22}, {"ruleId": "372", "severity": 1, "message": "427", "line": 194, "column": 9, "nodeType": null, "messageId": "374", "endLine": 194, "endColumn": 27}, {"ruleId": "372", "severity": 1, "message": "428", "line": 216, "column": 9, "nodeType": null, "messageId": "374", "endLine": 216, "endColumn": 19}, {"ruleId": "389", "severity": 1, "message": "390", "line": 390, "column": 23, "nodeType": "391", "endLine": 394, "endColumn": 25}, {"ruleId": "389", "severity": 1, "message": "390", "line": 507, "column": 25, "nodeType": "391", "endLine": 511, "endColumn": 27}, {"ruleId": "382", "severity": 1, "message": "383", "line": 113, "column": 66, "nodeType": "384", "messageId": "385", "endLine": 113, "endColumn": 69, "suggestions": "429"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 114, "column": 10, "nodeType": null, "messageId": "374", "endLine": 114, "endColumn": 22}, {"ruleId": "389", "severity": 1, "message": "390", "line": 349, "column": 23, "nodeType": "391", "endLine": 353, "endColumn": 25}, {"ruleId": "376", "severity": 1, "message": "430", "line": 435, "column": 19, "nodeType": "378", "messageId": "379", "suggestions": "431"}, {"ruleId": "376", "severity": 1, "message": "430", "line": 435, "column": 41, "nodeType": "378", "messageId": "379", "suggestions": "432"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 476, "column": 25, "nodeType": "391", "endLine": 480, "endColumn": 27}, {"ruleId": "376", "severity": 1, "message": "430", "line": 505, "column": 29, "nodeType": "378", "messageId": "379", "suggestions": "433"}, {"ruleId": "376", "severity": 1, "message": "430", "line": 505, "column": 59, "nodeType": "378", "messageId": "379", "suggestions": "434"}, {"ruleId": "372", "severity": 1, "message": "422", "line": 17, "column": 3, "nodeType": null, "messageId": "374", "endLine": 17, "endColumn": 10}, {"ruleId": "382", "severity": 1, "message": "383", "line": 104, "column": 52, "nodeType": "384", "messageId": "385", "endLine": 104, "endColumn": 55, "suggestions": "435"}, {"ruleId": "372", "severity": 1, "message": "387", "line": 105, "column": 10, "nodeType": null, "messageId": "374", "endLine": 105, "endColumn": 22}, {"ruleId": "389", "severity": 1, "message": "390", "line": 342, "column": 25, "nodeType": "391", "endLine": 346, "endColumn": 27}, {"ruleId": "389", "severity": 1, "message": "390", "line": 447, "column": 25, "nodeType": "391", "endLine": 451, "endColumn": 27}, {"ruleId": "382", "severity": 1, "message": "383", "line": 23, "column": 16, "nodeType": "384", "messageId": "385", "endLine": 23, "endColumn": 19, "suggestions": "436"}, {"ruleId": "372", "severity": 1, "message": "437", "line": 16, "column": 9, "nodeType": null, "messageId": "374", "endLine": 16, "endColumn": 20}, {"ruleId": "382", "severity": 1, "message": "383", "line": 23, "column": 16, "nodeType": "384", "messageId": "385", "endLine": 23, "endColumn": 19, "suggestions": "438"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 339, "column": 21, "nodeType": "384", "messageId": "385", "endLine": 339, "endColumn": 24, "suggestions": "439"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 23, "column": 16, "nodeType": "384", "messageId": "385", "endLine": 23, "endColumn": 19, "suggestions": "440"}, {"ruleId": "372", "severity": 1, "message": "441", "line": 6, "column": 3, "nodeType": null, "messageId": "374", "endLine": 6, "endColumn": 16}, {"ruleId": "382", "severity": 1, "message": "383", "line": 206, "column": 21, "nodeType": "384", "messageId": "385", "endLine": 206, "endColumn": 24, "suggestions": "442"}, {"ruleId": "372", "severity": 1, "message": "443", "line": 12, "column": 3, "nodeType": null, "messageId": "374", "endLine": 12, "endColumn": 12}, {"ruleId": "372", "severity": 1, "message": "444", "line": 91, "column": 14, "nodeType": null, "messageId": "374", "endLine": 91, "endColumn": 19}, {"ruleId": "376", "severity": 1, "message": "377", "line": 118, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "445"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 128, "column": 24, "nodeType": "378", "messageId": "379", "suggestions": "446"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 191, "column": 49, "nodeType": "378", "messageId": "379", "suggestions": "447"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 213, "column": 55, "nodeType": "378", "messageId": "379", "suggestions": "448"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 226, "column": 76, "nodeType": "378", "messageId": "379", "suggestions": "449"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 275, "column": 42, "nodeType": "378", "messageId": "379", "suggestions": "450"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 298, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "451"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 298, "column": 54, "nodeType": "378", "messageId": "379", "suggestions": "452"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 299, "column": 60, "nodeType": "378", "messageId": "379", "suggestions": "453"}, {"ruleId": "376", "severity": 1, "message": "430", "line": 326, "column": 21, "nodeType": "378", "messageId": "379", "suggestions": "454"}, {"ruleId": "376", "severity": 1, "message": "430", "line": 326, "column": 43, "nodeType": "378", "messageId": "379", "suggestions": "455"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 331, "column": 23, "nodeType": "391", "endLine": 335, "endColumn": 25}, {"ruleId": "376", "severity": 1, "message": "377", "line": 378, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "456"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 382, "column": 24, "nodeType": "378", "messageId": "379", "suggestions": "457"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 170, "column": 17, "nodeType": "378", "messageId": "379", "suggestions": "458"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 398, "column": 16, "nodeType": "378", "messageId": "379", "suggestions": "459"}, {"ruleId": "372", "severity": 1, "message": "428", "line": 212, "column": 9, "nodeType": null, "messageId": "374", "endLine": 212, "endColumn": 19}, {"ruleId": "376", "severity": 1, "message": "377", "line": 258, "column": 63, "nodeType": "378", "messageId": "379", "suggestions": "460"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 353, "column": 21, "nodeType": "391", "endLine": 357, "endColumn": 23}, {"ruleId": "376", "severity": 1, "message": "377", "line": 458, "column": 63, "nodeType": "378", "messageId": "379", "suggestions": "461"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 20, "column": 38, "nodeType": "384", "messageId": "385", "endLine": 20, "endColumn": 41, "suggestions": "462"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 148, "column": 70, "nodeType": "378", "messageId": "379", "suggestions": "463"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 148, "column": 91, "nodeType": "378", "messageId": "379", "suggestions": "464"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 277, "column": 17, "nodeType": "391", "endLine": 281, "endColumn": 19}, {"ruleId": "376", "severity": 1, "message": "430", "line": 413, "column": 19, "nodeType": "378", "messageId": "379", "suggestions": "465"}, {"ruleId": "376", "severity": 1, "message": "430", "line": 413, "column": 49, "nodeType": "378", "messageId": "379", "suggestions": "466"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 416, "column": 19, "nodeType": "391", "endLine": 420, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "377", "line": 445, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "467"}, {"ruleId": "372", "severity": 1, "message": "394", "line": 14, "column": 3, "nodeType": null, "messageId": "374", "endLine": 14, "endColumn": 21}, {"ruleId": "382", "severity": 1, "message": "383", "line": 168, "column": 31, "nodeType": "384", "messageId": "385", "endLine": 168, "endColumn": 34, "suggestions": "468"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 210, "column": 76, "nodeType": "378", "messageId": "379", "suggestions": "469"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 380, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "470"}, {"ruleId": "372", "severity": 1, "message": "471", "line": 9, "column": 3, "nodeType": null, "messageId": "374", "endLine": 9, "endColumn": 12}, {"ruleId": "372", "severity": 1, "message": "472", "line": 10, "column": 3, "nodeType": null, "messageId": "374", "endLine": 10, "endColumn": 10}, {"ruleId": "372", "severity": 1, "message": "473", "line": 11, "column": 3, "nodeType": null, "messageId": "374", "endLine": 11, "endColumn": 18}, {"ruleId": "372", "severity": 1, "message": "411", "line": 12, "column": 3, "nodeType": null, "messageId": "374", "endLine": 12, "endColumn": 15}, {"ruleId": "372", "severity": 1, "message": "373", "line": 16, "column": 3, "nodeType": null, "messageId": "374", "endLine": 16, "endColumn": 16}, {"ruleId": "382", "severity": 1, "message": "383", "line": 24, "column": 38, "nodeType": "384", "messageId": "385", "endLine": 24, "endColumn": 41, "suggestions": "474"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 162, "column": 70, "nodeType": "378", "messageId": "379", "suggestions": "475"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 162, "column": 91, "nodeType": "378", "messageId": "379", "suggestions": "476"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 175, "column": 33, "nodeType": "384", "messageId": "385", "endLine": 175, "endColumn": 36, "suggestions": "477"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 349, "column": 46, "nodeType": "378", "messageId": "379", "suggestions": "478"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 390, "column": 43, "nodeType": "384", "messageId": "385", "endLine": 390, "endColumn": 46, "suggestions": "479"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 427, "column": 22, "nodeType": "378", "messageId": "379", "suggestions": "480"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 427, "column": 56, "nodeType": "378", "messageId": "379", "suggestions": "481"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 432, "column": 57, "nodeType": "384", "messageId": "385", "endLine": 432, "endColumn": 60, "suggestions": "482"}, {"ruleId": "376", "severity": 1, "message": "430", "line": 445, "column": 23, "nodeType": "378", "messageId": "379", "suggestions": "483"}, {"ruleId": "376", "severity": 1, "message": "430", "line": 445, "column": 45, "nodeType": "378", "messageId": "379", "suggestions": "484"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 472, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "485"}, {"ruleId": "372", "severity": 1, "message": "486", "line": 9, "column": 3, "nodeType": null, "messageId": "374", "endLine": 9, "endColumn": 18}, {"ruleId": "376", "severity": 1, "message": "377", "line": 207, "column": 17, "nodeType": "378", "messageId": "379", "suggestions": "487"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 434, "column": 15, "nodeType": "378", "messageId": "379", "suggestions": "488"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 435, "column": 35, "nodeType": "378", "messageId": "379", "suggestions": "489"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 435, "column": 66, "nodeType": "378", "messageId": "379", "suggestions": "490"}, {"ruleId": "372", "severity": 1, "message": "491", "line": 10, "column": 3, "nodeType": null, "messageId": "374", "endLine": 10, "endColumn": 24}, {"ruleId": "372", "severity": 1, "message": "492", "line": 358, "column": 23, "nodeType": null, "messageId": "374", "endLine": 358, "endColumn": 35}, {"ruleId": "389", "severity": 1, "message": "390", "line": 371, "column": 27, "nodeType": "391", "endLine": 375, "endColumn": 29}, {"ruleId": "389", "severity": 1, "message": "390", "line": 494, "column": 21, "nodeType": "391", "endLine": 498, "endColumn": 23}, {"ruleId": "376", "severity": 1, "message": "377", "line": 530, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "493"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 19, "column": 42, "nodeType": "384", "messageId": "385", "endLine": 19, "endColumn": 45, "suggestions": "494"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 155, "column": 73, "nodeType": "378", "messageId": "379", "suggestions": "495"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 155, "column": 94, "nodeType": "378", "messageId": "379", "suggestions": "496"}, {"ruleId": "389", "severity": 1, "message": "390", "line": 213, "column": 21, "nodeType": "391", "endLine": 217, "endColumn": 23}, {"ruleId": "389", "severity": 1, "message": "390", "line": 277, "column": 19, "nodeType": "391", "endLine": 281, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "377", "line": 427, "column": 29, "nodeType": "378", "messageId": "379", "suggestions": "497"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 432, "column": 52, "nodeType": "384", "messageId": "385", "endLine": 432, "endColumn": 55, "suggestions": "498"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 471, "column": 20, "nodeType": "378", "messageId": "379", "suggestions": "499"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 6, "column": 34, "nodeType": "384", "messageId": "385", "endLine": 6, "endColumn": 37, "suggestions": "500"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 13, "column": 40, "nodeType": "384", "messageId": "385", "endLine": 13, "endColumn": 43, "suggestions": "501"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 131, "column": 45, "nodeType": "384", "messageId": "385", "endLine": 131, "endColumn": 48, "suggestions": "502"}, {"ruleId": "382", "severity": 1, "message": "383", "line": 133, "column": 49, "nodeType": "384", "messageId": "385", "endLine": 133, "endColumn": 52, "suggestions": "503"}, "@typescript-eslint/no-unused-vars", "'UserGroupIcon' is defined but never used.", "unusedVar", "'TrophyIcon' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["504", "505", "506", "507"], "'XCircleIcon' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["508", "509"], "'showAddModal' is assigned a value but never used.", ["510", "511", "512", "513"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["514", "515"], "'showAddKnowledge' is assigned a value but never used.", "'CurrencyDollarIcon' is defined but never used.", "'BriefcaseIcon' is defined but never used.", "'UserIcon' is defined but never used.", "'CalendarIcon' is defined but never used.", ["516", "517"], "'ExclamationTriangleIcon' is defined but never used.", ["518", "519"], "'PlayIcon' is defined but never used.", "'editingSection' is assigned a value but never used.", ["520", "521"], ["522", "523"], ["524", "525"], "'handleStatusChange' is assigned a value but never used.", ["526", "527"], ["528", "529"], "'handleToggleFeatured' is assigned a value but never used.", ["530", "531"], "'ChartBarIcon' is defined but never used.", ["532", "533"], ["534", "535"], ["536", "537"], ["538", "539", "540", "541"], ["542", "543", "544", "545"], ["546", "547"], ["548", "549"], ["550", "551"], "'GlobeAltIcon' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "'KeyIcon' is defined but never used.", "'DocumentTextIcon' is defined but never used.", ["552", "553"], "'FunnelIcon' is defined but never used.", ["554", "555"], "'handleToggleActive' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", ["556", "557"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["558", "559", "560", "561"], ["562", "563", "564", "565"], ["566", "567", "568", "569"], ["570", "571", "572", "573"], ["574", "575"], ["576", "577"], "'startOfYear' is assigned a value but never used.", ["578", "579"], ["580", "581"], ["582", "583"], "'errorResponse' is defined but never used.", ["584", "585"], "'ClockIcon' is defined but never used.", "'error' is defined but never used.", ["586", "587", "588", "589"], ["590", "591", "592", "593"], ["594", "595", "596", "597"], ["598", "599", "600", "601"], ["602", "603", "604", "605"], ["606", "607", "608", "609"], ["610", "611", "612", "613"], ["614", "615", "616", "617"], ["618", "619", "620", "621"], ["622", "623", "624", "625"], ["626", "627", "628", "629"], ["630", "631", "632", "633"], ["634", "635", "636", "637"], ["638", "639", "640", "641"], ["642", "643", "644", "645"], ["646", "647", "648", "649"], ["650", "651", "652", "653"], ["654", "655"], ["656", "657", "658", "659"], ["660", "661", "662", "663"], ["664", "665", "666", "667"], ["668", "669", "670", "671"], ["672", "673", "674", "675"], ["676", "677"], ["678", "679", "680", "681"], ["682", "683", "684", "685"], "'CloudIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'ShieldCheckIcon' is defined but never used.", ["686", "687"], ["688", "689", "690", "691"], ["692", "693", "694", "695"], ["696", "697"], ["698", "699", "700", "701"], ["702", "703"], ["704", "705", "706", "707"], ["708", "709", "710", "711"], ["712", "713"], ["714", "715", "716", "717"], ["718", "719", "720", "721"], ["722", "723", "724", "725"], "'AcademicCapIcon' is defined but never used.", ["726", "727", "728", "729"], ["730", "731", "732", "733"], ["734", "735", "736", "737"], ["738", "739", "740", "741"], "'DevicePhoneMobileIcon' is defined but never used.", "'CategoryIcon' is assigned a value but never used.", ["742", "743", "744", "745"], ["746", "747"], ["748", "749", "750", "751"], ["752", "753", "754", "755"], ["756", "757", "758", "759"], ["760", "761"], ["762", "763", "764", "765"], ["766", "767"], ["768", "769"], ["770", "771"], ["772", "773"], {"messageId": "774", "data": "775", "fix": "776", "desc": "777"}, {"messageId": "774", "data": "778", "fix": "779", "desc": "780"}, {"messageId": "774", "data": "781", "fix": "782", "desc": "783"}, {"messageId": "774", "data": "784", "fix": "785", "desc": "786"}, {"messageId": "787", "fix": "788", "desc": "789"}, {"messageId": "790", "fix": "791", "desc": "792"}, {"messageId": "774", "data": "793", "fix": "794", "desc": "777"}, {"messageId": "774", "data": "795", "fix": "796", "desc": "780"}, {"messageId": "774", "data": "797", "fix": "798", "desc": "783"}, {"messageId": "774", "data": "799", "fix": "800", "desc": "786"}, {"messageId": "787", "fix": "801", "desc": "789"}, {"messageId": "790", "fix": "802", "desc": "792"}, {"messageId": "787", "fix": "803", "desc": "789"}, {"messageId": "790", "fix": "804", "desc": "792"}, {"messageId": "787", "fix": "805", "desc": "789"}, {"messageId": "790", "fix": "806", "desc": "792"}, {"messageId": "787", "fix": "807", "desc": "789"}, {"messageId": "790", "fix": "808", "desc": "792"}, {"messageId": "787", "fix": "809", "desc": "789"}, {"messageId": "790", "fix": "810", "desc": "792"}, {"messageId": "787", "fix": "811", "desc": "789"}, {"messageId": "790", "fix": "812", "desc": "792"}, {"messageId": "787", "fix": "813", "desc": "789"}, {"messageId": "790", "fix": "814", "desc": "792"}, {"messageId": "787", "fix": "815", "desc": "789"}, {"messageId": "790", "fix": "816", "desc": "792"}, {"messageId": "787", "fix": "817", "desc": "789"}, {"messageId": "790", "fix": "818", "desc": "792"}, {"messageId": "787", "fix": "819", "desc": "789"}, {"messageId": "790", "fix": "820", "desc": "792"}, {"messageId": "787", "fix": "821", "desc": "789"}, {"messageId": "790", "fix": "822", "desc": "792"}, {"messageId": "787", "fix": "823", "desc": "789"}, {"messageId": "790", "fix": "824", "desc": "792"}, {"messageId": "774", "data": "825", "fix": "826", "desc": "777"}, {"messageId": "774", "data": "827", "fix": "828", "desc": "780"}, {"messageId": "774", "data": "829", "fix": "830", "desc": "783"}, {"messageId": "774", "data": "831", "fix": "832", "desc": "786"}, {"messageId": "774", "data": "833", "fix": "834", "desc": "777"}, {"messageId": "774", "data": "835", "fix": "836", "desc": "780"}, {"messageId": "774", "data": "837", "fix": "838", "desc": "783"}, {"messageId": "774", "data": "839", "fix": "840", "desc": "786"}, {"messageId": "787", "fix": "841", "desc": "789"}, {"messageId": "790", "fix": "842", "desc": "792"}, {"messageId": "787", "fix": "843", "desc": "789"}, {"messageId": "790", "fix": "844", "desc": "792"}, {"messageId": "787", "fix": "845", "desc": "789"}, {"messageId": "790", "fix": "846", "desc": "792"}, {"messageId": "787", "fix": "847", "desc": "789"}, {"messageId": "790", "fix": "848", "desc": "792"}, {"messageId": "787", "fix": "849", "desc": "789"}, {"messageId": "790", "fix": "850", "desc": "792"}, {"messageId": "787", "fix": "851", "desc": "789"}, {"messageId": "790", "fix": "852", "desc": "792"}, {"messageId": "774", "data": "853", "fix": "854", "desc": "855"}, {"messageId": "774", "data": "856", "fix": "857", "desc": "858"}, {"messageId": "774", "data": "859", "fix": "860", "desc": "861"}, {"messageId": "774", "data": "862", "fix": "863", "desc": "864"}, {"messageId": "774", "data": "865", "fix": "866", "desc": "855"}, {"messageId": "774", "data": "867", "fix": "868", "desc": "858"}, {"messageId": "774", "data": "869", "fix": "870", "desc": "861"}, {"messageId": "774", "data": "871", "fix": "872", "desc": "864"}, {"messageId": "774", "data": "873", "fix": "874", "desc": "855"}, {"messageId": "774", "data": "875", "fix": "876", "desc": "858"}, {"messageId": "774", "data": "877", "fix": "878", "desc": "861"}, {"messageId": "774", "data": "879", "fix": "880", "desc": "864"}, {"messageId": "774", "data": "881", "fix": "882", "desc": "855"}, {"messageId": "774", "data": "883", "fix": "884", "desc": "858"}, {"messageId": "774", "data": "885", "fix": "886", "desc": "861"}, {"messageId": "774", "data": "887", "fix": "888", "desc": "864"}, {"messageId": "787", "fix": "889", "desc": "789"}, {"messageId": "790", "fix": "890", "desc": "792"}, {"messageId": "787", "fix": "891", "desc": "789"}, {"messageId": "790", "fix": "892", "desc": "792"}, {"messageId": "787", "fix": "893", "desc": "789"}, {"messageId": "790", "fix": "894", "desc": "792"}, {"messageId": "787", "fix": "895", "desc": "789"}, {"messageId": "790", "fix": "896", "desc": "792"}, {"messageId": "787", "fix": "897", "desc": "789"}, {"messageId": "790", "fix": "898", "desc": "792"}, {"messageId": "787", "fix": "899", "desc": "789"}, {"messageId": "790", "fix": "900", "desc": "792"}, {"messageId": "774", "data": "901", "fix": "902", "desc": "777"}, {"messageId": "774", "data": "903", "fix": "904", "desc": "780"}, {"messageId": "774", "data": "905", "fix": "906", "desc": "783"}, {"messageId": "774", "data": "907", "fix": "908", "desc": "786"}, {"messageId": "774", "data": "909", "fix": "910", "desc": "777"}, {"messageId": "774", "data": "911", "fix": "912", "desc": "780"}, {"messageId": "774", "data": "913", "fix": "914", "desc": "783"}, {"messageId": "774", "data": "915", "fix": "916", "desc": "786"}, {"messageId": "774", "data": "917", "fix": "918", "desc": "777"}, {"messageId": "774", "data": "919", "fix": "920", "desc": "780"}, {"messageId": "774", "data": "921", "fix": "922", "desc": "783"}, {"messageId": "774", "data": "923", "fix": "924", "desc": "786"}, {"messageId": "774", "data": "925", "fix": "926", "desc": "777"}, {"messageId": "774", "data": "927", "fix": "928", "desc": "780"}, {"messageId": "774", "data": "929", "fix": "930", "desc": "783"}, {"messageId": "774", "data": "931", "fix": "932", "desc": "786"}, {"messageId": "774", "data": "933", "fix": "934", "desc": "777"}, {"messageId": "774", "data": "935", "fix": "936", "desc": "780"}, {"messageId": "774", "data": "937", "fix": "938", "desc": "783"}, {"messageId": "774", "data": "939", "fix": "940", "desc": "786"}, {"messageId": "774", "data": "941", "fix": "942", "desc": "777"}, {"messageId": "774", "data": "943", "fix": "944", "desc": "780"}, {"messageId": "774", "data": "945", "fix": "946", "desc": "783"}, {"messageId": "774", "data": "947", "fix": "948", "desc": "786"}, {"messageId": "774", "data": "949", "fix": "950", "desc": "777"}, {"messageId": "774", "data": "951", "fix": "952", "desc": "780"}, {"messageId": "774", "data": "953", "fix": "954", "desc": "783"}, {"messageId": "774", "data": "955", "fix": "956", "desc": "786"}, {"messageId": "774", "data": "957", "fix": "958", "desc": "777"}, {"messageId": "774", "data": "959", "fix": "960", "desc": "780"}, {"messageId": "774", "data": "961", "fix": "962", "desc": "783"}, {"messageId": "774", "data": "963", "fix": "964", "desc": "786"}, {"messageId": "774", "data": "965", "fix": "966", "desc": "777"}, {"messageId": "774", "data": "967", "fix": "968", "desc": "780"}, {"messageId": "774", "data": "969", "fix": "970", "desc": "783"}, {"messageId": "774", "data": "971", "fix": "972", "desc": "786"}, {"messageId": "774", "data": "973", "fix": "974", "desc": "855"}, {"messageId": "774", "data": "975", "fix": "976", "desc": "858"}, {"messageId": "774", "data": "977", "fix": "978", "desc": "861"}, {"messageId": "774", "data": "979", "fix": "980", "desc": "864"}, {"messageId": "774", "data": "981", "fix": "982", "desc": "855"}, {"messageId": "774", "data": "983", "fix": "984", "desc": "858"}, {"messageId": "774", "data": "985", "fix": "986", "desc": "861"}, {"messageId": "774", "data": "987", "fix": "988", "desc": "864"}, {"messageId": "774", "data": "989", "fix": "990", "desc": "777"}, {"messageId": "774", "data": "991", "fix": "992", "desc": "780"}, {"messageId": "774", "data": "993", "fix": "994", "desc": "783"}, {"messageId": "774", "data": "995", "fix": "996", "desc": "786"}, {"messageId": "774", "data": "997", "fix": "998", "desc": "777"}, {"messageId": "774", "data": "999", "fix": "1000", "desc": "780"}, {"messageId": "774", "data": "1001", "fix": "1002", "desc": "783"}, {"messageId": "774", "data": "1003", "fix": "1004", "desc": "786"}, {"messageId": "774", "data": "1005", "fix": "1006", "desc": "777"}, {"messageId": "774", "data": "1007", "fix": "1008", "desc": "780"}, {"messageId": "774", "data": "1009", "fix": "1010", "desc": "783"}, {"messageId": "774", "data": "1011", "fix": "1012", "desc": "786"}, {"messageId": "774", "data": "1013", "fix": "1014", "desc": "777"}, {"messageId": "774", "data": "1015", "fix": "1016", "desc": "780"}, {"messageId": "774", "data": "1017", "fix": "1018", "desc": "783"}, {"messageId": "774", "data": "1019", "fix": "1020", "desc": "786"}, {"messageId": "774", "data": "1021", "fix": "1022", "desc": "777"}, {"messageId": "774", "data": "1023", "fix": "1024", "desc": "780"}, {"messageId": "774", "data": "1025", "fix": "1026", "desc": "783"}, {"messageId": "774", "data": "1027", "fix": "1028", "desc": "786"}, {"messageId": "774", "data": "1029", "fix": "1030", "desc": "777"}, {"messageId": "774", "data": "1031", "fix": "1032", "desc": "780"}, {"messageId": "774", "data": "1033", "fix": "1034", "desc": "783"}, {"messageId": "774", "data": "1035", "fix": "1036", "desc": "786"}, {"messageId": "787", "fix": "1037", "desc": "789"}, {"messageId": "790", "fix": "1038", "desc": "792"}, {"messageId": "774", "data": "1039", "fix": "1040", "desc": "777"}, {"messageId": "774", "data": "1041", "fix": "1042", "desc": "780"}, {"messageId": "774", "data": "1043", "fix": "1044", "desc": "783"}, {"messageId": "774", "data": "1045", "fix": "1046", "desc": "786"}, {"messageId": "774", "data": "1047", "fix": "1048", "desc": "777"}, {"messageId": "774", "data": "1049", "fix": "1050", "desc": "780"}, {"messageId": "774", "data": "1051", "fix": "1052", "desc": "783"}, {"messageId": "774", "data": "1053", "fix": "1054", "desc": "786"}, {"messageId": "774", "data": "1055", "fix": "1056", "desc": "855"}, {"messageId": "774", "data": "1057", "fix": "1058", "desc": "858"}, {"messageId": "774", "data": "1059", "fix": "1060", "desc": "861"}, {"messageId": "774", "data": "1061", "fix": "1062", "desc": "864"}, {"messageId": "774", "data": "1063", "fix": "1064", "desc": "855"}, {"messageId": "774", "data": "1065", "fix": "1066", "desc": "858"}, {"messageId": "774", "data": "1067", "fix": "1068", "desc": "861"}, {"messageId": "774", "data": "1069", "fix": "1070", "desc": "864"}, {"messageId": "774", "data": "1071", "fix": "1072", "desc": "777"}, {"messageId": "774", "data": "1073", "fix": "1074", "desc": "780"}, {"messageId": "774", "data": "1075", "fix": "1076", "desc": "783"}, {"messageId": "774", "data": "1077", "fix": "1078", "desc": "786"}, {"messageId": "787", "fix": "1079", "desc": "789"}, {"messageId": "790", "fix": "1080", "desc": "792"}, {"messageId": "774", "data": "1081", "fix": "1082", "desc": "777"}, {"messageId": "774", "data": "1083", "fix": "1084", "desc": "780"}, {"messageId": "774", "data": "1085", "fix": "1086", "desc": "783"}, {"messageId": "774", "data": "1087", "fix": "1088", "desc": "786"}, {"messageId": "774", "data": "1089", "fix": "1090", "desc": "777"}, {"messageId": "774", "data": "1091", "fix": "1092", "desc": "780"}, {"messageId": "774", "data": "1093", "fix": "1094", "desc": "783"}, {"messageId": "774", "data": "1095", "fix": "1096", "desc": "786"}, {"messageId": "787", "fix": "1097", "desc": "789"}, {"messageId": "790", "fix": "1098", "desc": "792"}, {"messageId": "774", "data": "1099", "fix": "1100", "desc": "777"}, {"messageId": "774", "data": "1101", "fix": "1102", "desc": "780"}, {"messageId": "774", "data": "1103", "fix": "1104", "desc": "783"}, {"messageId": "774", "data": "1105", "fix": "1106", "desc": "786"}, {"messageId": "774", "data": "1107", "fix": "1108", "desc": "777"}, {"messageId": "774", "data": "1109", "fix": "1110", "desc": "780"}, {"messageId": "774", "data": "1111", "fix": "1112", "desc": "783"}, {"messageId": "774", "data": "1113", "fix": "1114", "desc": "786"}, {"messageId": "787", "fix": "1115", "desc": "789"}, {"messageId": "790", "fix": "1116", "desc": "792"}, {"messageId": "774", "data": "1117", "fix": "1118", "desc": "777"}, {"messageId": "774", "data": "1119", "fix": "1120", "desc": "780"}, {"messageId": "774", "data": "1121", "fix": "1122", "desc": "783"}, {"messageId": "774", "data": "1123", "fix": "1124", "desc": "786"}, {"messageId": "787", "fix": "1125", "desc": "789"}, {"messageId": "790", "fix": "1126", "desc": "792"}, {"messageId": "774", "data": "1127", "fix": "1128", "desc": "777"}, {"messageId": "774", "data": "1129", "fix": "1130", "desc": "780"}, {"messageId": "774", "data": "1131", "fix": "1132", "desc": "783"}, {"messageId": "774", "data": "1133", "fix": "1134", "desc": "786"}, {"messageId": "774", "data": "1135", "fix": "1136", "desc": "777"}, {"messageId": "774", "data": "1137", "fix": "1138", "desc": "780"}, {"messageId": "774", "data": "1139", "fix": "1140", "desc": "783"}, {"messageId": "774", "data": "1141", "fix": "1142", "desc": "786"}, {"messageId": "787", "fix": "1143", "desc": "789"}, {"messageId": "790", "fix": "1144", "desc": "792"}, {"messageId": "774", "data": "1145", "fix": "1146", "desc": "855"}, {"messageId": "774", "data": "1147", "fix": "1148", "desc": "858"}, {"messageId": "774", "data": "1149", "fix": "1150", "desc": "861"}, {"messageId": "774", "data": "1151", "fix": "1152", "desc": "864"}, {"messageId": "774", "data": "1153", "fix": "1154", "desc": "855"}, {"messageId": "774", "data": "1155", "fix": "1156", "desc": "858"}, {"messageId": "774", "data": "1157", "fix": "1158", "desc": "861"}, {"messageId": "774", "data": "1159", "fix": "1160", "desc": "864"}, {"messageId": "774", "data": "1161", "fix": "1162", "desc": "777"}, {"messageId": "774", "data": "1163", "fix": "1164", "desc": "780"}, {"messageId": "774", "data": "1165", "fix": "1166", "desc": "783"}, {"messageId": "774", "data": "1167", "fix": "1168", "desc": "786"}, {"messageId": "774", "data": "1169", "fix": "1170", "desc": "777"}, {"messageId": "774", "data": "1171", "fix": "1172", "desc": "780"}, {"messageId": "774", "data": "1173", "fix": "1174", "desc": "783"}, {"messageId": "774", "data": "1175", "fix": "1176", "desc": "786"}, {"messageId": "774", "data": "1177", "fix": "1178", "desc": "777"}, {"messageId": "774", "data": "1179", "fix": "1180", "desc": "780"}, {"messageId": "774", "data": "1181", "fix": "1182", "desc": "783"}, {"messageId": "774", "data": "1183", "fix": "1184", "desc": "786"}, {"messageId": "774", "data": "1185", "fix": "1186", "desc": "777"}, {"messageId": "774", "data": "1187", "fix": "1188", "desc": "780"}, {"messageId": "774", "data": "1189", "fix": "1190", "desc": "783"}, {"messageId": "774", "data": "1191", "fix": "1192", "desc": "786"}, {"messageId": "774", "data": "1193", "fix": "1194", "desc": "777"}, {"messageId": "774", "data": "1195", "fix": "1196", "desc": "780"}, {"messageId": "774", "data": "1197", "fix": "1198", "desc": "783"}, {"messageId": "774", "data": "1199", "fix": "1200", "desc": "786"}, {"messageId": "774", "data": "1201", "fix": "1202", "desc": "777"}, {"messageId": "774", "data": "1203", "fix": "1204", "desc": "780"}, {"messageId": "774", "data": "1205", "fix": "1206", "desc": "783"}, {"messageId": "774", "data": "1207", "fix": "1208", "desc": "786"}, {"messageId": "787", "fix": "1209", "desc": "789"}, {"messageId": "790", "fix": "1210", "desc": "792"}, {"messageId": "774", "data": "1211", "fix": "1212", "desc": "777"}, {"messageId": "774", "data": "1213", "fix": "1214", "desc": "780"}, {"messageId": "774", "data": "1215", "fix": "1216", "desc": "783"}, {"messageId": "774", "data": "1217", "fix": "1218", "desc": "786"}, {"messageId": "774", "data": "1219", "fix": "1220", "desc": "777"}, {"messageId": "774", "data": "1221", "fix": "1222", "desc": "780"}, {"messageId": "774", "data": "1223", "fix": "1224", "desc": "783"}, {"messageId": "774", "data": "1225", "fix": "1226", "desc": "786"}, {"messageId": "774", "data": "1227", "fix": "1228", "desc": "777"}, {"messageId": "774", "data": "1229", "fix": "1230", "desc": "780"}, {"messageId": "774", "data": "1231", "fix": "1232", "desc": "783"}, {"messageId": "774", "data": "1233", "fix": "1234", "desc": "786"}, {"messageId": "787", "fix": "1235", "desc": "789"}, {"messageId": "790", "fix": "1236", "desc": "792"}, {"messageId": "774", "data": "1237", "fix": "1238", "desc": "777"}, {"messageId": "774", "data": "1239", "fix": "1240", "desc": "780"}, {"messageId": "774", "data": "1241", "fix": "1242", "desc": "783"}, {"messageId": "774", "data": "1243", "fix": "1244", "desc": "786"}, {"messageId": "787", "fix": "1245", "desc": "789"}, {"messageId": "790", "fix": "1246", "desc": "792"}, {"messageId": "787", "fix": "1247", "desc": "789"}, {"messageId": "790", "fix": "1248", "desc": "792"}, {"messageId": "787", "fix": "1249", "desc": "789"}, {"messageId": "790", "fix": "1250", "desc": "792"}, {"messageId": "787", "fix": "1251", "desc": "789"}, {"messageId": "790", "fix": "1252", "desc": "792"}, "replaceWithAlt", {"alt": "1253"}, {"range": "1254", "text": "1255"}, "Replace with `&apos;`.", {"alt": "1256"}, {"range": "1257", "text": "1258"}, "Replace with `&lsquo;`.", {"alt": "1259"}, {"range": "1260", "text": "1261"}, "Replace with `&#39;`.", {"alt": "1262"}, {"range": "1263", "text": "1264"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "1265", "text": "1266"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1267", "text": "1268"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "1253"}, {"range": "1269", "text": "1270"}, {"alt": "1256"}, {"range": "1271", "text": "1272"}, {"alt": "1259"}, {"range": "1273", "text": "1274"}, {"alt": "1262"}, {"range": "1275", "text": "1276"}, {"range": "1277", "text": "1266"}, {"range": "1278", "text": "1268"}, {"range": "1279", "text": "1266"}, {"range": "1280", "text": "1268"}, {"range": "1281", "text": "1266"}, {"range": "1282", "text": "1268"}, {"range": "1283", "text": "1266"}, {"range": "1284", "text": "1268"}, {"range": "1285", "text": "1266"}, {"range": "1286", "text": "1268"}, {"range": "1287", "text": "1266"}, {"range": "1288", "text": "1268"}, {"range": "1289", "text": "1266"}, {"range": "1290", "text": "1268"}, {"range": "1291", "text": "1266"}, {"range": "1292", "text": "1268"}, {"range": "1293", "text": "1266"}, {"range": "1294", "text": "1268"}, {"range": "1295", "text": "1266"}, {"range": "1296", "text": "1268"}, {"range": "1297", "text": "1266"}, {"range": "1298", "text": "1268"}, {"range": "1299", "text": "1266"}, {"range": "1300", "text": "1268"}, {"alt": "1253"}, {"range": "1301", "text": "1302"}, {"alt": "1256"}, {"range": "1303", "text": "1304"}, {"alt": "1259"}, {"range": "1305", "text": "1306"}, {"alt": "1262"}, {"range": "1307", "text": "1308"}, {"alt": "1253"}, {"range": "1309", "text": "1310"}, {"alt": "1256"}, {"range": "1311", "text": "1312"}, {"alt": "1259"}, {"range": "1313", "text": "1314"}, {"alt": "1262"}, {"range": "1315", "text": "1316"}, {"range": "1317", "text": "1266"}, {"range": "1318", "text": "1268"}, {"range": "1319", "text": "1266"}, {"range": "1320", "text": "1268"}, {"range": "1321", "text": "1266"}, {"range": "1322", "text": "1268"}, {"range": "1323", "text": "1266"}, {"range": "1324", "text": "1268"}, {"range": "1325", "text": "1266"}, {"range": "1326", "text": "1268"}, {"range": "1327", "text": "1266"}, {"range": "1328", "text": "1268"}, {"alt": "1329"}, {"range": "1330", "text": "1331"}, "Replace with `&quot;`.", {"alt": "1332"}, {"range": "1333", "text": "1334"}, "Replace with `&ldquo;`.", {"alt": "1335"}, {"range": "1336", "text": "1337"}, "Replace with `&#34;`.", {"alt": "1338"}, {"range": "1339", "text": "1340"}, "Replace with `&rdquo;`.", {"alt": "1329"}, {"range": "1341", "text": "1342"}, {"alt": "1332"}, {"range": "1343", "text": "1344"}, {"alt": "1335"}, {"range": "1345", "text": "1346"}, {"alt": "1338"}, {"range": "1347", "text": "1348"}, {"alt": "1329"}, {"range": "1349", "text": "1350"}, {"alt": "1332"}, {"range": "1351", "text": "1352"}, {"alt": "1335"}, {"range": "1353", "text": "1354"}, {"alt": "1338"}, {"range": "1355", "text": "1356"}, {"alt": "1329"}, {"range": "1357", "text": "1358"}, {"alt": "1332"}, {"range": "1359", "text": "1360"}, {"alt": "1335"}, {"range": "1361", "text": "1362"}, {"alt": "1338"}, {"range": "1363", "text": "1364"}, {"range": "1365", "text": "1266"}, {"range": "1366", "text": "1268"}, {"range": "1367", "text": "1266"}, {"range": "1368", "text": "1268"}, {"range": "1369", "text": "1266"}, {"range": "1370", "text": "1268"}, {"range": "1371", "text": "1266"}, {"range": "1372", "text": "1268"}, {"range": "1373", "text": "1266"}, {"range": "1374", "text": "1268"}, {"range": "1375", "text": "1266"}, {"range": "1376", "text": "1268"}, {"alt": "1253"}, {"range": "1377", "text": "1378"}, {"alt": "1256"}, {"range": "1379", "text": "1380"}, {"alt": "1259"}, {"range": "1381", "text": "1382"}, {"alt": "1262"}, {"range": "1383", "text": "1384"}, {"alt": "1253"}, {"range": "1385", "text": "1386"}, {"alt": "1256"}, {"range": "1387", "text": "1388"}, {"alt": "1259"}, {"range": "1389", "text": "1390"}, {"alt": "1262"}, {"range": "1391", "text": "1392"}, {"alt": "1253"}, {"range": "1393", "text": "1394"}, {"alt": "1256"}, {"range": "1395", "text": "1396"}, {"alt": "1259"}, {"range": "1397", "text": "1398"}, {"alt": "1262"}, {"range": "1399", "text": "1400"}, {"alt": "1253"}, {"range": "1401", "text": "1402"}, {"alt": "1256"}, {"range": "1403", "text": "1404"}, {"alt": "1259"}, {"range": "1405", "text": "1406"}, {"alt": "1262"}, {"range": "1407", "text": "1408"}, {"alt": "1253"}, {"range": "1409", "text": "1410"}, {"alt": "1256"}, {"range": "1411", "text": "1412"}, {"alt": "1259"}, {"range": "1413", "text": "1414"}, {"alt": "1262"}, {"range": "1415", "text": "1416"}, {"alt": "1253"}, {"range": "1417", "text": "1418"}, {"alt": "1256"}, {"range": "1419", "text": "1420"}, {"alt": "1259"}, {"range": "1421", "text": "1422"}, {"alt": "1262"}, {"range": "1423", "text": "1424"}, {"alt": "1253"}, {"range": "1425", "text": "1426"}, {"alt": "1256"}, {"range": "1427", "text": "1428"}, {"alt": "1259"}, {"range": "1429", "text": "1430"}, {"alt": "1262"}, {"range": "1431", "text": "1432"}, {"alt": "1253"}, {"range": "1433", "text": "1434"}, {"alt": "1256"}, {"range": "1435", "text": "1436"}, {"alt": "1259"}, {"range": "1437", "text": "1438"}, {"alt": "1262"}, {"range": "1439", "text": "1440"}, {"alt": "1253"}, {"range": "1441", "text": "1442"}, {"alt": "1256"}, {"range": "1443", "text": "1444"}, {"alt": "1259"}, {"range": "1445", "text": "1446"}, {"alt": "1262"}, {"range": "1447", "text": "1448"}, {"alt": "1329"}, {"range": "1449", "text": "1450"}, {"alt": "1332"}, {"range": "1451", "text": "1452"}, {"alt": "1335"}, {"range": "1453", "text": "1454"}, {"alt": "1338"}, {"range": "1455", "text": "1456"}, {"alt": "1329"}, {"range": "1457", "text": "1458"}, {"alt": "1332"}, {"range": "1459", "text": "1460"}, {"alt": "1335"}, {"range": "1461", "text": "1462"}, {"alt": "1338"}, {"range": "1463", "text": "1464"}, {"alt": "1253"}, {"range": "1465", "text": "1466"}, {"alt": "1256"}, {"range": "1467", "text": "1468"}, {"alt": "1259"}, {"range": "1469", "text": "1470"}, {"alt": "1262"}, {"range": "1471", "text": "1472"}, {"alt": "1253"}, {"range": "1473", "text": "1474"}, {"alt": "1256"}, {"range": "1475", "text": "1476"}, {"alt": "1259"}, {"range": "1477", "text": "1478"}, {"alt": "1262"}, {"range": "1479", "text": "1480"}, {"alt": "1253"}, {"range": "1481", "text": "1482"}, {"alt": "1256"}, {"range": "1483", "text": "1484"}, {"alt": "1259"}, {"range": "1485", "text": "1486"}, {"alt": "1262"}, {"range": "1487", "text": "1488"}, {"alt": "1253"}, {"range": "1489", "text": "1490"}, {"alt": "1256"}, {"range": "1491", "text": "1492"}, {"alt": "1259"}, {"range": "1493", "text": "1494"}, {"alt": "1262"}, {"range": "1495", "text": "1496"}, {"alt": "1253"}, {"range": "1497", "text": "1498"}, {"alt": "1256"}, {"range": "1499", "text": "1500"}, {"alt": "1259"}, {"range": "1501", "text": "1502"}, {"alt": "1262"}, {"range": "1503", "text": "1504"}, {"alt": "1253"}, {"range": "1505", "text": "1506"}, {"alt": "1256"}, {"range": "1507", "text": "1508"}, {"alt": "1259"}, {"range": "1509", "text": "1510"}, {"alt": "1262"}, {"range": "1511", "text": "1512"}, {"range": "1513", "text": "1266"}, {"range": "1514", "text": "1268"}, {"alt": "1253"}, {"range": "1515", "text": "1516"}, {"alt": "1256"}, {"range": "1517", "text": "1518"}, {"alt": "1259"}, {"range": "1519", "text": "1520"}, {"alt": "1262"}, {"range": "1521", "text": "1522"}, {"alt": "1253"}, {"range": "1523", "text": "1524"}, {"alt": "1256"}, {"range": "1525", "text": "1526"}, {"alt": "1259"}, {"range": "1527", "text": "1528"}, {"alt": "1262"}, {"range": "1529", "text": "1530"}, {"alt": "1329"}, {"range": "1531", "text": "1331"}, {"alt": "1332"}, {"range": "1532", "text": "1334"}, {"alt": "1335"}, {"range": "1533", "text": "1337"}, {"alt": "1338"}, {"range": "1534", "text": "1340"}, {"alt": "1329"}, {"range": "1535", "text": "1342"}, {"alt": "1332"}, {"range": "1536", "text": "1344"}, {"alt": "1335"}, {"range": "1537", "text": "1346"}, {"alt": "1338"}, {"range": "1538", "text": "1348"}, {"alt": "1253"}, {"range": "1539", "text": "1540"}, {"alt": "1256"}, {"range": "1541", "text": "1542"}, {"alt": "1259"}, {"range": "1543", "text": "1544"}, {"alt": "1262"}, {"range": "1545", "text": "1546"}, {"range": "1547", "text": "1266"}, {"range": "1548", "text": "1268"}, {"alt": "1253"}, {"range": "1549", "text": "1550"}, {"alt": "1256"}, {"range": "1551", "text": "1552"}, {"alt": "1259"}, {"range": "1553", "text": "1554"}, {"alt": "1262"}, {"range": "1555", "text": "1556"}, {"alt": "1253"}, {"range": "1557", "text": "1558"}, {"alt": "1256"}, {"range": "1559", "text": "1560"}, {"alt": "1259"}, {"range": "1561", "text": "1562"}, {"alt": "1262"}, {"range": "1563", "text": "1564"}, {"range": "1565", "text": "1266"}, {"range": "1566", "text": "1268"}, {"alt": "1253"}, {"range": "1567", "text": "1568"}, {"alt": "1256"}, {"range": "1569", "text": "1570"}, {"alt": "1259"}, {"range": "1571", "text": "1572"}, {"alt": "1262"}, {"range": "1573", "text": "1574"}, {"alt": "1253"}, {"range": "1575", "text": "1576"}, {"alt": "1256"}, {"range": "1577", "text": "1578"}, {"alt": "1259"}, {"range": "1579", "text": "1580"}, {"alt": "1262"}, {"range": "1581", "text": "1582"}, {"range": "1583", "text": "1266"}, {"range": "1584", "text": "1268"}, {"alt": "1253"}, {"range": "1585", "text": "1586"}, {"alt": "1256"}, {"range": "1587", "text": "1588"}, {"alt": "1259"}, {"range": "1589", "text": "1590"}, {"alt": "1262"}, {"range": "1591", "text": "1592"}, {"range": "1593", "text": "1266"}, {"range": "1594", "text": "1268"}, {"alt": "1253"}, {"range": "1595", "text": "1596"}, {"alt": "1256"}, {"range": "1597", "text": "1598"}, {"alt": "1259"}, {"range": "1599", "text": "1600"}, {"alt": "1262"}, {"range": "1601", "text": "1602"}, {"alt": "1253"}, {"range": "1603", "text": "1604"}, {"alt": "1256"}, {"range": "1605", "text": "1606"}, {"alt": "1259"}, {"range": "1607", "text": "1608"}, {"alt": "1262"}, {"range": "1609", "text": "1610"}, {"range": "1611", "text": "1266"}, {"range": "1612", "text": "1268"}, {"alt": "1329"}, {"range": "1613", "text": "1614"}, {"alt": "1332"}, {"range": "1615", "text": "1616"}, {"alt": "1335"}, {"range": "1617", "text": "1618"}, {"alt": "1338"}, {"range": "1619", "text": "1620"}, {"alt": "1329"}, {"range": "1621", "text": "1622"}, {"alt": "1332"}, {"range": "1623", "text": "1624"}, {"alt": "1335"}, {"range": "1625", "text": "1626"}, {"alt": "1338"}, {"range": "1627", "text": "1628"}, {"alt": "1253"}, {"range": "1629", "text": "1540"}, {"alt": "1256"}, {"range": "1630", "text": "1542"}, {"alt": "1259"}, {"range": "1631", "text": "1544"}, {"alt": "1262"}, {"range": "1632", "text": "1546"}, {"alt": "1253"}, {"range": "1633", "text": "1634"}, {"alt": "1256"}, {"range": "1635", "text": "1636"}, {"alt": "1259"}, {"range": "1637", "text": "1638"}, {"alt": "1262"}, {"range": "1639", "text": "1640"}, {"alt": "1253"}, {"range": "1641", "text": "1642"}, {"alt": "1256"}, {"range": "1643", "text": "1644"}, {"alt": "1259"}, {"range": "1645", "text": "1646"}, {"alt": "1262"}, {"range": "1647", "text": "1648"}, {"alt": "1253"}, {"range": "1649", "text": "1650"}, {"alt": "1256"}, {"range": "1651", "text": "1652"}, {"alt": "1259"}, {"range": "1653", "text": "1654"}, {"alt": "1262"}, {"range": "1655", "text": "1656"}, {"alt": "1253"}, {"range": "1657", "text": "1658"}, {"alt": "1256"}, {"range": "1659", "text": "1660"}, {"alt": "1259"}, {"range": "1661", "text": "1662"}, {"alt": "1262"}, {"range": "1663", "text": "1664"}, {"alt": "1253"}, {"range": "1665", "text": "1666"}, {"alt": "1256"}, {"range": "1667", "text": "1668"}, {"alt": "1259"}, {"range": "1669", "text": "1670"}, {"alt": "1262"}, {"range": "1671", "text": "1672"}, {"range": "1673", "text": "1266"}, {"range": "1674", "text": "1268"}, {"alt": "1253"}, {"range": "1675", "text": "1676"}, {"alt": "1256"}, {"range": "1677", "text": "1678"}, {"alt": "1259"}, {"range": "1679", "text": "1680"}, {"alt": "1262"}, {"range": "1681", "text": "1682"}, {"alt": "1253"}, {"range": "1683", "text": "1684"}, {"alt": "1256"}, {"range": "1685", "text": "1686"}, {"alt": "1259"}, {"range": "1687", "text": "1688"}, {"alt": "1262"}, {"range": "1689", "text": "1690"}, {"alt": "1253"}, {"range": "1691", "text": "1692"}, {"alt": "1256"}, {"range": "1693", "text": "1694"}, {"alt": "1259"}, {"range": "1695", "text": "1696"}, {"alt": "1262"}, {"range": "1697", "text": "1698"}, {"range": "1699", "text": "1266"}, {"range": "1700", "text": "1268"}, {"alt": "1253"}, {"range": "1701", "text": "1702"}, {"alt": "1256"}, {"range": "1703", "text": "1704"}, {"alt": "1259"}, {"range": "1705", "text": "1706"}, {"alt": "1262"}, {"range": "1707", "text": "1708"}, {"range": "1709", "text": "1266"}, {"range": "1710", "text": "1268"}, {"range": "1711", "text": "1266"}, {"range": "1712", "text": "1268"}, {"range": "1713", "text": "1266"}, {"range": "1714", "text": "1268"}, {"range": "1715", "text": "1266"}, {"range": "1716", "text": "1268"}, "&apos;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&apos;s build something amazing together.\n              ", "&lsquo;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&lsquo;s build something amazing together.\n              ", "&#39;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&#39;s build something amazing together.\n              ", "&rsquo;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&rsquo;s build something amazing together.\n              ", [3978, 3981], "unknown", [3978, 3981], "never", [5375, 5481], "\n              Manage your company&apos;s about pages, team information, and process documentation\n            ", [5375, 5481], "\n              Manage your company&lsquo;s about pages, team information, and process documentation\n            ", [5375, 5481], "\n              Manage your company&#39;s about pages, team information, and process documentation\n            ", [5375, 5481], "\n              Manage your company&rsquo;s about pages, team information, and process documentation\n            ", [2937, 2940], [2937, 2940], [3905, 3908], [3905, 3908], [2814, 2817], [2814, 2817], [2587, 2590], [2587, 2590], [2654, 2657], [2654, 2657], [4957, 4960], [4957, 4960], [22338, 22341], [22338, 22341], [6761, 6764], [6761, 6764], [8090, 8093], [8090, 8093], [7350, 7353], [7350, 7353], [1580, 1583], [1580, 1583], [3038, 3041], [3038, 3041], [4827, 4920], "\n              Welcome back! Here&apos;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here&lsquo;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here&#39;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here&rsquo;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&apos;s happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&lsquo;s happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&#39;s happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&rsquo;s happening with your business today.\n              ", [4128, 4131], [4128, 4131], [4472, 4475], [4472, 4475], [5328, 5331], [5328, 5331], [2076, 2079], [2076, 2079], [6359, 6362], [6359, 6362], [4129, 4132], [4129, 4132], "&quot;", [18329, 18349], "\n                  &quot;", "&ldquo;", [18329, 18349], "\n                  &ldquo;", "&#34;", [18329, 18349], "\n                  &#34;", "&rdquo;", [18329, 18349], "\n                  &rdquo;", [18370, 18388], "&quot;\n                ", [18370, 18388], "&ldquo;\n                ", [18370, 18388], "&#34;\n                ", [18370, 18388], "&rdquo;\n                ", [21932, 21962], "\n                            &quot;", [21932, 21962], "\n                            &ldquo;", [21932, 21962], "\n                            &#34;", [21932, 21962], "\n                            &rdquo;", [21991, 22019], "&quot;\n                          ", [21991, 22019], "&ldquo;\n                          ", [21991, 22019], "&#34;\n                          ", [21991, 22019], "&rdquo;\n                          ", [3290, 3293], [3290, 3293], [703, 706], [703, 706], [707, 710], [707, 710], [7600, 7603], [7600, 7603], [707, 710], [707, 710], [4987, 4990], [4987, 4990], [3354, 3393], "\n                Let&apos;s Build Something ", [3354, 3393], "\n                Let&lsquo;s Build Something ", [3354, 3393], "\n                Let&#39;s Build Something ", [3354, 3393], "\n                Let&rsquo;s Build Something ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&apos;s discuss how we can help bring your vision to life.\n              ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&lsquo;s discuss how we can help bring your vision to life.\n              ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&#39;s discuss how we can help bring your vision to life.\n              ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&rsquo;s discuss how we can help bring your vision to life.\n              ", [6402, 6529], "\n                  Fill out the form below and we&apos;ll get back to you within 24 hours with a detailed proposal.\n                ", [6402, 6529], "\n                  Fill out the form below and we&lsquo;ll get back to you within 24 hours with a detailed proposal.\n                ", [6402, 6529], "\n                  Fill out the form below and we&#39;ll get back to you within 24 hours with a detailed proposal.\n                ", [6402, 6529], "\n                  Fill out the form below and we&rsquo;ll get back to you within 24 hours with a detailed proposal.\n                ", [7502, 7616], "\n                        Thank you for reaching out. We&apos;ll get back to you within 24 hours.\n                      ", [7502, 7616], "\n                        Thank you for reaching out. We&lsquo;ll get back to you within 24 hours.\n                      ", [7502, 7616], "\n                        Thank you for reaching out. We&#39;ll get back to you within 24 hours.\n                      ", [7502, 7616], "\n                        Thank you for reaching out. We&rsquo;ll get back to you within 24 hours.\n                      ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&apos;ve got you covered.\r\n              ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&lsquo;ve got you covered.\r\n              ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&#39;ve got you covered.\r\n              ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&rsquo;ve got you covered.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&apos;d love to discuss your unique requirements.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&lsquo;d love to discuss your unique requirements.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&#39;d love to discuss your unique requirements.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&rsquo;d love to discuss your unique requirements.\r\n              ", [12874, 13065], "\r\n                Don&apos;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don&lsquo;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don&#39;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don&rsquo;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&apos;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&lsquo;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&#39;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&rsquo;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&apos;ve achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&lsquo;ve achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&#39;ve achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&rsquo;ve achieved together.\r\n              ", [14510, 14533], "\r\n                    &quot;", [14510, 14533], "\r\n                    &ldquo;", [14510, 14533], "\r\n                    &#34;", [14510, 14533], "\r\n                    &rdquo;", [14554, 14575], "&quot;\r\n                  ", [14554, 14575], "&ldquo;\r\n                  ", [14554, 14575], "&#34;\r\n                  ", [14554, 14575], "&rdquo;\r\n                  ", [16544, 16584], "\r\n                Let&apos;s Build Something ", [16544, 16584], "\r\n                Let&lsquo;s Build Something ", [16544, 16584], "\r\n                Let&#39;s Build Something ", [16544, 16584], "\r\n                Let&rsquo;s Build Something ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&apos;s discuss how we can help your business grow.\r\n              ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&lsquo;s discuss how we can help your business grow.\r\n              ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&#39;s discuss how we can help your business grow.\r\n              ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&rsquo;s discuss how we can help your business grow.\r\n              ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&apos;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&lsquo;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&#39;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&rsquo;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [17318, 17459], "\n            Let&apos;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [17318, 17459], "\n            Let&lsquo;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [17318, 17459], "\n            Let&#39;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [17318, 17459], "\n            Let&rsquo;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [9341, 9511], "\n                Explore our successful projects and see how we&apos;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [9341, 9511], "\n                Explore our successful projects and see how we&lsquo;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [9341, 9511], "\n                Explore our successful projects and see how we&#39;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [9341, 9511], "\n                Explore our successful projects and see how we&rsquo;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&apos;s discuss your requirements\n                and create something amazing together.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&lsquo;s discuss your requirements\n                and create something amazing together.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&#39;s discuss your requirements\n                and create something amazing together.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&rsquo;s discuss your requirements\n                and create something amazing together.\n              ", [516, 519], [516, 519], [6830, 6875], "The project you&apos;re looking for doesn't exist.", [6830, 6875], "The project you&lsquo;re looking for doesn't exist.", [6830, 6875], "The project you&#39;re looking for doesn't exist.", [6830, 6875], "The project you&rsquo;re looking for doesn't exist.", [6830, 6875], "The project you're looking for doesn&apos;t exist.", [6830, 6875], "The project you're looking for doesn&lsquo;t exist.", [6830, 6875], "The project you're looking for doesn&#39;t exist.", [6830, 6875], "The project you're looking for doesn&rsquo;t exist.", [17857, 17877], [17857, 17877], [17857, 17877], [17857, 17877], [17906, 17924], [17906, 17924], [17906, 17924], [17906, 17924], [19185, 19312], "\n                Let&apos;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [19185, 19312], "\n                Let&lsquo;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [19185, 19312], "\n                Let&#39;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [19185, 19312], "\n                Let&rsquo;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [5914, 5917], [5914, 5917], [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&apos;ve got you covered.\n              ", [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&lsquo;ve got you covered.\n              ", [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&#39;ve got you covered.\n              ", [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&rsquo;ve got you covered.\n              ", [15122, 15273], "\n                Let&apos;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [15122, 15273], "\n                Let&lsquo;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [15122, 15273], "\n                Let&#39;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [15122, 15273], "\n                Let&rsquo;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [572, 575], [572, 575], [5666, 5711], "The service you&apos;re looking for doesn't exist.", [5666, 5711], "The service you&lsquo;re looking for doesn't exist.", [5666, 5711], "The service you&#39;re looking for doesn't exist.", [5666, 5711], "The service you&rsquo;re looking for doesn't exist.", [5666, 5711], "The service you're looking for doesn&apos;t exist.", [5666, 5711], "The service you're looking for doesn&lsquo;t exist.", [5666, 5711], "The service you're looking for doesn&#39;t exist.", [5666, 5711], "The service you're looking for doesn&rsquo;t exist.", [5975, 5978], [5975, 5978], [13044, 13118], "\n                Here are the key benefits you&apos;ll get when you choose our ", [13044, 13118], "\n                Here are the key benefits you&lsquo;ll get when you choose our ", [13044, 13118], "\n                Here are the key benefits you&#39;ll get when you choose our ", [13044, 13118], "\n                Here are the key benefits you&rsquo;ll get when you choose our ", [14914, 14917], [14914, 14917], [16476, 16574], "\n                  Don&apos;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don&lsquo;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don&#39;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don&rsquo;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&apos;s what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&lsquo;s what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&#39;s what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&rsquo;s what our clients have to say about our ", [16789, 16792], [16789, 16792], [17409, 17433], "\n                      &quot;", [17409, 17433], "\n                      &ldquo;", [17409, 17433], "\n                      &#34;", [17409, 17433], "\n                      &rdquo;", [17454, 17476], "&quot;\n                    ", [17454, 17476], "&ldquo;\n                    ", [17454, 17476], "&#34;\n                    ", [17454, 17476], "&rdquo;\n                    ", [18461, 18588], [18461, 18588], [18461, 18588], [18461, 18588], [8428, 8621], "\n              We&apos;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [8428, 8621], "\n              We&lsquo;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [8428, 8621], "\n              We&#39;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [8428, 8621], "\n              We&rsquo;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [20418, 20616], "\n            We&apos;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We&lsquo;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We&#39;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We&rsquo;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&apos;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&lsquo;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&#39;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&rsquo;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&apos;d love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&lsquo;d love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&#39;d love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&rsquo;d love to hear from you.\n          ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&apos;s discuss your requirements and find the best solution.\n              ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&lsquo;s discuss your requirements and find the best solution.\n              ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&#39;s discuss your requirements and find the best solution.\n              ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&rsquo;s discuss your requirements and find the best solution.\n              ", [499, 502], [499, 502], [5129, 5177], "The technology you&apos;re looking for doesn't exist.", [5129, 5177], "The technology you&lsquo;re looking for doesn't exist.", [5129, 5177], "The technology you&#39;re looking for doesn't exist.", [5129, 5177], "The technology you&rsquo;re looking for doesn't exist.", [5129, 5177], "The technology you're looking for doesn&apos;t exist.", [5129, 5177], "The technology you're looking for doesn&lsquo;t exist.", [5129, 5177], "The technology you're looking for doesn&#39;t exist.", [5129, 5177], "The technology you're looking for doesn&rsquo;t exist.", [16685, 16743], "\n                  See how we&apos;ve successfully implemented ", [16685, 16743], "\n                  See how we&lsquo;ve successfully implemented ", [16685, 16743], "\n                  See how we&#39;ve successfully implemented ", [16685, 16743], "\n                  See how we&rsquo;ve successfully implemented ", [16956, 16959], [16956, 16959], [18599, 18634], "\n                Let&apos;s discuss how ", [18599, 18634], "\n                Let&lsquo;s discuss how ", [18599, 18634], "\n                Let&#39;s discuss how ", [18599, 18634], "\n                Let&rsquo;s discuss how ", [194, 197], [194, 197], [309, 312], [309, 312], [3092, 3095], [3092, 3095], [3174, 3177], [3174, 3177]]