[{"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\about-pages\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\blog\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\chatbot\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\clients\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\contact-forms\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\data-upload\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\hero-sections\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\invoices\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\jobs\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx": "11", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\legal-pages\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\projects\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\services\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\settings\\page.tsx": "16", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\team-members\\page.tsx": "17", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\technologies\\page.tsx": "18", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\testimonials\\page.tsx": "19", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\users\\page.tsx": "20", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\clients\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\dashboard\\stats\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\[id]\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\[id]\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\blog\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\contact\\page.tsx": "28", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx": "29", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\portfolio\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\[slug]\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\[slug]\\page.tsx": "35", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\team\\page.tsx": "36", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\[slug]\\page.tsx": "38", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\footer.tsx": "39", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\header.tsx": "40", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\api-utils.ts": "41", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\prisma.ts": "42", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\validations.ts": "43", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "44", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\blog\\route.ts": "45", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\blog\\[id]\\route.ts": "46", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\contact\\route.ts": "47", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\team\\route.ts": "48", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\technologies\\route.ts": "49", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\upload\\route.ts": "50", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\auth\\signin\\page.tsx": "51", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\providers\\auth-provider.tsx": "52", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\ui\\file-upload.tsx": "53", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\analytics.ts": "54", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\auth.ts": "55", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\branding.ts": "56", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\email.ts": "57", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\file-upload.ts": "58", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\monitoring.ts": "59", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\types\\next-auth.d.ts": "60", "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\__tests__\\api\\services.test.ts": "61"}, {"size": 13657, "mtime": 1749671010969, "results": "62", "hashOfConfig": "63"}, {"size": 17327, "mtime": 1749667378423, "results": "64", "hashOfConfig": "63"}, {"size": 17128, "mtime": 1749663650370, "results": "65", "hashOfConfig": "63"}, {"size": 26363, "mtime": 1749668075115, "results": "66", "hashOfConfig": "63"}, {"size": 16903, "mtime": 1749663720740, "results": "67", "hashOfConfig": "63"}, {"size": 20417, "mtime": 1749663877267, "results": "68", "hashOfConfig": "63"}, {"size": 22039, "mtime": 1749667991007, "results": "69", "hashOfConfig": "63"}, {"size": 15425, "mtime": 1749667317630, "results": "70", "hashOfConfig": "63"}, {"size": 25277, "mtime": 1749667909047, "results": "71", "hashOfConfig": "63"}, {"size": 26814, "mtime": 1749667819685, "results": "72", "hashOfConfig": "63"}, {"size": 11656, "mtime": 1749663410254, "results": "73", "hashOfConfig": "63"}, {"size": 23861, "mtime": 1749667720524, "results": "74", "hashOfConfig": "63"}, {"size": 13167, "mtime": 1749663463735, "results": "75", "hashOfConfig": "63"}, {"size": 16438, "mtime": 1749663582524, "results": "76", "hashOfConfig": "63"}, {"size": 22201, "mtime": 1749667451813, "results": "77", "hashOfConfig": "63"}, {"size": 17124, "mtime": 1749663781811, "results": "78", "hashOfConfig": "63"}, {"size": 14019, "mtime": 1749663520985, "results": "79", "hashOfConfig": "63"}, {"size": 26198, "mtime": 1749667537222, "results": "80", "hashOfConfig": "63"}, {"size": 24731, "mtime": 1749667624873, "results": "81", "hashOfConfig": "63"}, {"size": 24497, "mtime": 1749668155062, "results": "82", "hashOfConfig": "63"}, {"size": 5960, "mtime": 1749675735244, "results": "83", "hashOfConfig": "63"}, {"size": 5504, "mtime": 1749676202151, "results": "84", "hashOfConfig": "63"}, {"size": 5615, "mtime": 1749675675766, "results": "85", "hashOfConfig": "63"}, {"size": 8255, "mtime": 1749675706965, "results": "86", "hashOfConfig": "63"}, {"size": 4257, "mtime": 1749675626380, "results": "87", "hashOfConfig": "63"}, {"size": 5535, "mtime": 1749675649625, "results": "88", "hashOfConfig": "63"}, {"size": 12502, "mtime": 1749620380724, "results": "89", "hashOfConfig": "63"}, {"size": 22471, "mtime": 1749671507346, "results": "90", "hashOfConfig": "63"}, {"size": 1640, "mtime": 1749619132991, "results": "91", "hashOfConfig": "63"}, {"size": 22774, "mtime": 1749620619309, "results": "92", "hashOfConfig": "63"}, {"size": 17645, "mtime": 1749620425563, "results": "93", "hashOfConfig": "63"}, {"size": 19574, "mtime": 1749671208197, "results": "94", "hashOfConfig": "63"}, {"size": 20166, "mtime": 1749671283514, "results": "95", "hashOfConfig": "63"}, {"size": 16128, "mtime": 1749671069182, "results": "96", "hashOfConfig": "63"}, {"size": 19440, "mtime": 1749671137251, "results": "97", "hashOfConfig": "63"}, {"size": 20803, "mtime": 1749620525864, "results": "98", "hashOfConfig": "63"}, {"size": 23204, "mtime": 1749671363990, "results": "99", "hashOfConfig": "63"}, {"size": 19654, "mtime": 1749671433776, "results": "100", "hashOfConfig": "63"}, {"size": 2782, "mtime": 1749620348135, "results": "101", "hashOfConfig": "63"}, {"size": 5642, "mtime": 1749671671854, "results": "102", "hashOfConfig": "63"}, {"size": 6471, "mtime": 1749676878395, "results": "103", "hashOfConfig": "63"}, {"size": 279, "mtime": 1749675546287, "results": "104", "hashOfConfig": "63"}, {"size": 8128, "mtime": 1749675578619, "results": "105", "hashOfConfig": "63"}, {"size": 157, "mtime": 1749676833087, "results": "106", "hashOfConfig": "63"}, {"size": 4221, "mtime": 1749676412180, "results": "107", "hashOfConfig": "63"}, {"size": 4884, "mtime": 1749676435635, "results": "108", "hashOfConfig": "63"}, {"size": 3975, "mtime": 1749676499223, "results": "109", "hashOfConfig": "63"}, {"size": 4878, "mtime": 1749676459248, "results": "110", "hashOfConfig": "63"}, {"size": 4608, "mtime": 1749676479574, "results": "111", "hashOfConfig": "63"}, {"size": 1815, "mtime": 1749676931820, "results": "112", "hashOfConfig": "63"}, {"size": 4839, "mtime": 1749746875162, "results": "113", "hashOfConfig": "63"}, {"size": 275, "mtime": 1749676890338, "results": "114", "hashOfConfig": "63"}, {"size": 5681, "mtime": 1749676955816, "results": "115", "hashOfConfig": "63"}, {"size": 6085, "mtime": 1749746929609, "results": "116", "hashOfConfig": "63"}, {"size": 3578, "mtime": 1749747005073, "results": "117", "hashOfConfig": "63"}, {"size": 6569, "mtime": 1749745938513, "results": "118", "hashOfConfig": "63"}, {"size": 9273, "mtime": 1749745977931, "results": "119", "hashOfConfig": "63"}, {"size": 5378, "mtime": 1749676915988, "results": "120", "hashOfConfig": "63"}, {"size": 9603, "mtime": 1749746083782, "results": "121", "hashOfConfig": "63"}, {"size": 420, "mtime": 1749746865930, "results": "122", "hashOfConfig": "63"}, {"size": 5512, "mtime": 1749746948151, "results": "123", "hashOfConfig": "63"}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "12i8zce", {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\about\\page.tsx", ["307", "308", "309"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\about-pages\\page.tsx", ["310", "311", "312", "313"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\blog\\page.tsx", ["314"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\chatbot\\page.tsx", ["315", "316"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\clients\\page.tsx", ["317", "318", "319"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\contact-forms\\page.tsx", ["320", "321", "322"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\data-upload\\page.tsx", ["323", "324"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\hero-sections\\page.tsx", ["325", "326", "327", "328", "329", "330", "331"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\invoices\\page.tsx", ["332", "333", "334", "335", "336"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\jobs\\page.tsx", ["337", "338", "339", "340"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx", ["341"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\legal-pages\\page.tsx", ["342", "343", "344"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\page.tsx", ["345", "346", "347", "348"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\services\\page.tsx", ["349", "350", "351", "352", "353", "354"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\settings\\page.tsx", ["355", "356", "357", "358", "359"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\team-members\\page.tsx", ["360", "361", "362"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\technologies\\page.tsx", ["363", "364", "365", "366", "367", "368"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\testimonials\\page.tsx", ["369", "370", "371", "372", "373", "374", "375", "376"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\users\\page.tsx", ["377", "378", "379", "380", "381"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\clients\\route.ts", ["382"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\dashboard\\stats\\route.ts", ["383"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\route.ts", ["384"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\[id]\\route.ts", ["385"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\route.ts", ["386"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\[id]\\route.ts", ["387", "388"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\contact\\page.tsx", ["389", "390", "391", "392", "393", "394"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\page.tsx", ["395", "396", "397", "398", "399", "400", "401", "402", "403", "404"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\portfolio\\page.tsx", ["405", "406"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\page.tsx", ["407", "408", "409", "410"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\projects\\[slug]\\page.tsx", ["411", "412", "413", "414", "415", "416", "417", "418"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\page.tsx", ["419", "420", "421", "422"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\services\\[slug]\\page.tsx", ["423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\team\\page.tsx", ["440", "441", "442", "443", "444"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\page.tsx", ["445", "446", "447", "448", "449"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\technologies\\[slug]\\page.tsx", ["450", "451", "452", "453", "454", "455", "456", "457"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\api-utils.ts", ["458", "459", "460", "461", "462"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\validations.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\blog\\route.ts", ["463"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\blog\\[id]\\route.ts", ["464"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\contact\\route.ts", ["465"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\team\\route.ts", ["466"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\technologies\\route.ts", ["467"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\auth\\signin\\page.tsx", ["468"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\providers\\auth-provider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\components\\ui\\file-upload.tsx", ["469", "470"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\analytics.ts", ["471", "472", "473", "474"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\branding.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\email.ts", ["475"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\file-upload.ts", [], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\lib\\monitoring.ts", ["476", "477", "478", "479", "480", "481", "482", "483", "484"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\types\\next-auth.d.ts", ["485"], [], "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\__tests__\\api\\services.test.ts", ["486", "487", "488", "489", "490"], [], {"ruleId": "491", "severity": 1, "message": "492", "line": 5, "column": 3, "nodeType": null, "messageId": "493", "endLine": 5, "endColumn": 16}, {"ruleId": "491", "severity": 1, "message": "494", "line": 9, "column": 3, "nodeType": null, "messageId": "493", "endLine": 9, "endColumn": 13}, {"ruleId": "495", "severity": 1, "message": "496", "line": 318, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "499"}, {"ruleId": "491", "severity": 1, "message": "500", "line": 12, "column": 3, "nodeType": null, "messageId": "493", "endLine": 12, "endColumn": 14}, {"ruleId": "501", "severity": 1, "message": "502", "line": 90, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 90, "endColumn": 55, "suggestions": "505"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 91, "column": 10, "nodeType": null, "messageId": "493", "endLine": 91, "endColumn": 22}, {"ruleId": "495", "severity": 1, "message": "496", "line": 136, "column": 34, "nodeType": "497", "messageId": "498", "suggestions": "507"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 290, "column": 25, "nodeType": "510", "endLine": 294, "endColumn": 27}, {"ruleId": "501", "severity": 1, "message": "502", "line": 111, "column": 68, "nodeType": "503", "messageId": "504", "endLine": 111, "endColumn": 71, "suggestions": "511"}, {"ruleId": "491", "severity": 1, "message": "512", "line": 112, "column": 10, "nodeType": null, "messageId": "493", "endLine": 112, "endColumn": 26}, {"ruleId": "491", "severity": 1, "message": "513", "line": 15, "column": 3, "nodeType": null, "messageId": "493", "endLine": 15, "endColumn": 21}, {"ruleId": "491", "severity": 1, "message": "514", "line": 16, "column": 3, "nodeType": null, "messageId": "493", "endLine": 16, "endColumn": 16}, {"ruleId": "508", "severity": 1, "message": "509", "line": 307, "column": 23, "nodeType": "510", "endLine": 311, "endColumn": 25}, {"ruleId": "491", "severity": 1, "message": "515", "line": 8, "column": 3, "nodeType": null, "messageId": "493", "endLine": 8, "endColumn": 11}, {"ruleId": "491", "severity": 1, "message": "516", "line": 9, "column": 3, "nodeType": null, "messageId": "493", "endLine": 9, "endColumn": 15}, {"ruleId": "501", "severity": 1, "message": "502", "line": 118, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 118, "endColumn": 55, "suggestions": "517"}, {"ruleId": "491", "severity": 1, "message": "518", "line": 9, "column": 3, "nodeType": null, "messageId": "493", "endLine": 9, "endColumn": 26}, {"ruleId": "501", "severity": 1, "message": "502", "line": 93, "column": 56, "nodeType": "503", "messageId": "504", "endLine": 93, "endColumn": 59, "suggestions": "519"}, {"ruleId": "491", "severity": 1, "message": "520", "line": 11, "column": 3, "nodeType": null, "messageId": "493", "endLine": 11, "endColumn": 11}, {"ruleId": "491", "severity": 1, "message": "506", "line": 70, "column": 10, "nodeType": null, "messageId": "493", "endLine": 70, "endColumn": 22}, {"ruleId": "491", "severity": 1, "message": "521", "line": 71, "column": 10, "nodeType": null, "messageId": "493", "endLine": 71, "endColumn": 24}, {"ruleId": "501", "severity": 1, "message": "502", "line": 71, "column": 56, "nodeType": "503", "messageId": "504", "endLine": 71, "endColumn": 59, "suggestions": "522"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 72, "column": 56, "nodeType": "503", "messageId": "504", "endLine": 72, "endColumn": 59, "suggestions": "523"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 194, "column": 17, "nodeType": "510", "endLine": 198, "endColumn": 19}, {"ruleId": "508", "severity": 1, "message": "509", "line": 311, "column": 19, "nodeType": "510", "endLine": 315, "endColumn": 21}, {"ruleId": "491", "severity": 1, "message": "513", "line": 11, "column": 3, "nodeType": null, "messageId": "493", "endLine": 11, "endColumn": 21}, {"ruleId": "501", "severity": 1, "message": "502", "line": 152, "column": 58, "nodeType": "503", "messageId": "504", "endLine": 152, "endColumn": 61, "suggestions": "524"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 153, "column": 10, "nodeType": null, "messageId": "493", "endLine": 153, "endColumn": 22}, {"ruleId": "491", "severity": 1, "message": "525", "line": 164, "column": 9, "nodeType": null, "messageId": "493", "endLine": 164, "endColumn": 27}, {"ruleId": "501", "severity": 1, "message": "502", "line": 506, "column": 67, "nodeType": "503", "messageId": "504", "endLine": 506, "endColumn": 70, "suggestions": "526"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 204, "column": 50, "nodeType": "503", "messageId": "504", "endLine": 204, "endColumn": 53, "suggestions": "527"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 205, "column": 10, "nodeType": null, "messageId": "493", "endLine": 205, "endColumn": 22}, {"ruleId": "491", "severity": 1, "message": "528", "line": 226, "column": 9, "nodeType": null, "messageId": "493", "endLine": 226, "endColumn": 29}, {"ruleId": "501", "severity": 1, "message": "502", "line": 240, "column": 33, "nodeType": "503", "messageId": "504", "endLine": 240, "endColumn": 36, "suggestions": "529"}, {"ruleId": "491", "severity": 1, "message": "530", "line": 12, "column": 3, "nodeType": null, "messageId": "493", "endLine": 12, "endColumn": 15}, {"ruleId": "491", "severity": 1, "message": "500", "line": 12, "column": 3, "nodeType": null, "messageId": "493", "endLine": 12, "endColumn": 14}, {"ruleId": "501", "severity": 1, "message": "502", "line": 185, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 185, "endColumn": 55, "suggestions": "531"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 186, "column": 10, "nodeType": null, "messageId": "493", "endLine": 186, "endColumn": 22}, {"ruleId": "501", "severity": 1, "message": "502", "line": 38, "column": 83, "nodeType": "503", "messageId": "504", "endLine": 38, "endColumn": 86, "suggestions": "532"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 82, "column": 37, "nodeType": "503", "messageId": "504", "endLine": 82, "endColumn": 40, "suggestions": "533"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 137, "column": 33, "nodeType": "497", "messageId": "498", "suggestions": "534"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 137, "column": 40, "nodeType": "497", "messageId": "498", "suggestions": "535"}, {"ruleId": "491", "severity": 1, "message": "513", "line": 16, "column": 3, "nodeType": null, "messageId": "493", "endLine": 16, "endColumn": 21}, {"ruleId": "501", "severity": 1, "message": "502", "line": 139, "column": 33, "nodeType": "503", "messageId": "504", "endLine": 139, "endColumn": 36, "suggestions": "536"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 151, "column": 58, "nodeType": "503", "messageId": "504", "endLine": 151, "endColumn": 61, "suggestions": "537"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 152, "column": 10, "nodeType": null, "messageId": "493", "endLine": 152, "endColumn": 22}, {"ruleId": "491", "severity": 1, "message": "528", "line": 166, "column": 9, "nodeType": null, "messageId": "493", "endLine": 166, "endColumn": 29}, {"ruleId": "501", "severity": 1, "message": "502", "line": 180, "column": 33, "nodeType": "503", "messageId": "504", "endLine": 180, "endColumn": 36, "suggestions": "538"}, {"ruleId": "491", "severity": 1, "message": "539", "line": 10, "column": 3, "nodeType": null, "messageId": "493", "endLine": 10, "endColumn": 15}, {"ruleId": "491", "severity": 1, "message": "540", "line": 11, "column": 3, "nodeType": null, "messageId": "493", "endLine": 11, "endColumn": 15}, {"ruleId": "491", "severity": 1, "message": "541", "line": 12, "column": 3, "nodeType": null, "messageId": "493", "endLine": 12, "endColumn": 10}, {"ruleId": "491", "severity": 1, "message": "542", "line": 15, "column": 3, "nodeType": null, "messageId": "493", "endLine": 15, "endColumn": 19}, {"ruleId": "501", "severity": 1, "message": "502", "line": 93, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 93, "endColumn": 55, "suggestions": "543"}, {"ruleId": "491", "severity": 1, "message": "544", "line": 10, "column": 3, "nodeType": null, "messageId": "493", "endLine": 10, "endColumn": 13}, {"ruleId": "491", "severity": 1, "message": "506", "line": 81, "column": 10, "nodeType": null, "messageId": "493", "endLine": 81, "endColumn": 22}, {"ruleId": "508", "severity": 1, "message": "509", "line": 178, "column": 25, "nodeType": "510", "endLine": 182, "endColumn": 27}, {"ruleId": "501", "severity": 1, "message": "502", "line": 181, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 181, "endColumn": 55, "suggestions": "545"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 182, "column": 10, "nodeType": null, "messageId": "493", "endLine": 182, "endColumn": 22}, {"ruleId": "491", "severity": 1, "message": "546", "line": 194, "column": 9, "nodeType": null, "messageId": "493", "endLine": 194, "endColumn": 27}, {"ruleId": "491", "severity": 1, "message": "547", "line": 216, "column": 9, "nodeType": null, "messageId": "493", "endLine": 216, "endColumn": 19}, {"ruleId": "508", "severity": 1, "message": "509", "line": 390, "column": 23, "nodeType": "510", "endLine": 394, "endColumn": 25}, {"ruleId": "508", "severity": 1, "message": "509", "line": 507, "column": 25, "nodeType": "510", "endLine": 511, "endColumn": 27}, {"ruleId": "501", "severity": 1, "message": "502", "line": 113, "column": 66, "nodeType": "503", "messageId": "504", "endLine": 113, "endColumn": 69, "suggestions": "548"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 114, "column": 10, "nodeType": null, "messageId": "493", "endLine": 114, "endColumn": 22}, {"ruleId": "508", "severity": 1, "message": "509", "line": 349, "column": 23, "nodeType": "510", "endLine": 353, "endColumn": 25}, {"ruleId": "495", "severity": 1, "message": "549", "line": 435, "column": 19, "nodeType": "497", "messageId": "498", "suggestions": "550"}, {"ruleId": "495", "severity": 1, "message": "549", "line": 435, "column": 41, "nodeType": "497", "messageId": "498", "suggestions": "551"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 476, "column": 25, "nodeType": "510", "endLine": 480, "endColumn": 27}, {"ruleId": "495", "severity": 1, "message": "549", "line": 505, "column": 29, "nodeType": "497", "messageId": "498", "suggestions": "552"}, {"ruleId": "495", "severity": 1, "message": "549", "line": 505, "column": 59, "nodeType": "497", "messageId": "498", "suggestions": "553"}, {"ruleId": "491", "severity": 1, "message": "541", "line": 17, "column": 3, "nodeType": null, "messageId": "493", "endLine": 17, "endColumn": 10}, {"ruleId": "501", "severity": 1, "message": "502", "line": 104, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 104, "endColumn": 55, "suggestions": "554"}, {"ruleId": "491", "severity": 1, "message": "506", "line": 105, "column": 10, "nodeType": null, "messageId": "493", "endLine": 105, "endColumn": 22}, {"ruleId": "508", "severity": 1, "message": "509", "line": 342, "column": 25, "nodeType": "510", "endLine": 346, "endColumn": 27}, {"ruleId": "508", "severity": 1, "message": "509", "line": 447, "column": 25, "nodeType": "510", "endLine": 451, "endColumn": 27}, {"ruleId": "501", "severity": 1, "message": "502", "line": 23, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 23, "endColumn": 19, "suggestions": "555"}, {"ruleId": "491", "severity": 1, "message": "556", "line": 16, "column": 9, "nodeType": null, "messageId": "493", "endLine": 16, "endColumn": 20}, {"ruleId": "501", "severity": 1, "message": "502", "line": 23, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 23, "endColumn": 19, "suggestions": "557"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 339, "column": 21, "nodeType": "503", "messageId": "504", "endLine": 339, "endColumn": 24, "suggestions": "558"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 23, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 23, "endColumn": 19, "suggestions": "559"}, {"ruleId": "491", "severity": 1, "message": "560", "line": 6, "column": 3, "nodeType": null, "messageId": "493", "endLine": 6, "endColumn": 16}, {"ruleId": "501", "severity": 1, "message": "502", "line": 206, "column": 21, "nodeType": "503", "messageId": "504", "endLine": 206, "endColumn": 24, "suggestions": "561"}, {"ruleId": "491", "severity": 1, "message": "562", "line": 12, "column": 3, "nodeType": null, "messageId": "493", "endLine": 12, "endColumn": 12}, {"ruleId": "491", "severity": 1, "message": "563", "line": 91, "column": 14, "nodeType": null, "messageId": "493", "endLine": 91, "endColumn": 19}, {"ruleId": "495", "severity": 1, "message": "496", "line": 118, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "564"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 128, "column": 24, "nodeType": "497", "messageId": "498", "suggestions": "565"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 191, "column": 49, "nodeType": "497", "messageId": "498", "suggestions": "566"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 213, "column": 55, "nodeType": "497", "messageId": "498", "suggestions": "567"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 226, "column": 76, "nodeType": "497", "messageId": "498", "suggestions": "568"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 275, "column": 42, "nodeType": "497", "messageId": "498", "suggestions": "569"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 298, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "570"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 298, "column": 54, "nodeType": "497", "messageId": "498", "suggestions": "571"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 299, "column": 60, "nodeType": "497", "messageId": "498", "suggestions": "572"}, {"ruleId": "495", "severity": 1, "message": "549", "line": 326, "column": 21, "nodeType": "497", "messageId": "498", "suggestions": "573"}, {"ruleId": "495", "severity": 1, "message": "549", "line": 326, "column": 43, "nodeType": "497", "messageId": "498", "suggestions": "574"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 331, "column": 23, "nodeType": "510", "endLine": 335, "endColumn": 25}, {"ruleId": "495", "severity": 1, "message": "496", "line": 378, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "575"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 382, "column": 24, "nodeType": "497", "messageId": "498", "suggestions": "576"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 170, "column": 17, "nodeType": "497", "messageId": "498", "suggestions": "577"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 398, "column": 16, "nodeType": "497", "messageId": "498", "suggestions": "578"}, {"ruleId": "491", "severity": 1, "message": "547", "line": 212, "column": 9, "nodeType": null, "messageId": "493", "endLine": 212, "endColumn": 19}, {"ruleId": "495", "severity": 1, "message": "496", "line": 258, "column": 63, "nodeType": "497", "messageId": "498", "suggestions": "579"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 353, "column": 21, "nodeType": "510", "endLine": 357, "endColumn": 23}, {"ruleId": "495", "severity": 1, "message": "496", "line": 458, "column": 63, "nodeType": "497", "messageId": "498", "suggestions": "580"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 20, "column": 38, "nodeType": "503", "messageId": "504", "endLine": 20, "endColumn": 41, "suggestions": "581"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 148, "column": 70, "nodeType": "497", "messageId": "498", "suggestions": "582"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 148, "column": 91, "nodeType": "497", "messageId": "498", "suggestions": "583"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 277, "column": 17, "nodeType": "510", "endLine": 281, "endColumn": 19}, {"ruleId": "495", "severity": 1, "message": "549", "line": 413, "column": 19, "nodeType": "497", "messageId": "498", "suggestions": "584"}, {"ruleId": "495", "severity": 1, "message": "549", "line": 413, "column": 49, "nodeType": "497", "messageId": "498", "suggestions": "585"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 416, "column": 19, "nodeType": "510", "endLine": 420, "endColumn": 21}, {"ruleId": "495", "severity": 1, "message": "496", "line": 445, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "586"}, {"ruleId": "491", "severity": 1, "message": "513", "line": 14, "column": 3, "nodeType": null, "messageId": "493", "endLine": 14, "endColumn": 21}, {"ruleId": "501", "severity": 1, "message": "502", "line": 168, "column": 31, "nodeType": "503", "messageId": "504", "endLine": 168, "endColumn": 34, "suggestions": "587"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 210, "column": 76, "nodeType": "497", "messageId": "498", "suggestions": "588"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 380, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "589"}, {"ruleId": "491", "severity": 1, "message": "590", "line": 9, "column": 3, "nodeType": null, "messageId": "493", "endLine": 9, "endColumn": 12}, {"ruleId": "491", "severity": 1, "message": "591", "line": 10, "column": 3, "nodeType": null, "messageId": "493", "endLine": 10, "endColumn": 10}, {"ruleId": "491", "severity": 1, "message": "592", "line": 11, "column": 3, "nodeType": null, "messageId": "493", "endLine": 11, "endColumn": 18}, {"ruleId": "491", "severity": 1, "message": "530", "line": 12, "column": 3, "nodeType": null, "messageId": "493", "endLine": 12, "endColumn": 15}, {"ruleId": "491", "severity": 1, "message": "492", "line": 16, "column": 3, "nodeType": null, "messageId": "493", "endLine": 16, "endColumn": 16}, {"ruleId": "501", "severity": 1, "message": "502", "line": 24, "column": 38, "nodeType": "503", "messageId": "504", "endLine": 24, "endColumn": 41, "suggestions": "593"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 162, "column": 70, "nodeType": "497", "messageId": "498", "suggestions": "594"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 162, "column": 91, "nodeType": "497", "messageId": "498", "suggestions": "595"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 175, "column": 33, "nodeType": "503", "messageId": "504", "endLine": 175, "endColumn": 36, "suggestions": "596"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 349, "column": 46, "nodeType": "497", "messageId": "498", "suggestions": "597"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 390, "column": 43, "nodeType": "503", "messageId": "504", "endLine": 390, "endColumn": 46, "suggestions": "598"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 427, "column": 22, "nodeType": "497", "messageId": "498", "suggestions": "599"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 427, "column": 56, "nodeType": "497", "messageId": "498", "suggestions": "600"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 432, "column": 57, "nodeType": "503", "messageId": "504", "endLine": 432, "endColumn": 60, "suggestions": "601"}, {"ruleId": "495", "severity": 1, "message": "549", "line": 445, "column": 23, "nodeType": "497", "messageId": "498", "suggestions": "602"}, {"ruleId": "495", "severity": 1, "message": "549", "line": 445, "column": 45, "nodeType": "497", "messageId": "498", "suggestions": "603"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 472, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "604"}, {"ruleId": "491", "severity": 1, "message": "605", "line": 9, "column": 3, "nodeType": null, "messageId": "493", "endLine": 9, "endColumn": 18}, {"ruleId": "495", "severity": 1, "message": "496", "line": 207, "column": 17, "nodeType": "497", "messageId": "498", "suggestions": "606"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 434, "column": 15, "nodeType": "497", "messageId": "498", "suggestions": "607"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 435, "column": 35, "nodeType": "497", "messageId": "498", "suggestions": "608"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 435, "column": 66, "nodeType": "497", "messageId": "498", "suggestions": "609"}, {"ruleId": "491", "severity": 1, "message": "610", "line": 10, "column": 3, "nodeType": null, "messageId": "493", "endLine": 10, "endColumn": 24}, {"ruleId": "491", "severity": 1, "message": "611", "line": 358, "column": 23, "nodeType": null, "messageId": "493", "endLine": 358, "endColumn": 35}, {"ruleId": "508", "severity": 1, "message": "509", "line": 371, "column": 27, "nodeType": "510", "endLine": 375, "endColumn": 29}, {"ruleId": "508", "severity": 1, "message": "509", "line": 494, "column": 21, "nodeType": "510", "endLine": 498, "endColumn": 23}, {"ruleId": "495", "severity": 1, "message": "496", "line": 530, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "612"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 19, "column": 42, "nodeType": "503", "messageId": "504", "endLine": 19, "endColumn": 45, "suggestions": "613"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 155, "column": 73, "nodeType": "497", "messageId": "498", "suggestions": "614"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 155, "column": 94, "nodeType": "497", "messageId": "498", "suggestions": "615"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 213, "column": 21, "nodeType": "510", "endLine": 217, "endColumn": 23}, {"ruleId": "508", "severity": 1, "message": "509", "line": 277, "column": 19, "nodeType": "510", "endLine": 281, "endColumn": 21}, {"ruleId": "495", "severity": 1, "message": "496", "line": 427, "column": 29, "nodeType": "497", "messageId": "498", "suggestions": "616"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 432, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 432, "endColumn": 55, "suggestions": "617"}, {"ruleId": "495", "severity": 1, "message": "496", "line": 471, "column": 20, "nodeType": "497", "messageId": "498", "suggestions": "618"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 6, "column": 34, "nodeType": "503", "messageId": "504", "endLine": 6, "endColumn": 37, "suggestions": "619"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 13, "column": 40, "nodeType": "503", "messageId": "504", "endLine": 13, "endColumn": 43, "suggestions": "620"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 131, "column": 45, "nodeType": "503", "messageId": "504", "endLine": 131, "endColumn": 48, "suggestions": "621"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 133, "column": 49, "nodeType": "503", "messageId": "504", "endLine": 133, "endColumn": 52, "suggestions": "622"}, {"ruleId": "491", "severity": 1, "message": "623", "line": 176, "column": 35, "nodeType": null, "messageId": "493", "endLine": 176, "endColumn": 42}, {"ruleId": "501", "severity": 1, "message": "502", "line": 24, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 24, "endColumn": 19, "suggestions": "624"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 170, "column": 21, "nodeType": "503", "messageId": "504", "endLine": 170, "endColumn": 24, "suggestions": "625"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 25, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 25, "endColumn": 19, "suggestions": "626"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 23, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 23, "endColumn": 19, "suggestions": "627"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 23, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 23, "endColumn": 19, "suggestions": "628"}, {"ruleId": "491", "severity": 1, "message": "563", "line": 53, "column": 14, "nodeType": null, "messageId": "493", "endLine": 53, "endColumn": 19}, {"ruleId": "491", "severity": 1, "message": "629", "line": 5, "column": 39, "nodeType": null, "messageId": "493", "endLine": 5, "endColumn": 51}, {"ruleId": "630", "severity": 1, "message": "631", "line": 73, "column": 6, "nodeType": "632", "endLine": 73, "endColumn": 8, "suggestions": "633"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 5, "column": 21, "nodeType": "503", "messageId": "504", "endLine": 5, "endColumn": 24, "suggestions": "634"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 6, "column": 16, "nodeType": "503", "messageId": "504", "endLine": 6, "endColumn": 19, "suggestions": "635"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 23, "column": 40, "nodeType": "503", "messageId": "504", "endLine": 23, "endColumn": 43, "suggestions": "636"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 151, "column": 85, "nodeType": "503", "messageId": "504", "endLine": 151, "endColumn": 88, "suggestions": "637"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 132, "column": 69, "nodeType": "503", "messageId": "504", "endLine": 132, "endColumn": 72, "suggestions": "638"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 9, "column": 28, "nodeType": "503", "messageId": "504", "endLine": 9, "endColumn": 31, "suggestions": "639"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 22, "column": 28, "nodeType": "503", "messageId": "504", "endLine": 22, "endColumn": 31, "suggestions": "640"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 36, "column": 68, "nodeType": "503", "messageId": "504", "endLine": 36, "endColumn": 71, "suggestions": "641"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 63, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 63, "endColumn": 55, "suggestions": "642"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 82, "column": 52, "nodeType": "503", "messageId": "504", "endLine": 82, "endColumn": 55, "suggestions": "643"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 100, "column": 53, "nodeType": "503", "messageId": "504", "endLine": 100, "endColumn": 56, "suggestions": "644"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 175, "column": 36, "nodeType": "503", "messageId": "504", "endLine": 175, "endColumn": 39, "suggestions": "645"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 191, "column": 90, "nodeType": "503", "messageId": "504", "endLine": 191, "endColumn": 93, "suggestions": "646"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 286, "column": 70, "nodeType": "503", "messageId": "504", "endLine": 286, "endColumn": 73, "suggestions": "647"}, {"ruleId": "491", "severity": 1, "message": "648", "line": 1, "column": 8, "nodeType": null, "messageId": "493", "endLine": 1, "endColumn": 16}, {"ruleId": "501", "severity": 1, "message": "502", "line": 57, "column": 41, "nodeType": "503", "messageId": "504", "endLine": 57, "endColumn": 44, "suggestions": "649"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 80, "column": 24, "nodeType": "503", "messageId": "504", "endLine": 80, "endColumn": 27, "suggestions": "650"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 119, "column": 42, "nodeType": "503", "messageId": "504", "endLine": 119, "endColumn": 45, "suggestions": "651"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 143, "column": 42, "nodeType": "503", "messageId": "504", "endLine": 143, "endColumn": 45, "suggestions": "652"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 163, "column": 42, "nodeType": "503", "messageId": "504", "endLine": 163, "endColumn": 45, "suggestions": "653"}, "@typescript-eslint/no-unused-vars", "'UserGroupIcon' is defined but never used.", "unusedVar", "'TrophyIcon' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["654", "655", "656", "657"], "'XCircleIcon' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["658", "659"], "'showAddModal' is assigned a value but never used.", ["660", "661", "662", "663"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["664", "665"], "'showAddKnowledge' is assigned a value but never used.", "'CurrencyDollarIcon' is defined but never used.", "'BriefcaseIcon' is defined but never used.", "'UserIcon' is defined but never used.", "'CalendarIcon' is defined but never used.", ["666", "667"], "'ExclamationTriangleIcon' is defined but never used.", ["668", "669"], "'PlayIcon' is defined but never used.", "'editingSection' is assigned a value but never used.", ["670", "671"], ["672", "673"], ["674", "675"], "'handleStatusChange' is assigned a value but never used.", ["676", "677"], ["678", "679"], "'handleToggleFeatured' is assigned a value but never used.", ["680", "681"], "'ChartBarIcon' is defined but never used.", ["682", "683"], ["684", "685"], ["686", "687"], ["688", "689", "690", "691"], ["692", "693", "694", "695"], ["696", "697"], ["698", "699"], ["700", "701"], "'GlobeAltIcon' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "'KeyIcon' is defined but never used.", "'DocumentTextIcon' is defined but never used.", ["702", "703"], "'FunnelIcon' is defined but never used.", ["704", "705"], "'handleToggleActive' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", ["706", "707"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["708", "709", "710", "711"], ["712", "713", "714", "715"], ["716", "717", "718", "719"], ["720", "721", "722", "723"], ["724", "725"], ["726", "727"], "'startOfYear' is assigned a value but never used.", ["728", "729"], ["730", "731"], ["732", "733"], "'errorResponse' is defined but never used.", ["734", "735"], "'ClockIcon' is defined but never used.", "'error' is defined but never used.", ["736", "737", "738", "739"], ["740", "741", "742", "743"], ["744", "745", "746", "747"], ["748", "749", "750", "751"], ["752", "753", "754", "755"], ["756", "757", "758", "759"], ["760", "761", "762", "763"], ["764", "765", "766", "767"], ["768", "769", "770", "771"], ["772", "773", "774", "775"], ["776", "777", "778", "779"], ["780", "781", "782", "783"], ["784", "785", "786", "787"], ["788", "789", "790", "791"], ["792", "793", "794", "795"], ["796", "797", "798", "799"], ["800", "801", "802", "803"], ["804", "805"], ["806", "807", "808", "809"], ["810", "811", "812", "813"], ["814", "815", "816", "817"], ["818", "819", "820", "821"], ["822", "823", "824", "825"], ["826", "827"], ["828", "829", "830", "831"], ["832", "833", "834", "835"], "'CloudIcon' is defined but never used.", "'CogIcon' is defined but never used.", "'ShieldCheckIcon' is defined but never used.", ["836", "837"], ["838", "839", "840", "841"], ["842", "843", "844", "845"], ["846", "847"], ["848", "849", "850", "851"], ["852", "853"], ["854", "855", "856", "857"], ["858", "859", "860", "861"], ["862", "863"], ["864", "865", "866", "867"], ["868", "869", "870", "871"], ["872", "873", "874", "875"], "'AcademicCapIcon' is defined but never used.", ["876", "877", "878", "879"], ["880", "881", "882", "883"], ["884", "885", "886", "887"], ["888", "889", "890", "891"], "'DevicePhoneMobileIcon' is defined but never used.", "'CategoryIcon' is assigned a value but never used.", ["892", "893", "894", "895"], ["896", "897"], ["898", "899", "900", "901"], ["902", "903", "904", "905"], ["906", "907", "908", "909"], ["910", "911"], ["912", "913", "914", "915"], ["916", "917"], ["918", "919"], ["920", "921"], ["922", "923"], "'request' is defined but never used.", ["924", "925"], ["926", "927"], ["928", "929"], ["930", "931"], ["932", "933"], "'DocumentIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'uploadFiles'. Either include it or remove the dependency array.", "ArrayExpression", ["934"], ["935", "936"], ["937", "938"], ["939", "940"], ["941", "942"], ["943", "944"], ["945", "946"], ["947", "948"], ["949", "950"], ["951", "952"], ["953", "954"], ["955", "956"], ["957", "958"], ["959", "960"], ["961", "962"], "'NextAuth' is defined but never used.", ["963", "964"], ["965", "966"], ["967", "968"], ["969", "970"], ["971", "972"], {"messageId": "973", "data": "974", "fix": "975", "desc": "976"}, {"messageId": "973", "data": "977", "fix": "978", "desc": "979"}, {"messageId": "973", "data": "980", "fix": "981", "desc": "982"}, {"messageId": "973", "data": "983", "fix": "984", "desc": "985"}, {"messageId": "986", "fix": "987", "desc": "988"}, {"messageId": "989", "fix": "990", "desc": "991"}, {"messageId": "973", "data": "992", "fix": "993", "desc": "976"}, {"messageId": "973", "data": "994", "fix": "995", "desc": "979"}, {"messageId": "973", "data": "996", "fix": "997", "desc": "982"}, {"messageId": "973", "data": "998", "fix": "999", "desc": "985"}, {"messageId": "986", "fix": "1000", "desc": "988"}, {"messageId": "989", "fix": "1001", "desc": "991"}, {"messageId": "986", "fix": "1002", "desc": "988"}, {"messageId": "989", "fix": "1003", "desc": "991"}, {"messageId": "986", "fix": "1004", "desc": "988"}, {"messageId": "989", "fix": "1005", "desc": "991"}, {"messageId": "986", "fix": "1006", "desc": "988"}, {"messageId": "989", "fix": "1007", "desc": "991"}, {"messageId": "986", "fix": "1008", "desc": "988"}, {"messageId": "989", "fix": "1009", "desc": "991"}, {"messageId": "986", "fix": "1010", "desc": "988"}, {"messageId": "989", "fix": "1011", "desc": "991"}, {"messageId": "986", "fix": "1012", "desc": "988"}, {"messageId": "989", "fix": "1013", "desc": "991"}, {"messageId": "986", "fix": "1014", "desc": "988"}, {"messageId": "989", "fix": "1015", "desc": "991"}, {"messageId": "986", "fix": "1016", "desc": "988"}, {"messageId": "989", "fix": "1017", "desc": "991"}, {"messageId": "986", "fix": "1018", "desc": "988"}, {"messageId": "989", "fix": "1019", "desc": "991"}, {"messageId": "986", "fix": "1020", "desc": "988"}, {"messageId": "989", "fix": "1021", "desc": "991"}, {"messageId": "986", "fix": "1022", "desc": "988"}, {"messageId": "989", "fix": "1023", "desc": "991"}, {"messageId": "973", "data": "1024", "fix": "1025", "desc": "976"}, {"messageId": "973", "data": "1026", "fix": "1027", "desc": "979"}, {"messageId": "973", "data": "1028", "fix": "1029", "desc": "982"}, {"messageId": "973", "data": "1030", "fix": "1031", "desc": "985"}, {"messageId": "973", "data": "1032", "fix": "1033", "desc": "976"}, {"messageId": "973", "data": "1034", "fix": "1035", "desc": "979"}, {"messageId": "973", "data": "1036", "fix": "1037", "desc": "982"}, {"messageId": "973", "data": "1038", "fix": "1039", "desc": "985"}, {"messageId": "986", "fix": "1040", "desc": "988"}, {"messageId": "989", "fix": "1041", "desc": "991"}, {"messageId": "986", "fix": "1042", "desc": "988"}, {"messageId": "989", "fix": "1043", "desc": "991"}, {"messageId": "986", "fix": "1044", "desc": "988"}, {"messageId": "989", "fix": "1045", "desc": "991"}, {"messageId": "986", "fix": "1046", "desc": "988"}, {"messageId": "989", "fix": "1047", "desc": "991"}, {"messageId": "986", "fix": "1048", "desc": "988"}, {"messageId": "989", "fix": "1049", "desc": "991"}, {"messageId": "986", "fix": "1050", "desc": "988"}, {"messageId": "989", "fix": "1051", "desc": "991"}, {"messageId": "973", "data": "1052", "fix": "1053", "desc": "1054"}, {"messageId": "973", "data": "1055", "fix": "1056", "desc": "1057"}, {"messageId": "973", "data": "1058", "fix": "1059", "desc": "1060"}, {"messageId": "973", "data": "1061", "fix": "1062", "desc": "1063"}, {"messageId": "973", "data": "1064", "fix": "1065", "desc": "1054"}, {"messageId": "973", "data": "1066", "fix": "1067", "desc": "1057"}, {"messageId": "973", "data": "1068", "fix": "1069", "desc": "1060"}, {"messageId": "973", "data": "1070", "fix": "1071", "desc": "1063"}, {"messageId": "973", "data": "1072", "fix": "1073", "desc": "1054"}, {"messageId": "973", "data": "1074", "fix": "1075", "desc": "1057"}, {"messageId": "973", "data": "1076", "fix": "1077", "desc": "1060"}, {"messageId": "973", "data": "1078", "fix": "1079", "desc": "1063"}, {"messageId": "973", "data": "1080", "fix": "1081", "desc": "1054"}, {"messageId": "973", "data": "1082", "fix": "1083", "desc": "1057"}, {"messageId": "973", "data": "1084", "fix": "1085", "desc": "1060"}, {"messageId": "973", "data": "1086", "fix": "1087", "desc": "1063"}, {"messageId": "986", "fix": "1088", "desc": "988"}, {"messageId": "989", "fix": "1089", "desc": "991"}, {"messageId": "986", "fix": "1090", "desc": "988"}, {"messageId": "989", "fix": "1091", "desc": "991"}, {"messageId": "986", "fix": "1092", "desc": "988"}, {"messageId": "989", "fix": "1093", "desc": "991"}, {"messageId": "986", "fix": "1094", "desc": "988"}, {"messageId": "989", "fix": "1095", "desc": "991"}, {"messageId": "986", "fix": "1096", "desc": "988"}, {"messageId": "989", "fix": "1097", "desc": "991"}, {"messageId": "986", "fix": "1098", "desc": "988"}, {"messageId": "989", "fix": "1099", "desc": "991"}, {"messageId": "973", "data": "1100", "fix": "1101", "desc": "976"}, {"messageId": "973", "data": "1102", "fix": "1103", "desc": "979"}, {"messageId": "973", "data": "1104", "fix": "1105", "desc": "982"}, {"messageId": "973", "data": "1106", "fix": "1107", "desc": "985"}, {"messageId": "973", "data": "1108", "fix": "1109", "desc": "976"}, {"messageId": "973", "data": "1110", "fix": "1111", "desc": "979"}, {"messageId": "973", "data": "1112", "fix": "1113", "desc": "982"}, {"messageId": "973", "data": "1114", "fix": "1115", "desc": "985"}, {"messageId": "973", "data": "1116", "fix": "1117", "desc": "976"}, {"messageId": "973", "data": "1118", "fix": "1119", "desc": "979"}, {"messageId": "973", "data": "1120", "fix": "1121", "desc": "982"}, {"messageId": "973", "data": "1122", "fix": "1123", "desc": "985"}, {"messageId": "973", "data": "1124", "fix": "1125", "desc": "976"}, {"messageId": "973", "data": "1126", "fix": "1127", "desc": "979"}, {"messageId": "973", "data": "1128", "fix": "1129", "desc": "982"}, {"messageId": "973", "data": "1130", "fix": "1131", "desc": "985"}, {"messageId": "973", "data": "1132", "fix": "1133", "desc": "976"}, {"messageId": "973", "data": "1134", "fix": "1135", "desc": "979"}, {"messageId": "973", "data": "1136", "fix": "1137", "desc": "982"}, {"messageId": "973", "data": "1138", "fix": "1139", "desc": "985"}, {"messageId": "973", "data": "1140", "fix": "1141", "desc": "976"}, {"messageId": "973", "data": "1142", "fix": "1143", "desc": "979"}, {"messageId": "973", "data": "1144", "fix": "1145", "desc": "982"}, {"messageId": "973", "data": "1146", "fix": "1147", "desc": "985"}, {"messageId": "973", "data": "1148", "fix": "1149", "desc": "976"}, {"messageId": "973", "data": "1150", "fix": "1151", "desc": "979"}, {"messageId": "973", "data": "1152", "fix": "1153", "desc": "982"}, {"messageId": "973", "data": "1154", "fix": "1155", "desc": "985"}, {"messageId": "973", "data": "1156", "fix": "1157", "desc": "976"}, {"messageId": "973", "data": "1158", "fix": "1159", "desc": "979"}, {"messageId": "973", "data": "1160", "fix": "1161", "desc": "982"}, {"messageId": "973", "data": "1162", "fix": "1163", "desc": "985"}, {"messageId": "973", "data": "1164", "fix": "1165", "desc": "976"}, {"messageId": "973", "data": "1166", "fix": "1167", "desc": "979"}, {"messageId": "973", "data": "1168", "fix": "1169", "desc": "982"}, {"messageId": "973", "data": "1170", "fix": "1171", "desc": "985"}, {"messageId": "973", "data": "1172", "fix": "1173", "desc": "1054"}, {"messageId": "973", "data": "1174", "fix": "1175", "desc": "1057"}, {"messageId": "973", "data": "1176", "fix": "1177", "desc": "1060"}, {"messageId": "973", "data": "1178", "fix": "1179", "desc": "1063"}, {"messageId": "973", "data": "1180", "fix": "1181", "desc": "1054"}, {"messageId": "973", "data": "1182", "fix": "1183", "desc": "1057"}, {"messageId": "973", "data": "1184", "fix": "1185", "desc": "1060"}, {"messageId": "973", "data": "1186", "fix": "1187", "desc": "1063"}, {"messageId": "973", "data": "1188", "fix": "1189", "desc": "976"}, {"messageId": "973", "data": "1190", "fix": "1191", "desc": "979"}, {"messageId": "973", "data": "1192", "fix": "1193", "desc": "982"}, {"messageId": "973", "data": "1194", "fix": "1195", "desc": "985"}, {"messageId": "973", "data": "1196", "fix": "1197", "desc": "976"}, {"messageId": "973", "data": "1198", "fix": "1199", "desc": "979"}, {"messageId": "973", "data": "1200", "fix": "1201", "desc": "982"}, {"messageId": "973", "data": "1202", "fix": "1203", "desc": "985"}, {"messageId": "973", "data": "1204", "fix": "1205", "desc": "976"}, {"messageId": "973", "data": "1206", "fix": "1207", "desc": "979"}, {"messageId": "973", "data": "1208", "fix": "1209", "desc": "982"}, {"messageId": "973", "data": "1210", "fix": "1211", "desc": "985"}, {"messageId": "973", "data": "1212", "fix": "1213", "desc": "976"}, {"messageId": "973", "data": "1214", "fix": "1215", "desc": "979"}, {"messageId": "973", "data": "1216", "fix": "1217", "desc": "982"}, {"messageId": "973", "data": "1218", "fix": "1219", "desc": "985"}, {"messageId": "973", "data": "1220", "fix": "1221", "desc": "976"}, {"messageId": "973", "data": "1222", "fix": "1223", "desc": "979"}, {"messageId": "973", "data": "1224", "fix": "1225", "desc": "982"}, {"messageId": "973", "data": "1226", "fix": "1227", "desc": "985"}, {"messageId": "973", "data": "1228", "fix": "1229", "desc": "976"}, {"messageId": "973", "data": "1230", "fix": "1231", "desc": "979"}, {"messageId": "973", "data": "1232", "fix": "1233", "desc": "982"}, {"messageId": "973", "data": "1234", "fix": "1235", "desc": "985"}, {"messageId": "986", "fix": "1236", "desc": "988"}, {"messageId": "989", "fix": "1237", "desc": "991"}, {"messageId": "973", "data": "1238", "fix": "1239", "desc": "976"}, {"messageId": "973", "data": "1240", "fix": "1241", "desc": "979"}, {"messageId": "973", "data": "1242", "fix": "1243", "desc": "982"}, {"messageId": "973", "data": "1244", "fix": "1245", "desc": "985"}, {"messageId": "973", "data": "1246", "fix": "1247", "desc": "976"}, {"messageId": "973", "data": "1248", "fix": "1249", "desc": "979"}, {"messageId": "973", "data": "1250", "fix": "1251", "desc": "982"}, {"messageId": "973", "data": "1252", "fix": "1253", "desc": "985"}, {"messageId": "973", "data": "1254", "fix": "1255", "desc": "1054"}, {"messageId": "973", "data": "1256", "fix": "1257", "desc": "1057"}, {"messageId": "973", "data": "1258", "fix": "1259", "desc": "1060"}, {"messageId": "973", "data": "1260", "fix": "1261", "desc": "1063"}, {"messageId": "973", "data": "1262", "fix": "1263", "desc": "1054"}, {"messageId": "973", "data": "1264", "fix": "1265", "desc": "1057"}, {"messageId": "973", "data": "1266", "fix": "1267", "desc": "1060"}, {"messageId": "973", "data": "1268", "fix": "1269", "desc": "1063"}, {"messageId": "973", "data": "1270", "fix": "1271", "desc": "976"}, {"messageId": "973", "data": "1272", "fix": "1273", "desc": "979"}, {"messageId": "973", "data": "1274", "fix": "1275", "desc": "982"}, {"messageId": "973", "data": "1276", "fix": "1277", "desc": "985"}, {"messageId": "986", "fix": "1278", "desc": "988"}, {"messageId": "989", "fix": "1279", "desc": "991"}, {"messageId": "973", "data": "1280", "fix": "1281", "desc": "976"}, {"messageId": "973", "data": "1282", "fix": "1283", "desc": "979"}, {"messageId": "973", "data": "1284", "fix": "1285", "desc": "982"}, {"messageId": "973", "data": "1286", "fix": "1287", "desc": "985"}, {"messageId": "973", "data": "1288", "fix": "1289", "desc": "976"}, {"messageId": "973", "data": "1290", "fix": "1291", "desc": "979"}, {"messageId": "973", "data": "1292", "fix": "1293", "desc": "982"}, {"messageId": "973", "data": "1294", "fix": "1295", "desc": "985"}, {"messageId": "986", "fix": "1296", "desc": "988"}, {"messageId": "989", "fix": "1297", "desc": "991"}, {"messageId": "973", "data": "1298", "fix": "1299", "desc": "976"}, {"messageId": "973", "data": "1300", "fix": "1301", "desc": "979"}, {"messageId": "973", "data": "1302", "fix": "1303", "desc": "982"}, {"messageId": "973", "data": "1304", "fix": "1305", "desc": "985"}, {"messageId": "973", "data": "1306", "fix": "1307", "desc": "976"}, {"messageId": "973", "data": "1308", "fix": "1309", "desc": "979"}, {"messageId": "973", "data": "1310", "fix": "1311", "desc": "982"}, {"messageId": "973", "data": "1312", "fix": "1313", "desc": "985"}, {"messageId": "986", "fix": "1314", "desc": "988"}, {"messageId": "989", "fix": "1315", "desc": "991"}, {"messageId": "973", "data": "1316", "fix": "1317", "desc": "976"}, {"messageId": "973", "data": "1318", "fix": "1319", "desc": "979"}, {"messageId": "973", "data": "1320", "fix": "1321", "desc": "982"}, {"messageId": "973", "data": "1322", "fix": "1323", "desc": "985"}, {"messageId": "986", "fix": "1324", "desc": "988"}, {"messageId": "989", "fix": "1325", "desc": "991"}, {"messageId": "973", "data": "1326", "fix": "1327", "desc": "976"}, {"messageId": "973", "data": "1328", "fix": "1329", "desc": "979"}, {"messageId": "973", "data": "1330", "fix": "1331", "desc": "982"}, {"messageId": "973", "data": "1332", "fix": "1333", "desc": "985"}, {"messageId": "973", "data": "1334", "fix": "1335", "desc": "976"}, {"messageId": "973", "data": "1336", "fix": "1337", "desc": "979"}, {"messageId": "973", "data": "1338", "fix": "1339", "desc": "982"}, {"messageId": "973", "data": "1340", "fix": "1341", "desc": "985"}, {"messageId": "986", "fix": "1342", "desc": "988"}, {"messageId": "989", "fix": "1343", "desc": "991"}, {"messageId": "973", "data": "1344", "fix": "1345", "desc": "1054"}, {"messageId": "973", "data": "1346", "fix": "1347", "desc": "1057"}, {"messageId": "973", "data": "1348", "fix": "1349", "desc": "1060"}, {"messageId": "973", "data": "1350", "fix": "1351", "desc": "1063"}, {"messageId": "973", "data": "1352", "fix": "1353", "desc": "1054"}, {"messageId": "973", "data": "1354", "fix": "1355", "desc": "1057"}, {"messageId": "973", "data": "1356", "fix": "1357", "desc": "1060"}, {"messageId": "973", "data": "1358", "fix": "1359", "desc": "1063"}, {"messageId": "973", "data": "1360", "fix": "1361", "desc": "976"}, {"messageId": "973", "data": "1362", "fix": "1363", "desc": "979"}, {"messageId": "973", "data": "1364", "fix": "1365", "desc": "982"}, {"messageId": "973", "data": "1366", "fix": "1367", "desc": "985"}, {"messageId": "973", "data": "1368", "fix": "1369", "desc": "976"}, {"messageId": "973", "data": "1370", "fix": "1371", "desc": "979"}, {"messageId": "973", "data": "1372", "fix": "1373", "desc": "982"}, {"messageId": "973", "data": "1374", "fix": "1375", "desc": "985"}, {"messageId": "973", "data": "1376", "fix": "1377", "desc": "976"}, {"messageId": "973", "data": "1378", "fix": "1379", "desc": "979"}, {"messageId": "973", "data": "1380", "fix": "1381", "desc": "982"}, {"messageId": "973", "data": "1382", "fix": "1383", "desc": "985"}, {"messageId": "973", "data": "1384", "fix": "1385", "desc": "976"}, {"messageId": "973", "data": "1386", "fix": "1387", "desc": "979"}, {"messageId": "973", "data": "1388", "fix": "1389", "desc": "982"}, {"messageId": "973", "data": "1390", "fix": "1391", "desc": "985"}, {"messageId": "973", "data": "1392", "fix": "1393", "desc": "976"}, {"messageId": "973", "data": "1394", "fix": "1395", "desc": "979"}, {"messageId": "973", "data": "1396", "fix": "1397", "desc": "982"}, {"messageId": "973", "data": "1398", "fix": "1399", "desc": "985"}, {"messageId": "973", "data": "1400", "fix": "1401", "desc": "976"}, {"messageId": "973", "data": "1402", "fix": "1403", "desc": "979"}, {"messageId": "973", "data": "1404", "fix": "1405", "desc": "982"}, {"messageId": "973", "data": "1406", "fix": "1407", "desc": "985"}, {"messageId": "986", "fix": "1408", "desc": "988"}, {"messageId": "989", "fix": "1409", "desc": "991"}, {"messageId": "973", "data": "1410", "fix": "1411", "desc": "976"}, {"messageId": "973", "data": "1412", "fix": "1413", "desc": "979"}, {"messageId": "973", "data": "1414", "fix": "1415", "desc": "982"}, {"messageId": "973", "data": "1416", "fix": "1417", "desc": "985"}, {"messageId": "973", "data": "1418", "fix": "1419", "desc": "976"}, {"messageId": "973", "data": "1420", "fix": "1421", "desc": "979"}, {"messageId": "973", "data": "1422", "fix": "1423", "desc": "982"}, {"messageId": "973", "data": "1424", "fix": "1425", "desc": "985"}, {"messageId": "973", "data": "1426", "fix": "1427", "desc": "976"}, {"messageId": "973", "data": "1428", "fix": "1429", "desc": "979"}, {"messageId": "973", "data": "1430", "fix": "1431", "desc": "982"}, {"messageId": "973", "data": "1432", "fix": "1433", "desc": "985"}, {"messageId": "986", "fix": "1434", "desc": "988"}, {"messageId": "989", "fix": "1435", "desc": "991"}, {"messageId": "973", "data": "1436", "fix": "1437", "desc": "976"}, {"messageId": "973", "data": "1438", "fix": "1439", "desc": "979"}, {"messageId": "973", "data": "1440", "fix": "1441", "desc": "982"}, {"messageId": "973", "data": "1442", "fix": "1443", "desc": "985"}, {"messageId": "986", "fix": "1444", "desc": "988"}, {"messageId": "989", "fix": "1445", "desc": "991"}, {"messageId": "986", "fix": "1446", "desc": "988"}, {"messageId": "989", "fix": "1447", "desc": "991"}, {"messageId": "986", "fix": "1448", "desc": "988"}, {"messageId": "989", "fix": "1449", "desc": "991"}, {"messageId": "986", "fix": "1450", "desc": "988"}, {"messageId": "989", "fix": "1451", "desc": "991"}, {"messageId": "986", "fix": "1452", "desc": "988"}, {"messageId": "989", "fix": "1453", "desc": "991"}, {"messageId": "986", "fix": "1454", "desc": "988"}, {"messageId": "989", "fix": "1455", "desc": "991"}, {"messageId": "986", "fix": "1456", "desc": "988"}, {"messageId": "989", "fix": "1457", "desc": "991"}, {"messageId": "986", "fix": "1458", "desc": "988"}, {"messageId": "989", "fix": "1459", "desc": "991"}, {"messageId": "986", "fix": "1460", "desc": "988"}, {"messageId": "989", "fix": "1461", "desc": "991"}, {"desc": "1462", "fix": "1463"}, {"messageId": "986", "fix": "1464", "desc": "988"}, {"messageId": "989", "fix": "1465", "desc": "991"}, {"messageId": "986", "fix": "1466", "desc": "988"}, {"messageId": "989", "fix": "1467", "desc": "991"}, {"messageId": "986", "fix": "1468", "desc": "988"}, {"messageId": "989", "fix": "1469", "desc": "991"}, {"messageId": "986", "fix": "1470", "desc": "988"}, {"messageId": "989", "fix": "1471", "desc": "991"}, {"messageId": "986", "fix": "1472", "desc": "988"}, {"messageId": "989", "fix": "1473", "desc": "991"}, {"messageId": "986", "fix": "1474", "desc": "988"}, {"messageId": "989", "fix": "1475", "desc": "991"}, {"messageId": "986", "fix": "1476", "desc": "988"}, {"messageId": "989", "fix": "1477", "desc": "991"}, {"messageId": "986", "fix": "1478", "desc": "988"}, {"messageId": "989", "fix": "1479", "desc": "991"}, {"messageId": "986", "fix": "1480", "desc": "988"}, {"messageId": "989", "fix": "1481", "desc": "991"}, {"messageId": "986", "fix": "1482", "desc": "988"}, {"messageId": "989", "fix": "1483", "desc": "991"}, {"messageId": "986", "fix": "1484", "desc": "988"}, {"messageId": "989", "fix": "1485", "desc": "991"}, {"messageId": "986", "fix": "1486", "desc": "988"}, {"messageId": "989", "fix": "1487", "desc": "991"}, {"messageId": "986", "fix": "1488", "desc": "988"}, {"messageId": "989", "fix": "1489", "desc": "991"}, {"messageId": "986", "fix": "1490", "desc": "988"}, {"messageId": "989", "fix": "1491", "desc": "991"}, {"messageId": "986", "fix": "1492", "desc": "988"}, {"messageId": "989", "fix": "1493", "desc": "991"}, {"messageId": "986", "fix": "1494", "desc": "988"}, {"messageId": "989", "fix": "1495", "desc": "991"}, {"messageId": "986", "fix": "1496", "desc": "988"}, {"messageId": "989", "fix": "1497", "desc": "991"}, {"messageId": "986", "fix": "1498", "desc": "988"}, {"messageId": "989", "fix": "1499", "desc": "991"}, {"messageId": "986", "fix": "1500", "desc": "988"}, {"messageId": "989", "fix": "1501", "desc": "991"}, "replaceWithAlt", {"alt": "1502"}, {"range": "1503", "text": "1504"}, "Replace with `&apos;`.", {"alt": "1505"}, {"range": "1506", "text": "1507"}, "Replace with `&lsquo;`.", {"alt": "1508"}, {"range": "1509", "text": "1510"}, "Replace with `&#39;`.", {"alt": "1511"}, {"range": "1512", "text": "1513"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "1514", "text": "1515"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1516", "text": "1517"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "1502"}, {"range": "1518", "text": "1519"}, {"alt": "1505"}, {"range": "1520", "text": "1521"}, {"alt": "1508"}, {"range": "1522", "text": "1523"}, {"alt": "1511"}, {"range": "1524", "text": "1525"}, {"range": "1526", "text": "1515"}, {"range": "1527", "text": "1517"}, {"range": "1528", "text": "1515"}, {"range": "1529", "text": "1517"}, {"range": "1530", "text": "1515"}, {"range": "1531", "text": "1517"}, {"range": "1532", "text": "1515"}, {"range": "1533", "text": "1517"}, {"range": "1534", "text": "1515"}, {"range": "1535", "text": "1517"}, {"range": "1536", "text": "1515"}, {"range": "1537", "text": "1517"}, {"range": "1538", "text": "1515"}, {"range": "1539", "text": "1517"}, {"range": "1540", "text": "1515"}, {"range": "1541", "text": "1517"}, {"range": "1542", "text": "1515"}, {"range": "1543", "text": "1517"}, {"range": "1544", "text": "1515"}, {"range": "1545", "text": "1517"}, {"range": "1546", "text": "1515"}, {"range": "1547", "text": "1517"}, {"range": "1548", "text": "1515"}, {"range": "1549", "text": "1517"}, {"alt": "1502"}, {"range": "1550", "text": "1551"}, {"alt": "1505"}, {"range": "1552", "text": "1553"}, {"alt": "1508"}, {"range": "1554", "text": "1555"}, {"alt": "1511"}, {"range": "1556", "text": "1557"}, {"alt": "1502"}, {"range": "1558", "text": "1559"}, {"alt": "1505"}, {"range": "1560", "text": "1561"}, {"alt": "1508"}, {"range": "1562", "text": "1563"}, {"alt": "1511"}, {"range": "1564", "text": "1565"}, {"range": "1566", "text": "1515"}, {"range": "1567", "text": "1517"}, {"range": "1568", "text": "1515"}, {"range": "1569", "text": "1517"}, {"range": "1570", "text": "1515"}, {"range": "1571", "text": "1517"}, {"range": "1572", "text": "1515"}, {"range": "1573", "text": "1517"}, {"range": "1574", "text": "1515"}, {"range": "1575", "text": "1517"}, {"range": "1576", "text": "1515"}, {"range": "1577", "text": "1517"}, {"alt": "1578"}, {"range": "1579", "text": "1580"}, "Replace with `&quot;`.", {"alt": "1581"}, {"range": "1582", "text": "1583"}, "Replace with `&ldquo;`.", {"alt": "1584"}, {"range": "1585", "text": "1586"}, "Replace with `&#34;`.", {"alt": "1587"}, {"range": "1588", "text": "1589"}, "Replace with `&rdquo;`.", {"alt": "1578"}, {"range": "1590", "text": "1591"}, {"alt": "1581"}, {"range": "1592", "text": "1593"}, {"alt": "1584"}, {"range": "1594", "text": "1595"}, {"alt": "1587"}, {"range": "1596", "text": "1597"}, {"alt": "1578"}, {"range": "1598", "text": "1599"}, {"alt": "1581"}, {"range": "1600", "text": "1601"}, {"alt": "1584"}, {"range": "1602", "text": "1603"}, {"alt": "1587"}, {"range": "1604", "text": "1605"}, {"alt": "1578"}, {"range": "1606", "text": "1607"}, {"alt": "1581"}, {"range": "1608", "text": "1609"}, {"alt": "1584"}, {"range": "1610", "text": "1611"}, {"alt": "1587"}, {"range": "1612", "text": "1613"}, {"range": "1614", "text": "1515"}, {"range": "1615", "text": "1517"}, {"range": "1616", "text": "1515"}, {"range": "1617", "text": "1517"}, {"range": "1618", "text": "1515"}, {"range": "1619", "text": "1517"}, {"range": "1620", "text": "1515"}, {"range": "1621", "text": "1517"}, {"range": "1622", "text": "1515"}, {"range": "1623", "text": "1517"}, {"range": "1624", "text": "1515"}, {"range": "1625", "text": "1517"}, {"alt": "1502"}, {"range": "1626", "text": "1627"}, {"alt": "1505"}, {"range": "1628", "text": "1629"}, {"alt": "1508"}, {"range": "1630", "text": "1631"}, {"alt": "1511"}, {"range": "1632", "text": "1633"}, {"alt": "1502"}, {"range": "1634", "text": "1635"}, {"alt": "1505"}, {"range": "1636", "text": "1637"}, {"alt": "1508"}, {"range": "1638", "text": "1639"}, {"alt": "1511"}, {"range": "1640", "text": "1641"}, {"alt": "1502"}, {"range": "1642", "text": "1643"}, {"alt": "1505"}, {"range": "1644", "text": "1645"}, {"alt": "1508"}, {"range": "1646", "text": "1647"}, {"alt": "1511"}, {"range": "1648", "text": "1649"}, {"alt": "1502"}, {"range": "1650", "text": "1651"}, {"alt": "1505"}, {"range": "1652", "text": "1653"}, {"alt": "1508"}, {"range": "1654", "text": "1655"}, {"alt": "1511"}, {"range": "1656", "text": "1657"}, {"alt": "1502"}, {"range": "1658", "text": "1659"}, {"alt": "1505"}, {"range": "1660", "text": "1661"}, {"alt": "1508"}, {"range": "1662", "text": "1663"}, {"alt": "1511"}, {"range": "1664", "text": "1665"}, {"alt": "1502"}, {"range": "1666", "text": "1667"}, {"alt": "1505"}, {"range": "1668", "text": "1669"}, {"alt": "1508"}, {"range": "1670", "text": "1671"}, {"alt": "1511"}, {"range": "1672", "text": "1673"}, {"alt": "1502"}, {"range": "1674", "text": "1675"}, {"alt": "1505"}, {"range": "1676", "text": "1677"}, {"alt": "1508"}, {"range": "1678", "text": "1679"}, {"alt": "1511"}, {"range": "1680", "text": "1681"}, {"alt": "1502"}, {"range": "1682", "text": "1683"}, {"alt": "1505"}, {"range": "1684", "text": "1685"}, {"alt": "1508"}, {"range": "1686", "text": "1687"}, {"alt": "1511"}, {"range": "1688", "text": "1689"}, {"alt": "1502"}, {"range": "1690", "text": "1691"}, {"alt": "1505"}, {"range": "1692", "text": "1693"}, {"alt": "1508"}, {"range": "1694", "text": "1695"}, {"alt": "1511"}, {"range": "1696", "text": "1697"}, {"alt": "1578"}, {"range": "1698", "text": "1699"}, {"alt": "1581"}, {"range": "1700", "text": "1701"}, {"alt": "1584"}, {"range": "1702", "text": "1703"}, {"alt": "1587"}, {"range": "1704", "text": "1705"}, {"alt": "1578"}, {"range": "1706", "text": "1707"}, {"alt": "1581"}, {"range": "1708", "text": "1709"}, {"alt": "1584"}, {"range": "1710", "text": "1711"}, {"alt": "1587"}, {"range": "1712", "text": "1713"}, {"alt": "1502"}, {"range": "1714", "text": "1715"}, {"alt": "1505"}, {"range": "1716", "text": "1717"}, {"alt": "1508"}, {"range": "1718", "text": "1719"}, {"alt": "1511"}, {"range": "1720", "text": "1721"}, {"alt": "1502"}, {"range": "1722", "text": "1723"}, {"alt": "1505"}, {"range": "1724", "text": "1725"}, {"alt": "1508"}, {"range": "1726", "text": "1727"}, {"alt": "1511"}, {"range": "1728", "text": "1729"}, {"alt": "1502"}, {"range": "1730", "text": "1731"}, {"alt": "1505"}, {"range": "1732", "text": "1733"}, {"alt": "1508"}, {"range": "1734", "text": "1735"}, {"alt": "1511"}, {"range": "1736", "text": "1737"}, {"alt": "1502"}, {"range": "1738", "text": "1739"}, {"alt": "1505"}, {"range": "1740", "text": "1741"}, {"alt": "1508"}, {"range": "1742", "text": "1743"}, {"alt": "1511"}, {"range": "1744", "text": "1745"}, {"alt": "1502"}, {"range": "1746", "text": "1747"}, {"alt": "1505"}, {"range": "1748", "text": "1749"}, {"alt": "1508"}, {"range": "1750", "text": "1751"}, {"alt": "1511"}, {"range": "1752", "text": "1753"}, {"alt": "1502"}, {"range": "1754", "text": "1755"}, {"alt": "1505"}, {"range": "1756", "text": "1757"}, {"alt": "1508"}, {"range": "1758", "text": "1759"}, {"alt": "1511"}, {"range": "1760", "text": "1761"}, {"range": "1762", "text": "1515"}, {"range": "1763", "text": "1517"}, {"alt": "1502"}, {"range": "1764", "text": "1765"}, {"alt": "1505"}, {"range": "1766", "text": "1767"}, {"alt": "1508"}, {"range": "1768", "text": "1769"}, {"alt": "1511"}, {"range": "1770", "text": "1771"}, {"alt": "1502"}, {"range": "1772", "text": "1773"}, {"alt": "1505"}, {"range": "1774", "text": "1775"}, {"alt": "1508"}, {"range": "1776", "text": "1777"}, {"alt": "1511"}, {"range": "1778", "text": "1779"}, {"alt": "1578"}, {"range": "1780", "text": "1580"}, {"alt": "1581"}, {"range": "1781", "text": "1583"}, {"alt": "1584"}, {"range": "1782", "text": "1586"}, {"alt": "1587"}, {"range": "1783", "text": "1589"}, {"alt": "1578"}, {"range": "1784", "text": "1591"}, {"alt": "1581"}, {"range": "1785", "text": "1593"}, {"alt": "1584"}, {"range": "1786", "text": "1595"}, {"alt": "1587"}, {"range": "1787", "text": "1597"}, {"alt": "1502"}, {"range": "1788", "text": "1789"}, {"alt": "1505"}, {"range": "1790", "text": "1791"}, {"alt": "1508"}, {"range": "1792", "text": "1793"}, {"alt": "1511"}, {"range": "1794", "text": "1795"}, {"range": "1796", "text": "1515"}, {"range": "1797", "text": "1517"}, {"alt": "1502"}, {"range": "1798", "text": "1799"}, {"alt": "1505"}, {"range": "1800", "text": "1801"}, {"alt": "1508"}, {"range": "1802", "text": "1803"}, {"alt": "1511"}, {"range": "1804", "text": "1805"}, {"alt": "1502"}, {"range": "1806", "text": "1807"}, {"alt": "1505"}, {"range": "1808", "text": "1809"}, {"alt": "1508"}, {"range": "1810", "text": "1811"}, {"alt": "1511"}, {"range": "1812", "text": "1813"}, {"range": "1814", "text": "1515"}, {"range": "1815", "text": "1517"}, {"alt": "1502"}, {"range": "1816", "text": "1817"}, {"alt": "1505"}, {"range": "1818", "text": "1819"}, {"alt": "1508"}, {"range": "1820", "text": "1821"}, {"alt": "1511"}, {"range": "1822", "text": "1823"}, {"alt": "1502"}, {"range": "1824", "text": "1825"}, {"alt": "1505"}, {"range": "1826", "text": "1827"}, {"alt": "1508"}, {"range": "1828", "text": "1829"}, {"alt": "1511"}, {"range": "1830", "text": "1831"}, {"range": "1832", "text": "1515"}, {"range": "1833", "text": "1517"}, {"alt": "1502"}, {"range": "1834", "text": "1835"}, {"alt": "1505"}, {"range": "1836", "text": "1837"}, {"alt": "1508"}, {"range": "1838", "text": "1839"}, {"alt": "1511"}, {"range": "1840", "text": "1841"}, {"range": "1842", "text": "1515"}, {"range": "1843", "text": "1517"}, {"alt": "1502"}, {"range": "1844", "text": "1845"}, {"alt": "1505"}, {"range": "1846", "text": "1847"}, {"alt": "1508"}, {"range": "1848", "text": "1849"}, {"alt": "1511"}, {"range": "1850", "text": "1851"}, {"alt": "1502"}, {"range": "1852", "text": "1853"}, {"alt": "1505"}, {"range": "1854", "text": "1855"}, {"alt": "1508"}, {"range": "1856", "text": "1857"}, {"alt": "1511"}, {"range": "1858", "text": "1859"}, {"range": "1860", "text": "1515"}, {"range": "1861", "text": "1517"}, {"alt": "1578"}, {"range": "1862", "text": "1863"}, {"alt": "1581"}, {"range": "1864", "text": "1865"}, {"alt": "1584"}, {"range": "1866", "text": "1867"}, {"alt": "1587"}, {"range": "1868", "text": "1869"}, {"alt": "1578"}, {"range": "1870", "text": "1871"}, {"alt": "1581"}, {"range": "1872", "text": "1873"}, {"alt": "1584"}, {"range": "1874", "text": "1875"}, {"alt": "1587"}, {"range": "1876", "text": "1877"}, {"alt": "1502"}, {"range": "1878", "text": "1789"}, {"alt": "1505"}, {"range": "1879", "text": "1791"}, {"alt": "1508"}, {"range": "1880", "text": "1793"}, {"alt": "1511"}, {"range": "1881", "text": "1795"}, {"alt": "1502"}, {"range": "1882", "text": "1883"}, {"alt": "1505"}, {"range": "1884", "text": "1885"}, {"alt": "1508"}, {"range": "1886", "text": "1887"}, {"alt": "1511"}, {"range": "1888", "text": "1889"}, {"alt": "1502"}, {"range": "1890", "text": "1891"}, {"alt": "1505"}, {"range": "1892", "text": "1893"}, {"alt": "1508"}, {"range": "1894", "text": "1895"}, {"alt": "1511"}, {"range": "1896", "text": "1897"}, {"alt": "1502"}, {"range": "1898", "text": "1899"}, {"alt": "1505"}, {"range": "1900", "text": "1901"}, {"alt": "1508"}, {"range": "1902", "text": "1903"}, {"alt": "1511"}, {"range": "1904", "text": "1905"}, {"alt": "1502"}, {"range": "1906", "text": "1907"}, {"alt": "1505"}, {"range": "1908", "text": "1909"}, {"alt": "1508"}, {"range": "1910", "text": "1911"}, {"alt": "1511"}, {"range": "1912", "text": "1913"}, {"alt": "1502"}, {"range": "1914", "text": "1915"}, {"alt": "1505"}, {"range": "1916", "text": "1917"}, {"alt": "1508"}, {"range": "1918", "text": "1919"}, {"alt": "1511"}, {"range": "1920", "text": "1921"}, {"range": "1922", "text": "1515"}, {"range": "1923", "text": "1517"}, {"alt": "1502"}, {"range": "1924", "text": "1925"}, {"alt": "1505"}, {"range": "1926", "text": "1927"}, {"alt": "1508"}, {"range": "1928", "text": "1929"}, {"alt": "1511"}, {"range": "1930", "text": "1931"}, {"alt": "1502"}, {"range": "1932", "text": "1933"}, {"alt": "1505"}, {"range": "1934", "text": "1935"}, {"alt": "1508"}, {"range": "1936", "text": "1937"}, {"alt": "1511"}, {"range": "1938", "text": "1939"}, {"alt": "1502"}, {"range": "1940", "text": "1941"}, {"alt": "1505"}, {"range": "1942", "text": "1943"}, {"alt": "1508"}, {"range": "1944", "text": "1945"}, {"alt": "1511"}, {"range": "1946", "text": "1947"}, {"range": "1948", "text": "1515"}, {"range": "1949", "text": "1517"}, {"alt": "1502"}, {"range": "1950", "text": "1951"}, {"alt": "1505"}, {"range": "1952", "text": "1953"}, {"alt": "1508"}, {"range": "1954", "text": "1955"}, {"alt": "1511"}, {"range": "1956", "text": "1957"}, {"range": "1958", "text": "1515"}, {"range": "1959", "text": "1517"}, {"range": "1960", "text": "1515"}, {"range": "1961", "text": "1517"}, {"range": "1962", "text": "1515"}, {"range": "1963", "text": "1517"}, {"range": "1964", "text": "1515"}, {"range": "1965", "text": "1517"}, {"range": "1966", "text": "1515"}, {"range": "1967", "text": "1517"}, {"range": "1968", "text": "1515"}, {"range": "1969", "text": "1517"}, {"range": "1970", "text": "1515"}, {"range": "1971", "text": "1517"}, {"range": "1972", "text": "1515"}, {"range": "1973", "text": "1517"}, {"range": "1974", "text": "1515"}, {"range": "1975", "text": "1517"}, "Update the dependencies array to be: [uploadFiles]", {"range": "1976", "text": "1977"}, {"range": "1978", "text": "1515"}, {"range": "1979", "text": "1517"}, {"range": "1980", "text": "1515"}, {"range": "1981", "text": "1517"}, {"range": "1982", "text": "1515"}, {"range": "1983", "text": "1517"}, {"range": "1984", "text": "1515"}, {"range": "1985", "text": "1517"}, {"range": "1986", "text": "1515"}, {"range": "1987", "text": "1517"}, {"range": "1988", "text": "1515"}, {"range": "1989", "text": "1517"}, {"range": "1990", "text": "1515"}, {"range": "1991", "text": "1517"}, {"range": "1992", "text": "1515"}, {"range": "1993", "text": "1517"}, {"range": "1994", "text": "1515"}, {"range": "1995", "text": "1517"}, {"range": "1996", "text": "1515"}, {"range": "1997", "text": "1517"}, {"range": "1998", "text": "1515"}, {"range": "1999", "text": "1517"}, {"range": "2000", "text": "1515"}, {"range": "2001", "text": "1517"}, {"range": "2002", "text": "1515"}, {"range": "2003", "text": "1517"}, {"range": "2004", "text": "1515"}, {"range": "2005", "text": "1517"}, {"range": "2006", "text": "1515"}, {"range": "2007", "text": "1517"}, {"range": "2008", "text": "1515"}, {"range": "2009", "text": "1517"}, {"range": "2010", "text": "1515"}, {"range": "2011", "text": "1517"}, {"range": "2012", "text": "1515"}, {"range": "2013", "text": "1517"}, {"range": "2014", "text": "1515"}, {"range": "2015", "text": "1517"}, "&apos;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&apos;s build something amazing together.\n              ", "&lsquo;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&lsquo;s build something amazing together.\n              ", "&#39;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&#39;s build something amazing together.\n              ", "&rsquo;", [12635, 12815], "\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let&rsquo;s build something amazing together.\n              ", [3978, 3981], "unknown", [3978, 3981], "never", [5375, 5481], "\n              Manage your company&apos;s about pages, team information, and process documentation\n            ", [5375, 5481], "\n              Manage your company&lsquo;s about pages, team information, and process documentation\n            ", [5375, 5481], "\n              Manage your company&#39;s about pages, team information, and process documentation\n            ", [5375, 5481], "\n              Manage your company&rsquo;s about pages, team information, and process documentation\n            ", [2937, 2940], [2937, 2940], [3905, 3908], [3905, 3908], [2814, 2817], [2814, 2817], [2587, 2590], [2587, 2590], [2654, 2657], [2654, 2657], [4957, 4960], [4957, 4960], [22338, 22341], [22338, 22341], [6761, 6764], [6761, 6764], [8090, 8093], [8090, 8093], [7350, 7353], [7350, 7353], [1580, 1583], [1580, 1583], [3038, 3041], [3038, 3041], [4827, 4920], "\n              Welcome back! Here&apos;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here&lsquo;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here&#39;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here&rsquo;s what's happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&apos;s happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&lsquo;s happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&#39;s happening with your business today.\n              ", [4827, 4920], "\n              Welcome back! Here's what&rsquo;s happening with your business today.\n              ", [4128, 4131], [4128, 4131], [4472, 4475], [4472, 4475], [5328, 5331], [5328, 5331], [2076, 2079], [2076, 2079], [6359, 6362], [6359, 6362], [4129, 4132], [4129, 4132], "&quot;", [18329, 18349], "\n                  &quot;", "&ldquo;", [18329, 18349], "\n                  &ldquo;", "&#34;", [18329, 18349], "\n                  &#34;", "&rdquo;", [18329, 18349], "\n                  &rdquo;", [18370, 18388], "&quot;\n                ", [18370, 18388], "&ldquo;\n                ", [18370, 18388], "&#34;\n                ", [18370, 18388], "&rdquo;\n                ", [21932, 21962], "\n                            &quot;", [21932, 21962], "\n                            &ldquo;", [21932, 21962], "\n                            &#34;", [21932, 21962], "\n                            &rdquo;", [21991, 22019], "&quot;\n                          ", [21991, 22019], "&ldquo;\n                          ", [21991, 22019], "&#34;\n                          ", [21991, 22019], "&rdquo;\n                          ", [3290, 3293], [3290, 3293], [703, 706], [703, 706], [707, 710], [707, 710], [7600, 7603], [7600, 7603], [707, 710], [707, 710], [4987, 4990], [4987, 4990], [3354, 3393], "\n                Let&apos;s Build Something ", [3354, 3393], "\n                Let&lsquo;s Build Something ", [3354, 3393], "\n                Let&#39;s Build Something ", [3354, 3393], "\n                Let&rsquo;s Build Something ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&apos;s discuss how we can help bring your vision to life.\n              ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&lsquo;s discuss how we can help bring your vision to life.\n              ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&#39;s discuss how we can help bring your vision to life.\n              ", [3749, 3928], "\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let&rsquo;s discuss how we can help bring your vision to life.\n              ", [6402, 6529], "\n                  Fill out the form below and we&apos;ll get back to you within 24 hours with a detailed proposal.\n                ", [6402, 6529], "\n                  Fill out the form below and we&lsquo;ll get back to you within 24 hours with a detailed proposal.\n                ", [6402, 6529], "\n                  Fill out the form below and we&#39;ll get back to you within 24 hours with a detailed proposal.\n                ", [6402, 6529], "\n                  Fill out the form below and we&rsquo;ll get back to you within 24 hours with a detailed proposal.\n                ", [7502, 7616], "\n                        Thank you for reaching out. We&apos;ll get back to you within 24 hours.\n                      ", [7502, 7616], "\n                        Thank you for reaching out. We&lsquo;ll get back to you within 24 hours.\n                      ", [7502, 7616], "\n                        Thank you for reaching out. We&#39;ll get back to you within 24 hours.\n                      ", [7502, 7616], "\n                        Thank you for reaching out. We&rsquo;ll get back to you within 24 hours.\n                      ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&apos;ve got you covered.\r\n              ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&lsquo;ve got you covered.\r\n              ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&#39;ve got you covered.\r\n              ", [9512, 9717], "\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we&rsquo;ve got you covered.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&apos;d love to discuss your unique requirements.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&lsquo;d love to discuss your unique requirements.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&#39;d love to discuss your unique requirements.\r\n              ", [11899, 12002], "\r\n                Need something custom? We&rsquo;d love to discuss your unique requirements.\r\n              ", [12874, 13065], "\r\n                Don&apos;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don&lsquo;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don&#39;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don&rsquo;t just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&apos;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&lsquo;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&#39;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here&rsquo;s what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&apos;ve achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&lsquo;ve achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&#39;ve achieved together.\r\n              ", [12874, 13065], "\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we&rsquo;ve achieved together.\r\n              ", [14510, 14533], "\r\n                    &quot;", [14510, 14533], "\r\n                    &ldquo;", [14510, 14533], "\r\n                    &#34;", [14510, 14533], "\r\n                    &rdquo;", [14554, 14575], "&quot;\r\n                  ", [14554, 14575], "&ldquo;\r\n                  ", [14554, 14575], "&#34;\r\n                  ", [14554, 14575], "&rdquo;\r\n                  ", [16544, 16584], "\r\n                Let&apos;s Build Something ", [16544, 16584], "\r\n                Let&lsquo;s Build Something ", [16544, 16584], "\r\n                Let&#39;s Build Something ", [16544, 16584], "\r\n                Let&rsquo;s Build Something ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&apos;s discuss how we can help your business grow.\r\n              ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&lsquo;s discuss how we can help your business grow.\r\n              ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&#39;s discuss how we can help your business grow.\r\n              ", [16727, 16902], "\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let&rsquo;s discuss how we can help your business grow.\r\n              ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&apos;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&lsquo;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&#39;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [7181, 7393], "\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we&rsquo;ve helped businesses transform their ideas into powerful digital experiences.\n            ", [17318, 17459], "\n            Let&apos;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [17318, 17459], "\n            Let&lsquo;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [17318, 17459], "\n            Let&#39;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [17318, 17459], "\n            Let&rsquo;s discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          ", [9341, 9511], "\n                Explore our successful projects and see how we&apos;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [9341, 9511], "\n                Explore our successful projects and see how we&lsquo;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [9341, 9511], "\n                Explore our successful projects and see how we&#39;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [9341, 9511], "\n                Explore our successful projects and see how we&rsquo;ve helped businesses\n                transform their ideas into powerful digital solutions.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&apos;s discuss your requirements\n                and create something amazing together.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&lsquo;s discuss your requirements\n                and create something amazing together.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&#39;s discuss your requirements\n                and create something amazing together.\n              ", [18555, 18716], "\n                Join our portfolio of successful projects. Let&rsquo;s discuss your requirements\n                and create something amazing together.\n              ", [516, 519], [516, 519], [6830, 6875], "The project you&apos;re looking for doesn't exist.", [6830, 6875], "The project you&lsquo;re looking for doesn't exist.", [6830, 6875], "The project you&#39;re looking for doesn't exist.", [6830, 6875], "The project you&rsquo;re looking for doesn't exist.", [6830, 6875], "The project you're looking for doesn&apos;t exist.", [6830, 6875], "The project you're looking for doesn&lsquo;t exist.", [6830, 6875], "The project you're looking for doesn&#39;t exist.", [6830, 6875], "The project you're looking for doesn&rsquo;t exist.", [17857, 17877], [17857, 17877], [17857, 17877], [17857, 17877], [17906, 17924], [17906, 17924], [17906, 17924], [17906, 17924], [19185, 19312], "\n                Let&apos;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [19185, 19312], "\n                Let&lsquo;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [19185, 19312], "\n                Let&#39;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [19185, 19312], "\n                Let&rsquo;s discuss your requirements and create a custom solution that drives your business forward.\n              ", [5914, 5917], [5914, 5917], [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&apos;ve got you covered.\n              ", [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&lsquo;ve got you covered.\n              ", [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&#39;ve got you covered.\n              ", [7452, 7645], "\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we&rsquo;ve got you covered.\n              ", [15122, 15273], "\n                Let&apos;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [15122, 15273], "\n                Let&lsquo;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [15122, 15273], "\n                Let&#39;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [15122, 15273], "\n                Let&rsquo;s discuss your project requirements and create a custom solution\n                that drives your business forward.\n              ", [572, 575], [572, 575], [5666, 5711], "The service you&apos;re looking for doesn't exist.", [5666, 5711], "The service you&lsquo;re looking for doesn't exist.", [5666, 5711], "The service you&#39;re looking for doesn't exist.", [5666, 5711], "The service you&rsquo;re looking for doesn't exist.", [5666, 5711], "The service you're looking for doesn&apos;t exist.", [5666, 5711], "The service you're looking for doesn&lsquo;t exist.", [5666, 5711], "The service you're looking for doesn&#39;t exist.", [5666, 5711], "The service you're looking for doesn&rsquo;t exist.", [5975, 5978], [5975, 5978], [13044, 13118], "\n                Here are the key benefits you&apos;ll get when you choose our ", [13044, 13118], "\n                Here are the key benefits you&lsquo;ll get when you choose our ", [13044, 13118], "\n                Here are the key benefits you&#39;ll get when you choose our ", [13044, 13118], "\n                Here are the key benefits you&rsquo;ll get when you choose our ", [14914, 14917], [14914, 14917], [16476, 16574], "\n                  Don&apos;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don&lsquo;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don&#39;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don&rsquo;t just take our word for it. Here's what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&apos;s what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&lsquo;s what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&#39;s what our clients have to say about our ", [16476, 16574], "\n                  Don't just take our word for it. Here&rsquo;s what our clients have to say about our ", [16789, 16792], [16789, 16792], [17409, 17433], "\n                      &quot;", [17409, 17433], "\n                      &ldquo;", [17409, 17433], "\n                      &#34;", [17409, 17433], "\n                      &rdquo;", [17454, 17476], "&quot;\n                    ", [17454, 17476], "&ldquo;\n                    ", [17454, 17476], "&#34;\n                    ", [17454, 17476], "&rdquo;\n                    ", [18461, 18588], [18461, 18588], [18461, 18588], [18461, 18588], [8428, 8621], "\n              We&apos;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [8428, 8621], "\n              We&lsquo;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [8428, 8621], "\n              We&#39;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [8428, 8621], "\n              We&rsquo;re a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            ", [20418, 20616], "\n            We&apos;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We&lsquo;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We&#39;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We&rsquo;re always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&apos;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&lsquo;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&#39;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you&rsquo;re ready to make an impact, we'd love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&apos;d love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&lsquo;d love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&#39;d love to hear from you.\n          ", [20418, 20616], "\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we&rsquo;d love to hear from you.\n          ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&apos;s discuss your requirements and find the best solution.\n              ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&lsquo;s discuss your requirements and find the best solution.\n              ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&#39;s discuss your requirements and find the best solution.\n              ", [22151, 22337], "\n                Our experts can help you select the perfect technology stack for your project.\n                Let&rsquo;s discuss your requirements and find the best solution.\n              ", [499, 502], [499, 502], [5129, 5177], "The technology you&apos;re looking for doesn't exist.", [5129, 5177], "The technology you&lsquo;re looking for doesn't exist.", [5129, 5177], "The technology you&#39;re looking for doesn't exist.", [5129, 5177], "The technology you&rsquo;re looking for doesn't exist.", [5129, 5177], "The technology you're looking for doesn&apos;t exist.", [5129, 5177], "The technology you're looking for doesn&lsquo;t exist.", [5129, 5177], "The technology you're looking for doesn&#39;t exist.", [5129, 5177], "The technology you're looking for doesn&rsquo;t exist.", [16685, 16743], "\n                  See how we&apos;ve successfully implemented ", [16685, 16743], "\n                  See how we&lsquo;ve successfully implemented ", [16685, 16743], "\n                  See how we&#39;ve successfully implemented ", [16685, 16743], "\n                  See how we&rsquo;ve successfully implemented ", [16956, 16959], [16956, 16959], [18599, 18634], "\n                Let&apos;s discuss how ", [18599, 18634], "\n                Let&lsquo;s discuss how ", [18599, 18634], "\n                Let&#39;s discuss how ", [18599, 18634], "\n                Let&rsquo;s discuss how ", [194, 197], [194, 197], [309, 312], [309, 312], [3092, 3095], [3092, 3095], [3174, 3177], [3174, 3177], [723, 726], [723, 726], [4106, 4109], [4106, 4109], [776, 779], [776, 779], [713, 716], [713, 716], [721, 724], [721, 724], [1860, 1862], "[uploadFiles]", [107, 110], [107, 110], [137, 140], [137, 140], [530, 533], [530, 533], [3818, 3821], [3818, 3821], [5635, 5638], [5635, 5638], [203, 206], [203, 206], [420, 423], [420, 423], [835, 838], [835, 838], [1585, 1588], [1585, 1588], [2036, 2039], [2036, 2039], [2408, 2411], [2408, 2411], [4575, 4578], [4575, 4578], [5119, 5122], [5119, 5122], [7664, 7667], [7664, 7667], [1414, 1417], [1414, 1417], [2038, 2041], [2038, 2041], [3173, 3176], [3173, 3176], [3862, 3865], [3862, 3865], [4415, 4418], [4415, 4418]]