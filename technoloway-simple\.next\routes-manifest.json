{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/projects/[id]", "regex": "^/api/projects/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/projects/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/services/[id]", "regex": "^/api/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/services/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/projects/[slug]", "regex": "^/projects/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/projects/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/services/[slug]", "regex": "^/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/services/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/technologies/[slug]", "regex": "^/technologies/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/technologies/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/about-pages", "regex": "^/admin/about\\-pages(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/about\\-pages(?:/)?$"}, {"page": "/admin/blog", "regex": "^/admin/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/blog(?:/)?$"}, {"page": "/admin/chatbot", "regex": "^/admin/chatbot(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/chatbot(?:/)?$"}, {"page": "/admin/clients", "regex": "^/admin/clients(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/clients(?:/)?$"}, {"page": "/admin/contact-forms", "regex": "^/admin/contact\\-forms(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/contact\\-forms(?:/)?$"}, {"page": "/admin/data-upload", "regex": "^/admin/data\\-upload(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/data\\-upload(?:/)?$"}, {"page": "/admin/hero-sections", "regex": "^/admin/hero\\-sections(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/hero\\-sections(?:/)?$"}, {"page": "/admin/invoices", "regex": "^/admin/invoices(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/invoices(?:/)?$"}, {"page": "/admin/jobs", "regex": "^/admin/jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/jobs(?:/)?$"}, {"page": "/admin/legal-pages", "regex": "^/admin/legal\\-pages(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/legal\\-pages(?:/)?$"}, {"page": "/admin/projects", "regex": "^/admin/projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/projects(?:/)?$"}, {"page": "/admin/services", "regex": "^/admin/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/services(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/team-members", "regex": "^/admin/team\\-members(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/team\\-members(?:/)?$"}, {"page": "/admin/technologies", "regex": "^/admin/technologies(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/technologies(?:/)?$"}, {"page": "/admin/testimonials", "regex": "^/admin/testimonials(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/testimonials(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/portfolio", "regex": "^/portfolio(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio(?:/)?$"}, {"page": "/projects", "regex": "^/projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/projects(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/team", "regex": "^/team(?:/)?$", "routeKeys": {}, "namedRegex": "^/team(?:/)?$"}, {"page": "/technologies", "regex": "^/technologies(?:/)?$", "routeKeys": {}, "namedRegex": "^/technologies(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}