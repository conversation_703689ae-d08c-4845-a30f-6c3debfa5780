{"/_not-found/page": "app/_not-found/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/blog/route": "app/api/blog/route.js", "/api/blog/[id]/route": "app/api/blog/[id]/route.js", "/api/clients/route": "app/api/clients/route.js", "/api/contact/route": "app/api/contact/route.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/api/services/[id]/route": "app/api/services/[id]/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/services/route": "app/api/services/route.js", "/api/team/route": "app/api/team/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/technologies/route": "app/api/technologies/route.js", "/about/page": "app/about/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/blog/page": "app/blog/page.js", "/contact/page": "app/contact/page.js", "/page": "app/page.js", "/portfolio/page": "app/portfolio/page.js", "/projects/page": "app/projects/page.js", "/projects/[slug]/page": "app/projects/[slug]/page.js", "/services/[slug]/page": "app/services/[slug]/page.js", "/services/page": "app/services/page.js", "/team/page": "app/team/page.js", "/technologies/[slug]/page": "app/technologies/[slug]/page.js", "/technologies/page": "app/technologies/page.js", "/admin/blog/page": "app/admin/blog/page.js", "/admin/about-pages/page": "app/admin/about-pages/page.js", "/admin/chatbot/page": "app/admin/chatbot/page.js", "/admin/contact-forms/page": "app/admin/contact-forms/page.js", "/admin/clients/page": "app/admin/clients/page.js", "/admin/data-upload/page": "app/admin/data-upload/page.js", "/admin/hero-sections/page": "app/admin/hero-sections/page.js", "/admin/invoices/page": "app/admin/invoices/page.js", "/admin/jobs/page": "app/admin/jobs/page.js", "/admin/page": "app/admin/page.js", "/admin/projects/page": "app/admin/projects/page.js", "/admin/services/page": "app/admin/services/page.js", "/admin/legal-pages/page": "app/admin/legal-pages/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/team-members/page": "app/admin/team-members/page.js", "/admin/testimonials/page": "app/admin/testimonials/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/technologies/page": "app/admin/technologies/page.js"}