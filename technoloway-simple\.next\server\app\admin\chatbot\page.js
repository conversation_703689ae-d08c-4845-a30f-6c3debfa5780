(()=>{var e={};e.id=1010,e.ids=[1010],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5326:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(60687),i=t(43210),r=t(26001),n=t(45994),l=t(17712),d=t(14566),c=t(64908),o=t(50515),m=t(58089),x=t(59168),h=t(57891),u=t(71178),p=t(26403);let g={totalConversations:1247,activeUsers:89,avgResponseTime:2.3,satisfactionRate:94.2,resolvedQueries:1156,escalatedQueries:91},v=[{id:1,userId:"user_123",userName:"<PERSON>",startTime:"2024-01-22T14:30:00Z",endTime:"2024-01-22T14:45:00Z",messageCount:8,status:"Resolved",topic:"Pricing Information",satisfaction:5,escalated:!1},{id:2,userId:"user_456",userName:"Sarah Johnson",startTime:"2024-01-22T13:15:00Z",endTime:"2024-01-22T13:35:00Z",messageCount:12,status:"Escalated",topic:"Technical Support",satisfaction:null,escalated:!0},{id:3,userId:"user_789",userName:"Mike Chen",startTime:"2024-01-22T12:00:00Z",endTime:"2024-01-22T12:10:00Z",messageCount:5,status:"Resolved",topic:"Service Inquiry",satisfaction:4,escalated:!1}],f=[{id:1,question:"What services do you offer?",answer:"We offer web development, mobile app development, cloud solutions, and digital consulting services.",category:"Services",usage:156,lastUpdated:"2024-01-20T10:00:00Z",isActive:!0},{id:2,question:"How much does a website cost?",answer:"Website costs vary based on complexity. Basic websites start at $5,000, while complex applications can range from $15,000 to $50,000+.",category:"Pricing",usage:203,lastUpdated:"2024-01-18T14:30:00Z",isActive:!0},{id:3,question:"What is your development process?",answer:"Our process includes discovery, planning, design, development, testing, and deployment phases with regular client communication.",category:"Process",usage:89,lastUpdated:"2024-01-15T09:15:00Z",isActive:!0},{id:4,question:"Do you provide ongoing support?",answer:"Yes, we offer various support packages including maintenance, updates, and technical support for all our projects.",category:"Support",usage:134,lastUpdated:"2024-01-12T16:45:00Z",isActive:!0}],j=["All","Services","Pricing","Process","Support","Technical"];function w(){let[e,s]=(0,i.useState)("overview"),[t,w]=(0,i.useState)("All"),[y,b]=(0,i.useState)(null),[N,k]=(0,i.useState)(!1),T=f.filter(e=>"All"===t||e.category===t),A=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),C=(e,s)=>{let t=Math.floor((new Date(s).getTime()-new Date(e).getTime())/6e4);return`${t} min`},P=e=>[void 0,void 0,void 0,void 0,void 0].map((s,t)=>(0,a.jsx)("span",{className:`text-sm ${t<e?"text-yellow-400":"text-gray-300"}`,children:"★"},t));return(0,a.jsx)("div",{className:"py-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Chatbot Management"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Monitor chatbot performance, manage conversations, and update knowledge base"})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",name:"Overview",icon:n.A},{id:"conversations",name:"Conversations",icon:l.A},{id:"knowledge",name:"Knowledge Base",icon:d.A}].map(t=>{let i=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${e===t.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,a.jsx)(i,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:t.name})]},t.id)})})}),"overview"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"h-5 w-5 text-white"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Conversations"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:g.totalConversations})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-white"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Users"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:g.activeUsers})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"h-5 w-5 text-white"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Avg Response Time"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[g.avgResponseTime,"s"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"h-5 w-5 text-white"})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Satisfaction Rate"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[g.satisfactionRate,"%"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-600 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:g.resolvedQueries})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Resolved Queries"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Auto-resolved"})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:g.escalatedQueries})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Escalated"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"To Human"})]})})]})})})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Recent Activity"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Resolved 15 queries in the last hour"}),(0,a.jsx)("span",{className:"text-gray-400",children:"2 minutes ago"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Knowledge base updated with 3 new entries"}),(0,a.jsx)("span",{className:"text-gray-400",children:"1 hour ago"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-600",children:"2 conversations escalated to human agents"}),(0,a.jsx)("span",{className:"text-gray-400",children:"3 hours ago"})]})]})]})})]}),"conversations"===e&&(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Recent Conversations"}),(0,a.jsx)("div",{className:"overflow-hidden",children:(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:v.map((e,s)=>(0,a.jsx)(r.P.li,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*s},className:"py-4 cursor-pointer hover:bg-gray-50",onClick:()=>b(e),children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${"Resolved"===e.status?"bg-green-100":"bg-yellow-100"}`,children:"Resolved"===e.status?(0,a.jsx)(m.A,{className:"w-6 h-6 text-green-600"}):(0,a.jsx)(x.A,{className:"w-6 h-6 text-yellow-600"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"Resolved"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:e.status}),e.escalated&&(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"Escalated"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mb-1",children:[(0,a.jsxs)("span",{children:["Topic: ",e.topic]}),(0,a.jsxs)("span",{children:[e.messageCount," messages"]}),(0,a.jsxs)("span",{children:["Duration: ",C(e.startTime,e.endTime)]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Started: ",A(e.startTime)]}),e.satisfaction&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:"Rating:"}),P(e.satisfaction)]})]})]})]})})},e.id))})})]})}),"knowledge"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Knowledge Base"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage chatbot responses and frequently asked questions"})]}),(0,a.jsxs)("button",{onClick:()=>k(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(h.A,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Entry"]})]}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:j.map(e=>(0,a.jsx)("button",{onClick:()=>w(e),className:`px-4 py-2 rounded-full text-sm font-medium transition-colors ${t===e?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:e},e))})})}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,a.jsx)("div",{className:"space-y-4",children:T.map((e,s)=>(0,a.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*s},className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.question}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.category})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.answer}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Used ",e.usage," times"]}),(0,a.jsxs)("span",{children:["Updated: ",A(e.lastUpdated)]}),(0,a.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:e.isActive?"Active":"Inactive"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(u.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(p.A,{className:"h-5 w-5"})})]})]})},e.id))})})})]}),y&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:()=>b(null)}),(0,a.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full",children:[(0,a.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Conversation Details"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:y.userName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Topic"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:y.topic})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,a.jsx)("span",{className:`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"Resolved"===y.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:y.status})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Duration"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:C(y.startTime,y.endTime)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Messages"}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[y.messageCount," messages exchanged"]})]}),y.satisfaction&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User Satisfaction"}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-1",children:[P(y.satisfaction),(0,a.jsxs)("span",{className:"ml-2 text-sm text-gray-600",children:["(",y.satisfaction,"/5)"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Timeline"}),(0,a.jsxs)("div",{className:"mt-1 text-sm text-gray-900",children:[(0,a.jsxs)("p",{children:["Started: ",A(y.startTime)]}),(0,a.jsxs)("p",{children:["Ended: ",A(y.endTime)]})]})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm",children:"View Full Transcript"}),(0,a.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm",onClick:()=>b(null),children:"Close"})]})]})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19745:(e,s,t)=>{Promise.resolve().then(t.bind(t,23492))},23492:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Project By AI\\\\Technoloway\\\\Technoloway (Processing)\\\\technoloway-simple\\\\src\\\\app\\\\admin\\\\chatbot\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\chatbot\\page.tsx","default")},26403:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(43210);let i=a.forwardRef(function({title:e,titleId:s,...t},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45994:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(43210);let i=a.forwardRef(function({title:e,titleId:s,...t},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},50515:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(43210);let i=a.forwardRef(function({title:e,titleId:s,...t},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},56193:(e,s,t)=>{Promise.resolve().then(t.bind(t,5326))},57104:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=t(65239),i=t(48088),r=t(88170),n=t.n(r),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["admin",{children:["chatbot",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23492)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\chatbot\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\chatbot\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/chatbot/page",pathname:"/admin/chatbot",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},57891:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(43210);let i=a.forwardRef(function({title:e,titleId:s,...t},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},58089:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(43210);let i=a.forwardRef(function({title:e,titleId:s,...t},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},59168:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(43210);let i=a.forwardRef(function({title:e,titleId:s,...t},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71178:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(43210);let i=a.forwardRef(function({title:e,titleId:s,...t},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,9945,6818,5154],()=>t(57104));module.exports=a})();