(()=>{var e={};e.id=3419,e.ids=[3419],e.modules={2969:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9826:(e,t,s)=>{Promise.resolve().then(s.bind(s,72097))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26403:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},27611:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Project By AI\\\\Technoloway\\\\Technoloway (Processing)\\\\technoloway-simple\\\\src\\\\app\\\\admin\\\\invoices\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\invoices\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["invoices",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,27611)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\invoices\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\invoices\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/invoices/page",pathname:"/admin/invoices",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},50515:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},57891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},58089:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},59168:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},69658:(e,t,s)=>{Promise.resolve().then(s.bind(s,27611))},71178:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},72097:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(60687),r=s(43210),n=s(26001),i=s(58089),l=s(50515),o=s(59168),d=s(61245),c=s(57891),m=s(30922),u=s(2969),x=s(66524),h=s(71178),p=s(91164),f=s(26403);let g=[{id:1,invoiceNumber:"INV-2024-001",clientName:"GreenTech Solutions",clientEmail:"<EMAIL>",projectName:"EcoCommerce Platform",amount:25e3,currency:"USD",status:"Paid",issueDate:"2024-01-15T00:00:00Z",dueDate:"2024-02-14T00:00:00Z",paidDate:"2024-01-28T00:00:00Z",description:"Development of e-commerce platform - Phase 1",items:[{description:"Frontend Development",quantity:80,rate:150,amount:12e3},{description:"Backend Development",quantity:60,rate:160,amount:9600},{description:"UI/UX Design",quantity:40,rate:120,amount:4800},{description:"Project Management",quantity:20,rate:100,amount:2e3}],taxRate:.08,taxAmount:2e3,totalAmount:27e3,notes:"Payment received via wire transfer",createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-28T14:30:00Z"},{id:2,invoiceNumber:"INV-2024-002",clientName:"MedTech Innovations",clientEmail:"<EMAIL>",projectName:"HealthTracker Mobile App",amount:18e3,currency:"USD",status:"Pending",issueDate:"2024-01-20T00:00:00Z",dueDate:"2024-02-19T00:00:00Z",paidDate:null,description:"Mobile app development - Final phase",items:[{description:"Mobile Development",quantity:100,rate:140,amount:14e3},{description:"Testing & QA",quantity:30,rate:100,amount:3e3},{description:"App Store Deployment",quantity:10,rate:100,amount:1e3}],taxRate:.08,taxAmount:1440,totalAmount:19440,notes:"Net 30 payment terms",createdAt:"2024-01-20T11:00:00Z",updatedAt:"2024-01-22T16:45:00Z"},{id:3,invoiceNumber:"INV-2024-003",clientName:"FinanceFlow Corp",clientEmail:"<EMAIL>",projectName:"Financial Dashboard",amount:35e3,currency:"USD",status:"Overdue",issueDate:"2024-01-05T00:00:00Z",dueDate:"2024-01-20T00:00:00Z",paidDate:null,description:"Custom financial analytics dashboard",items:[{description:"Dashboard Development",quantity:120,rate:160,amount:19200},{description:"Data Integration",quantity:80,rate:150,amount:12e3},{description:"Security Implementation",quantity:40,rate:180,amount:7200}],taxRate:.08,taxAmount:3072,totalAmount:41472,notes:"Follow up required - payment overdue",createdAt:"2024-01-05T09:00:00Z",updatedAt:"2024-01-25T13:20:00Z"},{id:4,invoiceNumber:"INV-2024-004",clientName:"EcoCommerce",clientEmail:"<EMAIL>",projectName:"Website Maintenance",amount:5e3,currency:"USD",status:"Draft",issueDate:"2024-01-25T00:00:00Z",dueDate:"2024-02-24T00:00:00Z",paidDate:null,description:"Monthly maintenance and support",items:[{description:"Website Maintenance",quantity:20,rate:120,amount:2400},{description:"Content Updates",quantity:15,rate:100,amount:1500},{description:"Performance Optimization",quantity:10,rate:150,amount:1500}],taxRate:.08,taxAmount:432,totalAmount:5432,notes:"Monthly recurring service",createdAt:"2024-01-25T14:00:00Z",updatedAt:"2024-01-25T14:00:00Z"}],v=["All","Draft","Pending","Paid","Overdue","Cancelled"],j=e=>{switch(e){case"Paid":return"bg-green-100 text-green-800";case"Pending":return"bg-yellow-100 text-yellow-800";case"Overdue":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"Paid":return i.A;case"Pending":default:return l.A;case"Overdue":return o.A;case"Draft":return d.A}};function w(){let[e,t]=(0,r.useState)(g),[s,l]=(0,r.useState)(""),[o,w]=(0,r.useState)("All"),[b,N]=(0,r.useState)(null),[A,k]=(0,r.useState)(!1),D=e.filter(e=>{let t=e.invoiceNumber.toLowerCase().includes(s.toLowerCase())||e.clientName.toLowerCase().includes(s.toLowerCase())||e.projectName.toLowerCase().includes(s.toLowerCase()),a="All"===o||e.status===o;return t&&a}),P=e=>{confirm("Are you sure you want to delete this invoice?")&&t(t=>t.filter(t=>t.id!==e))},C=(e,t="USD")=>new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:0}).format(e),E=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"Not set",T=(e,t)=>"Paid"!==t&&new Date(e)<new Date,L=(()=>{let t=e.reduce((e,t)=>e+t.totalAmount,0),s=e.filter(e=>"Paid"===e.status).reduce((e,t)=>e+t.totalAmount,0);return{total:t,paid:s,pending:e.filter(e=>"Pending"===e.status).reduce((e,t)=>e+t.totalAmount,0),overdue:e.filter(e=>"Overdue"===e.status).reduce((e,t)=>e+t.totalAmount,0)}})();return(0,a.jsx)("div",{className:"py-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,a.jsxs)("div",{className:"md:flex md:items-center md:justify-between mb-8",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Invoices"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage client invoices, payments, and billing information"})]}),(0,a.jsx)("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:(0,a.jsxs)("button",{onClick:()=>k(!0),className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(c.A,{className:"-ml-1 mr-2 h-5 w-5"}),"Create Invoice"]})})]}),(0,a.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-white text-sm font-bold",children:[C(L.total/1e3),"K"]})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Invoiced"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"All Time"})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-white text-sm font-bold",children:[C(L.paid/1e3),"K"]})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Paid"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Received"})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-white text-sm font-bold",children:[C(L.pending/1e3),"K"]})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Outstanding"})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-white text-sm font-bold",children:[C(L.overdue/1e3),"K"]})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Overdue"}),(0,a.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Past Due"})]})})]})})})]}),(0,a.jsx)("div",{className:"mb-6 bg-white shadow rounded-lg",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",placeholder:"Search invoices...",value:s,onChange:e=>l(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,a.jsx)("div",{children:(0,a.jsx)("select",{value:o,onChange:e=>w(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:v.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})})]})})}),(0,a.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:D.map((e,t)=>{let s=y(e.status);return(0,a.jsx)(n.P.li,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},children:(0,a.jsx)("div",{className:"px-4 py-4 sm:px-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:`w-10 h-10 rounded-lg flex items-center justify-center ${j(e.status)}`,children:(0,a.jsx)(s,{className:"w-6 h-6"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.invoiceNumber}),(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${j(e.status)}`,children:e.status}),T(e.dueDate,e.status)&&(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"Overdue"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mb-2",children:[(0,a.jsx)("span",{children:e.clientName}),(0,a.jsx)("span",{children:e.projectName}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:C(e.totalAmount)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Issued: ",E(e.issueDate)]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Due: ",E(e.dueDate)]}),e.paidDate&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-1 text-green-500"}),"Paid: ",E(e.paidDate)]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("button",{onClick:()=>N(e),className:"text-gray-400 hover:text-blue-600 transition-colors",title:"View Details",children:(0,a.jsx)(x.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",title:"Edit",children:(0,a.jsx)(h.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",title:"Download PDF",children:(0,a.jsx)(p.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>P(e.id),className:"text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:(0,a.jsx)(f.A,{className:"h-5 w-5"})})]})]})})},e.id)})})}),0===D.length&&(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No invoices found"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Try adjusting your search or filter criteria."})]})}),b&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:()=>N(null)}),(0,a.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:[(0,a.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-96 overflow-y-auto",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:["Invoice ",b.invoiceNumber]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Client"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:b.clientName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Project"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:b.projectName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,a.jsx)("span",{className:`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${j(b.status)}`,children:b.status})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Total Amount"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900 font-semibold",children:C(b.totalAmount)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Line Items"}),(0,a.jsx)("div",{className:"mt-1 overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Description"}),(0,a.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Qty"}),(0,a.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Rate"}),(0,a.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Amount"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.items.map((e,t)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900",children:e.description}),(0,a.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900",children:e.quantity}),(0,a.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900",children:C(e.rate)}),(0,a.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900",children:C(e.amount)})]},t))})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Issue Date"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:E(b.issueDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Due Date"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:E(b.dueDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Paid Date"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:E(b.paidDate)})]})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm",children:"Edit Invoice"}),(0,a.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm",onClick:()=>N(null),children:"Close"})]})]})]})})]})})}},79551:e=>{"use strict";e.exports=require("url")},91164:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,9945,6818,5154],()=>s(30440));module.exports=a})();