(()=>{var e={};e.id=3321,e.ids=[3321],e.modules={2969:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8103:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(60687),a=s(43210),n=s(26001),l=s(57891),i=s(30922),o=s(2969),d=s(93635),c=s(31082),m=s(50515),u=s(66524),x=s(71178),h=s(26403);let p=[{id:1,name:"EcoCommerce Platform",client:"GreenTech Solutions",status:"In Progress",priority:"High",startDate:"2024-01-15",endDate:"2024-06-30",budget:85e3,spent:45e3,progress:65,teamSize:5,description:"E-commerce platform for sustainable products with advanced analytics.",technologies:["React","Node.js","PostgreSQL","AWS"],manager:"Sarah Johnson"},{id:2,name:"HealthTracker Mobile App",client:"MedTech Innovations",status:"Completed",priority:"Medium",startDate:"2023-09-01",endDate:"2024-01-15",budget:65e3,spent:62e3,progress:100,teamSize:4,description:"Cross-platform mobile app for health monitoring and fitness tracking.",technologies:["React Native","Firebase","Node.js"],manager:"Mike Chen"},{id:3,name:"FinanceFlow Dashboard",client:"FinanceFlow Corp",status:"Planning",priority:"High",startDate:"2024-03-01",endDate:"2024-08-15",budget:12e4,spent:5e3,progress:5,teamSize:6,description:"Real-time financial analytics dashboard with AI-powered insights.",technologies:["Next.js","Python","TensorFlow","PostgreSQL"],manager:"Emily Davis"},{id:4,name:"EduConnect Learning Platform",client:"EduTech Academy",status:"In Progress",priority:"Medium",startDate:"2024-02-01",endDate:"2024-07-30",budget:95e3,spent:35e3,progress:40,teamSize:5,description:"Online learning platform with interactive courses and assessments.",technologies:["Vue.js","Laravel","MySQL","Docker"],manager:"David Rodriguez"}],g=["All","Planning","In Progress","Completed","On Hold","Cancelled"],f=["All","Low","Medium","High","Critical"],j=e=>{switch(e){case"Completed":return"bg-green-100 text-green-800";case"In Progress":return"bg-blue-100 text-blue-800";case"Planning":return"bg-yellow-100 text-yellow-800";case"On Hold":default:return"bg-gray-100 text-gray-800";case"Cancelled":return"bg-red-100 text-red-800"}},v=e=>{switch(e){case"Critical":return"bg-red-100 text-red-800";case"High":return"bg-orange-100 text-orange-800";case"Medium":return"bg-yellow-100 text-yellow-800";case"Low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};function w(){let[e,t]=(0,a.useState)(""),[s,w]=(0,a.useState)("All"),[b,y]=(0,a.useState)("All"),N=p.filter(t=>{let r=t.name.toLowerCase().includes(e.toLowerCase())||t.client.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase()),a="All"===s||t.status===s,n="All"===b||t.priority===b;return r&&a&&n}),k=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(e),P=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,r.jsx)("div",{className:"py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,r.jsxs)("div",{className:"md:flex md:items-center md:justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Projects"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your projects, track progress, and monitor budgets"})]}),(0,r.jsx)("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:(0,r.jsxs)("button",{className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(l.A,{className:"-ml-1 mr-2 h-5 w-5"}),"New Project"]})})]}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:p.length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Projects"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"All Time"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:p.filter(e=>"In Progress"===e.status).length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Projects"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"In Progress"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:p.filter(e=>"Completed"===e.status).length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Completed"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Delivered"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-white text-sm font-bold",children:[k(p.reduce((e,t)=>e+t.budget,0)/1e3),"K"]})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Budget"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"All Projects"})]})})]})})})]}),(0,r.jsx)("div",{className:"mt-6 bg-white shadow rounded-lg",children:(0,r.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(i.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search projects...",value:e,onChange:e=>t(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("select",{value:s,onChange:e=>w(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:g.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})}),(0,r.jsx)("div",{children:(0,r.jsx)("select",{value:b,onChange:e=>y(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:f.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})})]})})}),(0,r.jsx)("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:N.map((e,t)=>(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.client})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${j(e.status)}`,children:e.status}),(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${v(e.priority)}`,children:e.priority})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.description}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[e.progress,"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${e.progress}%`}})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:[P(e.startDate)," - ",P(e.endDate)]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:[e.teamSize," members"]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:[k(e.spent)," / ",k(e.budget)]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:e.manager})]})]}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:e.technologies.map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800",children:e},e))})}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(h.A,{className:"h-5 w-5"})})]})]})},e.id))}),0===N.length&&(0,r.jsx)("div",{className:"mt-6 bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No projects found"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Try adjusting your search or filter criteria."})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20924:(e,t,s)=>{Promise.resolve().then(s.bind(s,28729))},26403:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},28729:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Project By AI\\\\Technoloway\\\\Technoloway (Processing)\\\\technoloway-simple\\\\src\\\\app\\\\admin\\\\projects\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\projects\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},33873:e=>{"use strict";e.exports=require("path")},44724:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28729)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\projects\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\projects\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/projects/page",pathname:"/admin/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},50515:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},57891:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},71178:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},79068:(e,t,s)=>{Promise.resolve().then(s.bind(s,8103))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,9945,7707,9180,5154],()=>s(44724));module.exports=r})();