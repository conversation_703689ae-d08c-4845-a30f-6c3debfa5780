(()=>{var e={};e.id=6255,e.ids=[6255],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13369:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),i=s(43210),n=s(26001),a=s(96374),l=s(66829),c=s(48544),o=s(14566),d=s(50942),m=s(45994),u=s(57891),x=s(66524),h=s(71178),p=s(26403);let g=[{id:1,name:"Web Development",description:"Modern, responsive web applications built with the latest technologies and best practices.",shortDescription:"Custom web applications and websites",icon:"CodeBracketIcon",features:["React/Next.js","TypeScript","Tailwind CSS","API Integration"],pricing:{startingPrice:5e3,currency:"USD",billingType:"project"},category:"Development",isActive:!0,isFeatured:!0,order:1,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-20T14:30:00Z"},{id:2,name:"Mobile Development",description:"Native and cross-platform mobile applications for iOS and Android with seamless user experiences.",shortDescription:"iOS and Android mobile applications",icon:"DevicePhoneMobileIcon",features:["React Native","Flutter","iOS/Android","App Store Deployment"],pricing:{startingPrice:8e3,currency:"USD",billingType:"project"},category:"Development",isActive:!0,isFeatured:!0,order:2,createdAt:"2024-01-12T11:00:00Z",updatedAt:"2024-01-18T16:45:00Z"},{id:3,name:"Cloud Solutions",description:"Scalable cloud infrastructure and deployment solutions for modern applications with high availability.",shortDescription:"Cloud infrastructure and DevOps",icon:"CloudIcon",features:["AWS/Azure/GCP","Docker/Kubernetes","CI/CD Pipelines","Monitoring"],pricing:{startingPrice:3e3,currency:"USD",billingType:"monthly"},category:"Infrastructure",isActive:!0,isFeatured:!1,order:3,createdAt:"2024-01-10T09:00:00Z",updatedAt:"2024-01-15T13:20:00Z"},{id:4,name:"API Development",description:"Robust and scalable APIs and microservices architecture for enterprise applications.",shortDescription:"RESTful APIs and microservices",icon:"CogIcon",features:["REST/GraphQL","Microservices","Database Design","Documentation"],pricing:{startingPrice:4e3,currency:"USD",billingType:"project"},category:"Development",isActive:!0,isFeatured:!1,order:4,createdAt:"2024-01-08T14:00:00Z",updatedAt:"2024-01-12T10:15:00Z"},{id:5,name:"Security & Testing",description:"Comprehensive security audits and automated testing solutions to ensure application reliability.",shortDescription:"Security audits and quality assurance",icon:"ShieldCheckIcon",features:["Security Audits","Automated Testing","Code Review","Performance"],pricing:{startingPrice:2500,currency:"USD",billingType:"project"},category:"Quality Assurance",isActive:!0,isFeatured:!1,order:5,createdAt:"2024-01-05T16:00:00Z",updatedAt:"2024-01-10T12:30:00Z"},{id:6,name:"Analytics & Insights",description:"Data-driven insights and analytics solutions for business growth and decision making.",shortDescription:"Business intelligence and analytics",icon:"ChartBarIcon",features:["Data Analytics","Business Intelligence","Reporting","Dashboards"],pricing:{startingPrice:3500,currency:"USD",billingType:"project"},category:"Analytics",isActive:!1,isFeatured:!1,order:6,createdAt:"2024-01-03T12:00:00Z",updatedAt:"2024-01-08T15:45:00Z"}],f=["All","Development","Infrastructure","Quality Assurance","Analytics"],v={CodeBracketIcon:a.A,DevicePhoneMobileIcon:l.A,CloudIcon:c.A,CogIcon:o.A,ShieldCheckIcon:d.A,ChartBarIcon:m.A};function y(){let[e,t]=(0,i.useState)(g),[s,a]=(0,i.useState)("All"),[l,c]=(0,i.useState)(null),[d,m]=(0,i.useState)(!1),y=e.filter(e=>"All"===s||e.category===s),b=e=>{t(t=>t.map(t=>t.id===e?{...t,isActive:!t.isActive}:t))},j=e=>{confirm("Are you sure you want to delete this service?")&&t(t=>t.filter(t=>t.id!==e))},w=e=>{let{startingPrice:t,currency:s,billingType:r}=e,i=new Intl.NumberFormat("en-US",{style:"currency",currency:s,minimumFractionDigits:0}).format(t);return`${i}${"monthly"===r?"/month":""}`},N=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,r.jsx)("div",{className:"py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,r.jsxs)("div",{className:"md:flex md:items-center md:justify-between mb-8",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Services"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your service offerings, pricing, and features"})]}),(0,r.jsx)("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:(0,r.jsxs)("button",{onClick:()=>m(!0),className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(u.A,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Service"]})})]}),(0,r.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:e.length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Services"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Available"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:e.filter(e=>e.isActive).length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Live"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:e.filter(e=>e.isFeatured).length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Featured"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Highlighted"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:f.length-1})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Categories"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Types"})]})})]})})})]}),(0,r.jsx)("div",{className:"mb-6 bg-white shadow rounded-lg",children:(0,r.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:f.map(e=>(0,r.jsx)("button",{onClick:()=>a(e),className:`px-4 py-2 rounded-full text-sm font-medium transition-colors ${s===e?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:e},e))})})}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3",children:y.map((e,t)=>{let s=v[e.icon]||o.A;return(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(s,{className:"w-6 h-6 text-blue-600"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.category})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[e.isActive&&(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Active"}),e.isFeatured&&(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"Featured"})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.shortDescription}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Features"}),(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:w(e.pricing)})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.features.slice(0,3).map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800",children:e},e)),e.features.length>3&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800",children:["+",e.features.length-3," more"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,r.jsxs)("span",{children:["Order: ",e.order]}),(0,r.jsxs)("span",{children:["Updated: ",N(e.updatedAt)]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>c(e),className:"text-gray-400 hover:text-blue-600 transition-colors",title:"View Details",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",title:"Edit",children:(0,r.jsx)(h.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>b(e.id),className:`text-gray-400 hover:text-green-600 transition-colors ${e.isActive?"text-green-600":""}`,title:e.isActive?"Deactivate":"Activate",children:(0,r.jsx)(o.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>j(e.id),className:"text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})]})},e.id)})}),0===y.length&&(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No services found"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"All"===s?"Get started by creating your first service.":`No services found in the ${s} category.`}),"All"===s&&(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("button",{onClick:()=>m(!0),className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(u.A,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Service"]})})]})}),l&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:()=>c(null)}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full",children:[(0,r.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsx)("div",{className:"sm:flex sm:items-start",children:(0,r.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:l.name}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:l.description})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Category"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:l.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Pricing"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:w(l.pricing)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Features"}),(0,r.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:l.features.map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800",children:e},e))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:l.isActive?"Active":"Inactive"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Featured"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:l.isFeatured?"Yes":"No"})]})]})]})]})})}),(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm",children:"Edit Service"}),(0,r.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm",onClick:()=>c(null),children:"Close"})]})]})]})})]})})}},13662:(e,t,s)=>{Promise.resolve().then(s.bind(s,13369))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26379:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Project By AI\\\\Technoloway\\\\Technoloway (Processing)\\\\technoloway-simple\\\\src\\\\app\\\\admin\\\\services\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\services\\page.tsx","default")},26403:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45994:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},48544:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))})},50454:(e,t,s)=>{Promise.resolve().then(s.bind(s,26379))},50942:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},57891:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},61436:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=s(65239),i=s(48088),n=s(88170),a=s.n(n),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["admin",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,26379)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\services\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/services/page",pathname:"/admin/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},66829:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"}))})},71178:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},79551:e=>{"use strict";e.exports=require("url")},96374:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(43210);let i=r.forwardRef(function({title:e,titleId:t,...s},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,9945,7707,9180,5154],()=>s(61436));module.exports=r})();