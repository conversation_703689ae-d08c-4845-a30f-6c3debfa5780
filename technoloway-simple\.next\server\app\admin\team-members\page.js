(()=>{var e={};e.id=7072,e.ids=[7072],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5270:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["team-members",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,14552)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\team-members\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\team-members\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/team-members/page",pathname:"/admin/team-members",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14552:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Project By AI\\\\Technoloway\\\\Technoloway (Processing)\\\\technoloway-simple\\\\src\\\\app\\\\admin\\\\team-members\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\admin\\team-members\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26403:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40638:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(60687),a=s(43210),n=s(26001),i=s(57891),l=s(30922),o=s(66524),d=s(71178),c=s(26403);let m=[{id:1,name:"Sarah Johnson",role:"CEO & Co-Founder",department:"Leadership",email:"<EMAIL>",phone:"+****************",location:"San Francisco, CA",joinDate:"2020-01-15",status:"Active",image:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",skills:["Strategic Planning","Team Leadership","Product Vision"],bio:"Visionary leader with 15+ years in tech."},{id:2,name:"Mike Chen",role:"CTO & Co-Founder",department:"Engineering",email:"<EMAIL>",phone:"+****************",location:"San Francisco, CA",joinDate:"2020-01-15",status:"Active",image:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",skills:["System Architecture","Cloud Computing","DevOps"],bio:"Full-stack architect passionate about scalable systems."},{id:3,name:"Emily Davis",role:"Lead Frontend Developer",department:"Engineering",email:"<EMAIL>",phone:"+****************",location:"Austin, TX",joinDate:"2021-03-10",status:"Active",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",skills:["React","TypeScript","UI/UX Design"],bio:"UI/UX enthusiast who crafts beautiful user experiences."},{id:4,name:"David Rodriguez",role:"Senior Backend Developer",department:"Engineering",email:"<EMAIL>",phone:"+****************",location:"Miami, FL",joinDate:"2021-06-20",status:"Active",image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",skills:["Node.js","Python","PostgreSQL"],bio:"Database wizard and API architect."}],h=["All","Leadership","Engineering","Security","Analytics","Infrastructure"],u=["All","Active","Inactive","On Leave"];function x(){let[e,t]=(0,a.useState)(""),[s,x]=(0,a.useState)("All"),[p,g]=(0,a.useState)("All"),[f,v]=(0,a.useState)(!1),j=m.filter(t=>{let r=t.name.toLowerCase().includes(e.toLowerCase())||t.role.toLowerCase().includes(e.toLowerCase())||t.email.toLowerCase().includes(e.toLowerCase()),a="All"===s||t.department===s,n="All"===p||t.status===p;return r&&a&&n});return(0,r.jsx)("div",{className:"py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,r.jsxs)("div",{className:"md:flex md:items-center md:justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Team Members"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your team members, roles, and information"})]}),(0,r.jsx)("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:(0,r.jsxs)("button",{onClick:()=>v(!0),className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(i.A,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Member"]})})]}),(0,r.jsx)("div",{className:"mt-6 bg-white shadow rounded-lg",children:(0,r.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(l.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search members...",value:e,onChange:e=>t(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("select",{value:s,onChange:e=>x(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:h.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})}),(0,r.jsx)("div",{children:(0,r.jsx)("select",{value:p,onChange:e=>g(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:u.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})})]})})}),(0,r.jsx)("div",{className:"mt-6 bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:j.map((e,t)=>(0,r.jsx)(n.P.li,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},children:(0,r.jsxs)("div",{className:"px-4 py-4 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-12 w-12",children:(0,r.jsx)("img",{className:"h-12 w-12 rounded-full object-cover",src:e.image,alt:e.name})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("span",{className:`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"Active"===e.status?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:e.status})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.role," • ",e.department]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email," • ",e.location]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(o.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e.bio}),(0,r.jsxs)("div",{className:"mt-2 flex flex-wrap gap-1",children:[e.skills.slice(0,3).map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800",children:e},e)),e.skills.length>3&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800",children:["+",e.skills.length-3," more"]})]})]})]})},e.id))})}),0===j.length&&(0,r.jsx)("div",{className:"mt-6 bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No team members found"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Try adjusting your search or filter criteria."})]})}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-3",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:m.length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Members"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Active Team"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:h.length-1})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Departments"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Active Depts"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:m.filter(e=>"Active"===e.status).length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Members"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Currently Working"})]})})]})})})]})]})})}},57891:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63215:(e,t,s)=>{Promise.resolve().then(s.bind(s,14552))},66524:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},71178:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},71463:(e,t,s)=>{Promise.resolve().then(s.bind(s,40638))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,9945,6818,5154],()=>s(5270));module.exports=r})();