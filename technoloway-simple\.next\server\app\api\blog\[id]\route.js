(()=>{var t={};t.id=8832,t.ids=[8832],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:t=>{"use strict";t.exports=require("querystring")},12412:t=>{"use strict";t.exports=require("assert")},28354:t=>{"use strict";t.exports=require("util")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(t,e,i)=>{"use strict";i.d(e,{z:()=>n});var r=i(96330);let n=globalThis.prisma??new r.PrismaClient},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(t,e,i)=>{"use strict";i.d(e,{FB:()=>m,Hx:()=>c,S:()=>p,ZT:()=>f,du:()=>x,hD:()=>o,oC:()=>z,r6:()=>s,vA:()=>d,vS:()=>u,yX:()=>g,z9:()=>h,zJ:()=>b});var r=i(32190),n=i(45697),a=i(96330);class o extends Error{constructor(t,e=500,i){super(t),this.message=t,this.statusCode=e,this.code=i,this.name="ApiError"}}function s(t,e,i=200){return r.NextResponse.json({success:!0,data:t,message:e},{status:i})}function l(t,e=500,i){let n=t instanceof Error?t.message:t;return r.NextResponse.json({success:!1,error:n,code:i},{status:e})}function u(t,e,i,n,a){let o=Math.ceil(n/i);return r.NextResponse.json({success:!0,data:t,message:a,pagination:{page:e,limit:i,total:n,totalPages:o}})}function p(t){return async e=>{try{let i=await e.json();return t.parse(i)}catch(t){if(t instanceof n.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");throw new o(`Validation error: ${e}`,400,"VALIDATION_ERROR")}throw new o("Invalid request body",400,"INVALID_BODY")}}}function d(t){let{searchParams:e}=new URL(t.url);return{page:parseInt(e.get("page")||"1"),limit:Math.min(parseInt(e.get("limit")||"10"),100),search:e.get("search")||void 0,sortBy:e.get("sortBy")||void 0,sortOrder:e.get("sortOrder")||"desc",filter:e.get("filter")||void 0}}function c(t,e){return{skip:(t-1)*e,take:e}}function m(t){return async(e,i)=>{try{return await t(e,i)}catch(t){if(console.error("API Error:",t),t instanceof o)return l(t.message,t.statusCode,t.code);if(t instanceof n.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");return l(`Validation error: ${e}`,400,"VALIDATION_ERROR")}if(t instanceof a.Prisma.PrismaClientKnownRequestError)switch(t.code){case"P2002":return l("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return l("Record not found",404,"NOT_FOUND");case"P2003":return l("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return l("Database error occurred",500,"DATABASE_ERROR")}return l("Internal server error",500,"INTERNAL_ERROR")}}}function g(t,e){if(!e.includes(t.method))throw new o(`Method ${t.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function z(t){let{getServerSession:e}=await i.e(5426).then(i.bind(i,35426)),{authOptions:r}=await Promise.all([i.e(1024),i.e(2909)]).then(i.bind(i,12909)),n=await e(r);if(!n||!n.user)throw new o("Authentication required",401,"UNAUTHORIZED");return{id:n.user.id,email:n.user.email,role:n.user.role,name:n.user.name}}async function f(t){let e=await z(t);if("ADMIN"!==e.role)throw new o("Admin access required",403,"FORBIDDEN");return e}function h(t){return t.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function b(t,e){return t?{OR:e.map(e=>({[e]:{contains:t,mode:"insensitive"}}))}:{}}function x(t,e="desc"){return t?{[t]:e}:{createdAt:e}}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:t=>{"use strict";t.exports=require("zlib")},78335:()=>{},79428:t=>{"use strict";t.exports=require("buffer")},79551:t=>{"use strict";t.exports=require("url")},81630:t=>{"use strict";t.exports=require("http")},85463:(t,e,i)=>{"use strict";i.d(e,{$b:()=>z,AF:()=>p,Gn:()=>f,N6:()=>c,Or:()=>s,Wc:()=>m,ZC:()=>h,Zi:()=>o,c5:()=>a,j6:()=>n,mQ:()=>g,nx:()=>d,ue:()=>u,xc:()=>l});var r=i(45697);r.z.object({email:r.z.string().email(),firstName:r.z.string().optional(),lastName:r.z.string().optional(),imageUrl:r.z.string().url().optional(),role:r.z.enum(["ADMIN","USER","CLIENT"]).default("USER")}).partial();let n=r.z.object({categoryId:r.z.string(),name:r.z.string().min(1).max(255),description:r.z.string().min(1),iconClass:r.z.string().max(100).optional(),price:r.z.number().positive(),discountRate:r.z.number().int().min(0).max(100).optional(),totalDiscount:r.z.number().optional(),manager:r.z.string().max(50).optional(),isActive:r.z.boolean().default(!0),displayOrder:r.z.number().int().default(0)}),a=n.partial(),o=r.z.object({orderId:r.z.string(),clientId:r.z.string().optional(),name:r.z.string().min(1),description:r.z.string().min(1),goals:r.z.string().optional(),manager:r.z.string().max(10).optional(),startDate:r.z.date().optional(),completionDate:r.z.date().optional(),estimatedCost:r.z.number().positive().optional(),estimatedTime:r.z.string().max(10).optional(),estimatedEffort:r.z.string().max(10).optional(),status:r.z.enum(["PLANNING","IN_PROGRESS","COMPLETED","ON_HOLD","CANCELLED"]).default("PLANNING"),isFeatured:r.z.boolean().default(!1),displayOrder:r.z.number().int().default(0),imageUrl:r.z.string().url().optional(),projectUrl:r.z.string().url().optional(),githubUrl:r.z.string().url().optional()}),s=o.partial(),l=r.z.object({userId:r.z.string().optional(),companyName:r.z.string().min(1).max(200),contactName:r.z.string().min(1).max(100),contactPosition:r.z.string().max(100).optional(),contactEmail:r.z.string().email().max(100),contactPhone:r.z.string().max(20).optional(),contactFax:r.z.string().max(20).optional(),companyWebsite:r.z.string().max(100).optional(),address:r.z.string().min(1).max(200),city:r.z.string().min(1).max(100),state:r.z.string().min(1).max(50),zipCode:r.z.string().min(1).max(20),country:r.z.string().min(1).max(100),logoUrl:r.z.string().max(500).optional(),notes:r.z.string().optional()}),u=l.partial(),p=r.z.object({authorId:r.z.string(),title:r.z.string().min(1).max(255),slug:r.z.string().min(1).max(255),content:r.z.string().min(1),excerpt:r.z.string().optional(),featuredImageUrl:r.z.string().max(500).optional(),isPublished:r.z.boolean().default(!1),publishedAt:r.z.date().optional(),categories:r.z.string().optional(),tags:r.z.string().optional()}),d=p.partial(),c=r.z.object({firstName:r.z.string().min(1),lastName:r.z.string().min(1),position:r.z.string().min(1),department:r.z.string().optional(),phone:r.z.string().min(1).max(12),email:r.z.string().email().max(255).optional(),salary:r.z.number().positive().optional(),payrollMethod:r.z.string().max(10).optional(),resumeUrl:r.z.string().max(500).optional(),notes:r.z.string().optional(),bio:r.z.string().optional(),photoUrl:r.z.string().max(500).optional(),linkedInUrl:r.z.string().max(500).optional(),twitterUrl:r.z.string().max(500).optional(),githubUrl:r.z.string().max(500).optional(),displayOrder:r.z.number().int().default(0),isActive:r.z.boolean().default(!0)}),m=c.partial(),g=r.z.object({name:r.z.string().min(1),description:r.z.string().optional(),iconUrl:r.z.string().url().optional(),displayOrder:r.z.number().int().default(0),isActive:r.z.boolean().default(!0)}),z=g.partial();r.z.object({clientName:r.z.string().min(1).max(100),clientTitle:r.z.string().min(1).max(100),clientCompany:r.z.string().min(1).max(100),clientPhotoUrl:r.z.string().max(500).optional(),content:r.z.string().min(1),rating:r.z.number().int().min(1).max(5).default(5),isFeatured:r.z.boolean().default(!1),displayOrder:r.z.number().int().default(0)}).partial();let f=r.z.object({userId:r.z.string().optional(),name:r.z.string().min(1),email:r.z.string().email(),phone:r.z.string().optional(),company:r.z.string().optional(),subject:r.z.string().min(1),message:r.z.string().min(1),status:r.z.enum(["NEW","IN_PROGRESS","RESOLVED","CLOSED"]).default("NEW")}),h=f.partial();r.z.object({name:r.z.string().min(1),description:r.z.string().optional(),parentId:r.z.string().optional(),isActive:r.z.boolean().default(!0),displayOrder:r.z.number().int().default(0)}).partial(),r.z.object({clientId:r.z.string(),orderNumber:r.z.string().min(1),description:r.z.string().optional(),totalAmount:r.z.number().positive(),status:r.z.enum(["PENDING","CONFIRMED","IN_PROGRESS","COMPLETED","CANCELLED"]).default("PENDING"),orderDate:r.z.date().default(()=>new Date)}).partial(),r.z.object({clientId:r.z.string(),projectId:r.z.string().optional(),orderId:r.z.string().optional(),contractId:r.z.string().optional(),invoiceNumber:r.z.string().min(1),description:r.z.string().optional(),subtotal:r.z.number().positive(),taxAmount:r.z.number().default(0),totalAmount:r.z.number().positive(),status:r.z.enum(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),issueDate:r.z.date().default(()=>new Date),dueDate:r.z.date(),paidAt:r.z.date().optional()}).partial(),r.z.object({title:r.z.string().min(1),description:r.z.string().min(1),requirements:r.z.string().min(1),location:r.z.string().min(1),employmentType:r.z.string().min(1),salaryMin:r.z.number().positive().optional(),salaryMax:r.z.number().positive().optional(),salaryCurrency:r.z.string().default("USD"),isRemote:r.z.boolean().default(!1),isActive:r.z.boolean().default(!0),expiresAt:r.z.date().optional()}).partial()},88812:(t,e,i)=>{"use strict";i.r(e),i.d(e,{patchFetch:()=>b,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>f});var r={};i.r(r),i.d(r,{DELETE:()=>c,GET:()=>p,PATCH:()=>m,PUT:()=>d});var n=i(96559),a=i(48088),o=i(37719),s=i(31183),l=i(53171),u=i(85463);let p=(0,l.FB)(async(t,{params:e})=>{let{id:i}=e,r=await s.z.blogPost.findUnique({where:{id:i},include:{author:{select:{id:!0,firstName:!0,lastName:!0,email:!0,imageUrl:!0}}}});if(!r)throw new l.hD("Blog post not found",404,"NOT_FOUND");return(0,l.r6)(r)}),d=(0,l.FB)(async(t,{params:e})=>{await (0,l.ZT)(t),(0,l.yX)(t,["PUT"]);let{id:i}=e,r=(0,l.S)(u.nx),n=await r(t),a=await s.z.blogPost.findUnique({where:{id:i}});if(!a)throw new l.hD("Blog post not found",404,"NOT_FOUND");if(n.title&&!n.slug&&(n.slug=(0,l.z9)(n.title)),n.slug&&n.slug!==a.slug&&await s.z.blogPost.findFirst({where:{slug:n.slug,id:{not:i}}})){let t=1,e=`${n.slug}-${t}`;for(;await s.z.blogPost.findFirst({where:{slug:e,id:{not:i}}});)t++,e=`${n.slug}-${t}`;n.slug=e}if(n.authorId&&!await s.z.user.findUnique({where:{id:n.authorId}}))throw new l.hD("Author not found",404,"AUTHOR_NOT_FOUND");let o=await s.z.blogPost.update({where:{id:i},data:n,include:{author:{select:{id:!0,firstName:!0,lastName:!0,email:!0,imageUrl:!0}}}});return(0,l.r6)(o,"Blog post updated successfully")}),c=(0,l.FB)(async(t,{params:e})=>{await (0,l.ZT)(t),(0,l.yX)(t,["DELETE"]);let{id:i}=e;if(!await s.z.blogPost.findUnique({where:{id:i}}))throw new l.hD("Blog post not found",404,"NOT_FOUND");return await s.z.blogPost.delete({where:{id:i}}),(0,l.r6)(null,"Blog post deleted successfully")}),m=(0,l.FB)(async(t,{params:e})=>{await (0,l.ZT)(t),(0,l.yX)(t,["PATCH"]);let{id:i}=e,r=await t.json(),n=await s.z.blogPost.findUnique({where:{id:i}});if(!n)throw new l.hD("Blog post not found",404,"NOT_FOUND");let a={};for(let t of["isPublished","publishedAt"])t in r&&("isPublished"!==t||!0!==r[t]||n.publishedAt||(a.publishedAt=new Date),a[t]=r[t]);if(0===Object.keys(a).length)throw new l.hD("No valid fields to update",400,"NO_VALID_FIELDS");let o=await s.z.blogPost.update({where:{id:i},data:a,include:{author:{select:{id:!0,firstName:!0,lastName:!0}}}});return(0,l.r6)(o,"Blog post updated successfully")}),g=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/blog/[id]/route",pathname:"/api/blog/[id]",filename:"route",bundlePath:"app/api/blog/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\blog\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:z,workUnitAsyncStorage:f,serverHooks:h}=g;function b(){return(0,o.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:f})}},94735:t=>{"use strict";t.exports=require("events")},96330:t=>{"use strict";t.exports=require("@prisma/client")},96487:()=>{}};var e=require("../../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),r=e.X(0,[4243,580,5697],()=>i(88812));module.exports=r})();