(()=>{var t={};t.id=2898,t.ids=[2898],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(t,e,i)=>{"use strict";i.d(e,{z:()=>r});var n=i(96330);let r=globalThis.prisma??new n.PrismaClient},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(t,e,i)=>{"use strict";i.d(e,{FB:()=>p,Hx:()=>m,S:()=>u,ZT:()=>f,du:()=>E,hD:()=>o,r6:()=>s,vA:()=>d,vS:()=>c,yX:()=>z,zJ:()=>y});var n=i(32190),r=i(45697),a=i(96330);class o extends Error{constructor(t,e=500,i){super(t),this.message=t,this.statusCode=e,this.code=i,this.name="ApiError"}}function s(t,e,i=200){return n.NextResponse.json({success:!0,data:t,message:e},{status:i})}function l(t,e=500,i){let r=t instanceof Error?t.message:t;return n.NextResponse.json({success:!1,error:r,code:i},{status:e})}function c(t,e,i,r,a){let o=Math.ceil(r/i);return n.NextResponse.json({success:!0,data:t,message:a,pagination:{page:e,limit:i,total:r,totalPages:o}})}function u(t){return async e=>{try{let i=await e.json();return t.parse(i)}catch(t){if(t instanceof r.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");throw new o(`Validation error: ${e}`,400,"VALIDATION_ERROR")}throw new o("Invalid request body",400,"INVALID_BODY")}}}function d(t){let{searchParams:e}=new URL(t.url);return{page:parseInt(e.get("page")||"1"),limit:Math.min(parseInt(e.get("limit")||"10"),100),search:e.get("search")||void 0,sortBy:e.get("sortBy")||void 0,sortOrder:e.get("sortOrder")||"desc",filter:e.get("filter")||void 0}}function m(t,e){return{skip:(t-1)*e,take:e}}function p(t){return async(e,i)=>{try{return await t(e,i)}catch(t){if(console.error("API Error:",t),t instanceof o)return l(t.message,t.statusCode,t.code);if(t instanceof r.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");return l(`Validation error: ${e}`,400,"VALIDATION_ERROR")}if(t instanceof a.Prisma.PrismaClientKnownRequestError)switch(t.code){case"P2002":return l("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return l("Record not found",404,"NOT_FOUND");case"P2003":return l("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return l("Database error occurred",500,"DATABASE_ERROR")}return l("Internal server error",500,"INTERNAL_ERROR")}}}function z(t,e){if(!e.includes(t.method))throw new o(`Method ${t.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function g(t){if(!t.headers.get("authorization"))throw new o("Authentication required",401,"UNAUTHORIZED");return{id:"admin-user-id",email:"<EMAIL>",role:"ADMIN"}}async function f(t){let e=await g(t);if("ADMIN"!==e.role)throw new o("Admin access required",403,"FORBIDDEN");return e}function y(t,e){return t?{OR:e.map(e=>({[e]:{contains:t,mode:"insensitive"}}))}:{}}function E(t,e="desc"){return t?{[t]:e}:{createdAt:e}}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},85463:(t,e,i)=>{"use strict";i.d(e,{Or:()=>s,Zi:()=>o,c5:()=>a,j6:()=>r,ue:()=>c,xc:()=>l});var n=i(45697);n.z.object({email:n.z.string().email(),firstName:n.z.string().optional(),lastName:n.z.string().optional(),imageUrl:n.z.string().url().optional(),role:n.z.enum(["ADMIN","USER","CLIENT"]).default("USER")}).partial();let r=n.z.object({categoryId:n.z.string(),name:n.z.string().min(1).max(255),description:n.z.string().min(1),iconClass:n.z.string().max(100).optional(),price:n.z.number().positive(),discountRate:n.z.number().int().min(0).max(100).optional(),totalDiscount:n.z.number().optional(),manager:n.z.string().max(50).optional(),isActive:n.z.boolean().default(!0),displayOrder:n.z.number().int().default(0)}),a=r.partial(),o=n.z.object({orderId:n.z.string(),clientId:n.z.string().optional(),name:n.z.string().min(1),description:n.z.string().min(1),goals:n.z.string().optional(),manager:n.z.string().max(10).optional(),startDate:n.z.date().optional(),completionDate:n.z.date().optional(),estimatedCost:n.z.number().positive().optional(),estimatedTime:n.z.string().max(10).optional(),estimatedEffort:n.z.string().max(10).optional(),status:n.z.enum(["PLANNING","IN_PROGRESS","COMPLETED","ON_HOLD","CANCELLED"]).default("PLANNING"),isFeatured:n.z.boolean().default(!1),displayOrder:n.z.number().int().default(0),imageUrl:n.z.string().url().optional(),projectUrl:n.z.string().url().optional(),githubUrl:n.z.string().url().optional()}),s=o.partial(),l=n.z.object({userId:n.z.string().optional(),companyName:n.z.string().min(1).max(200),contactName:n.z.string().min(1).max(100),contactPosition:n.z.string().max(100).optional(),contactEmail:n.z.string().email().max(100),contactPhone:n.z.string().max(20).optional(),contactFax:n.z.string().max(20).optional(),companyWebsite:n.z.string().max(100).optional(),address:n.z.string().min(1).max(200),city:n.z.string().min(1).max(100),state:n.z.string().min(1).max(50),zipCode:n.z.string().min(1).max(20),country:n.z.string().min(1).max(100),logoUrl:n.z.string().max(500).optional(),notes:n.z.string().optional()}),c=l.partial();n.z.object({authorId:n.z.string(),title:n.z.string().min(1).max(255),slug:n.z.string().min(1).max(255),content:n.z.string().min(1),excerpt:n.z.string().optional(),featuredImageUrl:n.z.string().max(500).optional(),isPublished:n.z.boolean().default(!1),publishedAt:n.z.date().optional(),categories:n.z.string().optional(),tags:n.z.string().optional()}).partial(),n.z.object({firstName:n.z.string().min(1),lastName:n.z.string().min(1),position:n.z.string().min(1),department:n.z.string().optional(),phone:n.z.string().min(1).max(12),email:n.z.string().email().max(255).optional(),salary:n.z.number().positive().optional(),payrollMethod:n.z.string().max(10).optional(),resumeUrl:n.z.string().max(500).optional(),notes:n.z.string().optional(),bio:n.z.string().optional(),photoUrl:n.z.string().max(500).optional(),linkedInUrl:n.z.string().max(500).optional(),twitterUrl:n.z.string().max(500).optional(),githubUrl:n.z.string().max(500).optional(),displayOrder:n.z.number().int().default(0),isActive:n.z.boolean().default(!0)}).partial(),n.z.object({name:n.z.string().min(1),description:n.z.string().optional(),iconUrl:n.z.string().url().optional(),displayOrder:n.z.number().int().default(0),isActive:n.z.boolean().default(!0)}).partial(),n.z.object({clientName:n.z.string().min(1).max(100),clientTitle:n.z.string().min(1).max(100),clientCompany:n.z.string().min(1).max(100),clientPhotoUrl:n.z.string().max(500).optional(),content:n.z.string().min(1),rating:n.z.number().int().min(1).max(5).default(5),isFeatured:n.z.boolean().default(!1),displayOrder:n.z.number().int().default(0)}).partial(),n.z.object({userId:n.z.string().optional(),name:n.z.string().min(1),email:n.z.string().email(),phone:n.z.string().optional(),company:n.z.string().optional(),subject:n.z.string().min(1),message:n.z.string().min(1),status:n.z.enum(["NEW","IN_PROGRESS","RESOLVED","CLOSED"]).default("NEW")}).partial(),n.z.object({name:n.z.string().min(1),description:n.z.string().optional(),parentId:n.z.string().optional(),isActive:n.z.boolean().default(!0),displayOrder:n.z.number().int().default(0)}).partial(),n.z.object({clientId:n.z.string(),orderNumber:n.z.string().min(1),description:n.z.string().optional(),totalAmount:n.z.number().positive(),status:n.z.enum(["PENDING","CONFIRMED","IN_PROGRESS","COMPLETED","CANCELLED"]).default("PENDING"),orderDate:n.z.date().default(()=>new Date)}).partial(),n.z.object({clientId:n.z.string(),projectId:n.z.string().optional(),orderId:n.z.string().optional(),contractId:n.z.string().optional(),invoiceNumber:n.z.string().min(1),description:n.z.string().optional(),subtotal:n.z.number().positive(),taxAmount:n.z.number().default(0),totalAmount:n.z.number().positive(),status:n.z.enum(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),issueDate:n.z.date().default(()=>new Date),dueDate:n.z.date(),paidAt:n.z.date().optional()}).partial(),n.z.object({title:n.z.string().min(1),description:n.z.string().min(1),requirements:n.z.string().min(1),location:n.z.string().min(1),employmentType:n.z.string().min(1),salaryMin:n.z.number().positive().optional(),salaryMax:n.z.number().positive().optional(),salaryCurrency:n.z.string().default("USD"),isRemote:n.z.boolean().default(!1),isActive:n.z.boolean().default(!0),expiresAt:n.z.date().optional()}).partial()},86087:(t,e,i)=>{"use strict";i.r(e),i.d(e,{patchFetch:()=>E,routeModule:()=>z,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var n={};i.r(n),i.d(n,{DELETE:()=>p,GET:()=>u,POST:()=>d,PUT:()=>m});var r=i(96559),a=i(48088),o=i(37719),s=i(31183),l=i(53171),c=i(85463);let u=(0,l.FB)(async t=>{let{page:e,limit:i,search:n,sortBy:r,sortOrder:a,filter:o}=(0,l.vA)(t),{skip:c,take:u}=(0,l.Hx)(e,i),d={};n&&Object.assign(d,(0,l.zJ)(n,["companyName","contactName","contactEmail","city","state","country"])),"active"===o?d.projects={some:{status:{in:["PLANNING","IN_PROGRESS"]}}}:"inactive"===o&&(d.projects={none:{status:{in:["PLANNING","IN_PROGRESS"]}}});let m=await s.z.client.count({where:d}),p=await s.z.client.findMany({where:d,include:{projects:{select:{id:!0,name:!0,status:!0,startDate:!0,completionDate:!0,estimatedCost:!0},orderBy:{createdAt:"desc"},take:5},orders:{select:{id:!0,orderNumber:!0,totalAmount:!0,status:!0,orderDate:!0},orderBy:{createdAt:"desc"},take:3},invoices:{select:{id:!0,invoiceNumber:!0,totalAmount:!0,status:!0,issueDate:!0,dueDate:!0},where:{status:{in:["SENT","OVERDUE"]}},orderBy:{dueDate:"asc"},take:3},_count:{select:{projects:!0,orders:!0,invoices:!0,contracts:!0}}},orderBy:(0,l.du)(r,a),skip:c,take:u});return(0,l.vS)(p,e,i,m)}),d=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["POST"]);let e=(0,l.S)(c.xc),i=await e(t);if(await s.z.client.findFirst({where:{contactEmail:i.contactEmail}}))throw Error("A client with this email already exists");if(i.userId&&!await s.z.user.findUnique({where:{id:i.userId}}))throw Error("User not found");let n=await s.z.client.create({data:i,include:{_count:{select:{projects:!0,orders:!0,invoices:!0,contracts:!0}}}});return(0,l.r6)(n,"Client created successfully",201)}),m=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["PUT"]);let{ids:e,data:i}=await t.json();if(!Array.isArray(e)||0===e.length)throw Error("Invalid client IDs provided");let n=(0,l.S)(c.ue),r=await n({json:()=>i}),a=await s.z.client.updateMany({where:{id:{in:e}},data:r});return(0,l.r6)({count:a.count},`${a.count} clients updated successfully`)}),p=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["DELETE"]);let{ids:e}=await t.json();if(!Array.isArray(e)||0===e.length)throw Error("Invalid client IDs provided");let i=await s.z.client.findMany({where:{id:{in:e},OR:[{projects:{some:{}}},{orders:{some:{}}},{invoices:{some:{}}},{contracts:{some:{}}}]},select:{id:!0,companyName:!0}});if(i.length>0){let t=i.map(t=>t.companyName).join(", ");throw Error(`Cannot delete clients with associated data: ${t}. Please handle their projects, orders, invoices, and contracts first.`)}let n=await s.z.client.deleteMany({where:{id:{in:e}}});return(0,l.r6)({count:n.count},`${n.count} clients deleted successfully`)}),z=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/clients/route",pathname:"/api/clients",filename:"route",bundlePath:"app/api/clients/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\clients\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:y}=z;function E(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},96330:t=>{"use strict";t.exports=require("@prisma/client")},96487:()=>{}};var e=require("../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),n=e.X(0,[4447,580,5697],()=>i(86087));module.exports=n})();