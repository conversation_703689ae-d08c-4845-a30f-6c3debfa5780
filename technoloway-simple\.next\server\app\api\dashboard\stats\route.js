(()=>{var e={};e.id=5694,e.ids=[5694],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12336:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>p});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(96559),n=r(48088),o=r(37719),i=r(31183),u=r(53171);let c=(0,u.FB)(async e=>{await (0,u.ZT)(e);let t=new Date,r=new Date(t.getFullYear(),t.getMonth(),1);t.getFullYear();let[s,a,n,o,c,d,l,p,m,h,w,g,A,y,f]=await Promise.all([i.z.client.count(),i.z.project.count(),i.z.service.count({where:{isActive:!0}}),i.z.teamMember.count({where:{isActive:!0}}),i.z.project.count({where:{status:{in:["PLANNING","IN_PROGRESS"]}}}),i.z.project.count({where:{status:"COMPLETED"}}),i.z.invoice.count({where:{status:{in:["SENT","OVERDUE"]}}}),i.z.invoice.aggregate({where:{status:"PAID"},_sum:{totalAmount:!0}}),i.z.invoice.aggregate({where:{status:"PAID",paidAt:{gte:r}},_sum:{totalAmount:!0}}),i.z.project.findMany({take:5,orderBy:{createdAt:"desc"},include:{client:{select:{companyName:!0}}}}),i.z.client.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,companyName:!0,contactName:!0,contactEmail:!0,createdAt:!0}}),i.z.project.groupBy({by:["status"],_count:{status:!0}}),i.z.invoice.groupBy({by:["status"],_count:{status:!0},_sum:{totalAmount:!0}}),i.z.service.findMany({include:{_count:{select:{projects:!0,orderDetails:!0}}},orderBy:{projects:{_count:"desc"}},take:5}),i.z.project.findMany({where:{completionDate:{gte:t,lte:new Date(t.getTime()+2592e6)},status:{in:["PLANNING","IN_PROGRESS"]}},include:{client:{select:{companyName:!0}}},orderBy:{completionDate:"asc"},take:10})]),v=p._sum.totalAmount?Number(p._sum.totalAmount):0,R=m._sum.totalAmount?Number(m._sum.totalAmount):0,N={overview:{totalClients:s,totalProjects:a,totalServices:n,totalTeamMembers:o,activeProjects:c,completedProjects:d,pendingInvoices:l,totalRevenue:v,monthlyRevenue:R},growth:{projects:c>0?c/a*100:0,clients:5.2*(s>0),revenue:v>0&&R>0?R/v*100:0},charts:{projectsByStatus:g.map(e=>({status:e.status,count:e._count.status})),invoicesByStatus:A.map(e=>({status:e.status,count:e._count.status,amount:e._sum.totalAmount?Number(e._sum.totalAmount):0}))},recent:{projects:h.map(e=>({id:e.id,name:e.name,status:e.status,clientName:e.client?.companyName,createdAt:e.createdAt})),clients:w},insights:{topServices:y.map(e=>({id:e.id,name:e.name,projectCount:e._count.projects,orderCount:e._count.orderDetails,price:Number(e.price)})),upcomingDeadlines:f.map(e=>({id:e.id,name:e.name,clientName:e.client?.companyName,deadline:e.completionDate,status:e.status}))}};return(0,u.r6)(N,"Dashboard statistics retrieved successfully")}),d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:p,serverHooks:m}=d;function h(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:p})}},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(e,t,r)=>{"use strict";r.d(t,{FB:()=>m,Hx:()=>p,S:()=>d,ZT:()=>g,du:()=>f,hD:()=>o,oC:()=>w,r6:()=>i,vA:()=>l,vS:()=>c,yX:()=>h,z9:()=>A,zJ:()=>y});var s=r(32190),a=r(45697),n=r(96330);class o extends Error{constructor(e,t=500,r){super(e),this.message=e,this.statusCode=t,this.code=r,this.name="ApiError"}}function i(e,t,r=200){return s.NextResponse.json({success:!0,data:e,message:t},{status:r})}function u(e,t=500,r){let a=e instanceof Error?e.message:e;return s.NextResponse.json({success:!1,error:a,code:r},{status:t})}function c(e,t,r,a,n){let o=Math.ceil(a/r);return s.NextResponse.json({success:!0,data:e,message:n,pagination:{page:t,limit:r,total:a,totalPages:o}})}function d(e){return async t=>{try{let r=await t.json();return e.parse(r)}catch(e){if(e instanceof a.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");throw new o(`Validation error: ${t}`,400,"VALIDATION_ERROR")}throw new o("Invalid request body",400,"INVALID_BODY")}}}function l(e){let{searchParams:t}=new URL(e.url);return{page:parseInt(t.get("page")||"1"),limit:Math.min(parseInt(t.get("limit")||"10"),100),search:t.get("search")||void 0,sortBy:t.get("sortBy")||void 0,sortOrder:t.get("sortOrder")||"desc",filter:t.get("filter")||void 0}}function p(e,t){return{skip:(e-1)*t,take:t}}function m(e){return async(t,r)=>{try{return await e(t,r)}catch(e){if(console.error("API Error:",e),e instanceof o)return u(e.message,e.statusCode,e.code);if(e instanceof a.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");return u(`Validation error: ${t}`,400,"VALIDATION_ERROR")}if(e instanceof n.Prisma.PrismaClientKnownRequestError)switch(e.code){case"P2002":return u("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return u("Record not found",404,"NOT_FOUND");case"P2003":return u("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return u("Database error occurred",500,"DATABASE_ERROR")}return u("Internal server error",500,"INTERNAL_ERROR")}}}function h(e,t){if(!t.includes(e.method))throw new o(`Method ${e.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function w(e){let{getServerSession:t}=await r.e(5426).then(r.bind(r,35426)),{authOptions:s}=await Promise.all([r.e(1024),r.e(2909)]).then(r.bind(r,12909)),a=await t(s);if(!a||!a.user)throw new o("Authentication required",401,"UNAUTHORIZED");return{id:a.user.id,email:a.user.email,role:a.user.role,name:a.user.name}}async function g(e){let t=await w(e);if("ADMIN"!==t.role)throw new o("Admin access required",403,"FORBIDDEN");return t}function A(e){return e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function y(e,t){return e?{OR:t.map(t=>({[t]:{contains:e,mode:"insensitive"}}))}:{}}function f(e,t="desc"){return e?{[e]:t}:{createdAt:t}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,5697],()=>r(12336));module.exports=s})();