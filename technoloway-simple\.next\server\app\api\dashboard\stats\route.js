(()=>{var e={};e.id=5694,e.ids=[5694],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12336:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>p,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u});var n=r(96559),a=r(48088),o=r(37719),i=r(31183),c=r(53171);let u=(0,c.FB)(async e=>{await (0,c.ZT)(e);let t=new Date,r=new Date(t.getFullYear(),t.getMonth(),1);t.getFullYear();let[s,n,a,o,u,d,l,m,p,h,A,g,w,y,f]=await Promise.all([i.z.client.count(),i.z.project.count(),i.z.service.count({where:{isActive:!0}}),i.z.teamMember.count({where:{isActive:!0}}),i.z.project.count({where:{status:{in:["PLANNING","IN_PROGRESS"]}}}),i.z.project.count({where:{status:"COMPLETED"}}),i.z.invoice.count({where:{status:{in:["SENT","OVERDUE"]}}}),i.z.invoice.aggregate({where:{status:"PAID"},_sum:{totalAmount:!0}}),i.z.invoice.aggregate({where:{status:"PAID",paidAt:{gte:r}},_sum:{totalAmount:!0}}),i.z.project.findMany({take:5,orderBy:{createdAt:"desc"},include:{client:{select:{companyName:!0}}}}),i.z.client.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,companyName:!0,contactName:!0,contactEmail:!0,createdAt:!0}}),i.z.project.groupBy({by:["status"],_count:{status:!0}}),i.z.invoice.groupBy({by:["status"],_count:{status:!0},_sum:{totalAmount:!0}}),i.z.service.findMany({include:{_count:{select:{projects:!0,orderDetails:!0}}},orderBy:{projects:{_count:"desc"}},take:5}),i.z.project.findMany({where:{completionDate:{gte:t,lte:new Date(t.getTime()+2592e6)},status:{in:["PLANNING","IN_PROGRESS"]}},include:{client:{select:{companyName:!0}}},orderBy:{completionDate:"asc"},take:10})]),R=m._sum.totalAmount?Number(m._sum.totalAmount):0,v=p._sum.totalAmount?Number(p._sum.totalAmount):0,N={overview:{totalClients:s,totalProjects:n,totalServices:a,totalTeamMembers:o,activeProjects:u,completedProjects:d,pendingInvoices:l,totalRevenue:R,monthlyRevenue:v},growth:{projects:u>0?u/n*100:0,clients:5.2*(s>0),revenue:R>0&&v>0?v/R*100:0},charts:{projectsByStatus:g.map(e=>({status:e.status,count:e._count.status})),invoicesByStatus:w.map(e=>({status:e.status,count:e._count.status,amount:e._sum.totalAmount?Number(e._sum.totalAmount):0}))},recent:{projects:h.map(e=>({id:e.id,name:e.name,status:e.status,clientName:e.client?.companyName,createdAt:e.createdAt})),clients:A},insights:{topServices:y.map(e=>({id:e.id,name:e.name,projectCount:e._count.projects,orderCount:e._count.orderDetails,price:Number(e.price)})),upcomingDeadlines:f.map(e=>({id:e.id,name:e.name,clientName:e.client?.companyName,deadline:e.completionDate,status:e.status}))}};return(0,c.r6)(N,"Dashboard statistics retrieved successfully")}),d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:p}=d;function h(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(e,t,r)=>{"use strict";r.d(t,{FB:()=>p,Hx:()=>m,S:()=>d,ZT:()=>g,du:()=>y,hD:()=>o,r6:()=>i,vA:()=>l,vS:()=>u,yX:()=>h,zJ:()=>w});var s=r(32190),n=r(45697),a=r(96330);class o extends Error{constructor(e,t=500,r){super(e),this.message=e,this.statusCode=t,this.code=r,this.name="ApiError"}}function i(e,t,r=200){return s.NextResponse.json({success:!0,data:e,message:t},{status:r})}function c(e,t=500,r){let n=e instanceof Error?e.message:e;return s.NextResponse.json({success:!1,error:n,code:r},{status:t})}function u(e,t,r,n,a){let o=Math.ceil(n/r);return s.NextResponse.json({success:!0,data:e,message:a,pagination:{page:t,limit:r,total:n,totalPages:o}})}function d(e){return async t=>{try{let r=await t.json();return e.parse(r)}catch(e){if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");throw new o(`Validation error: ${t}`,400,"VALIDATION_ERROR")}throw new o("Invalid request body",400,"INVALID_BODY")}}}function l(e){let{searchParams:t}=new URL(e.url);return{page:parseInt(t.get("page")||"1"),limit:Math.min(parseInt(t.get("limit")||"10"),100),search:t.get("search")||void 0,sortBy:t.get("sortBy")||void 0,sortOrder:t.get("sortOrder")||"desc",filter:t.get("filter")||void 0}}function m(e,t){return{skip:(e-1)*t,take:t}}function p(e){return async(t,r)=>{try{return await e(t,r)}catch(e){if(console.error("API Error:",e),e instanceof o)return c(e.message,e.statusCode,e.code);if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");return c(`Validation error: ${t}`,400,"VALIDATION_ERROR")}if(e instanceof a.Prisma.PrismaClientKnownRequestError)switch(e.code){case"P2002":return c("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return c("Record not found",404,"NOT_FOUND");case"P2003":return c("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return c("Database error occurred",500,"DATABASE_ERROR")}return c("Internal server error",500,"INTERNAL_ERROR")}}}function h(e,t){if(!t.includes(e.method))throw new o(`Method ${e.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function A(e){if(!e.headers.get("authorization"))throw new o("Authentication required",401,"UNAUTHORIZED");return{id:"admin-user-id",email:"<EMAIL>",role:"ADMIN"}}async function g(e){let t=await A(e);if("ADMIN"!==t.role)throw new o("Admin access required",403,"FORBIDDEN");return t}function w(e,t){return e?{OR:t.map(t=>({[t]:{contains:e,mode:"insensitive"}}))}:{}}function y(e,t="desc"){return e?{[e]:t}:{createdAt:t}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5697],()=>r(12336));module.exports=s})();