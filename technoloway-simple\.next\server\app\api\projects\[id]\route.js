(()=>{var e={};e.id=9514,e.ids=[9514],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10332:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>z,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{DELETE:()=>p,GET:()=>d,PATCH:()=>m,PUT:()=>u});var n=r(96559),a=r(48088),o=r(37719),s=r(31183),l=r(53171),c=r(85463);let d=(0,l.FB)(async(e,{params:t})=>{let{id:r}=t,i=await s.z.project.findUnique({where:{id:r},include:{client:{select:{id:!0,companyName:!0,contactName:!0,contactEmail:!0,contactPhone:!0,address:!0,city:!0,state:!0,country:!0}},order:{select:{id:!0,orderNumber:!0,totalAmount:!0,status:!0,orderDate:!0}},services:{select:{id:!0,name:!0,description:!0,price:!0,category:{select:{id:!0,name:!0}}}},technologies:{select:{id:!0,name:!0,description:!0,iconUrl:!0},orderBy:{displayOrder:"asc"}},tasks:{include:{teamMember:{select:{id:!0,firstName:!0,lastName:!0,position:!0,email:!0}}},orderBy:{createdAt:"desc"}},messages:{include:{sender:{select:{id:!0,firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"},take:20},documents:{orderBy:{createdAt:"desc"}},invoices:{select:{id:!0,invoiceNumber:!0,totalAmount:!0,status:!0,issueDate:!0,dueDate:!0},orderBy:{createdAt:"desc"}},contracts:{select:{id:!0,title:!0,totalAmount:!0,status:!0,startDate:!0,endDate:!0},orderBy:{createdAt:"desc"}},feedbacks:{include:{client:{select:{id:!0,companyName:!0,contactName:!0}}},orderBy:{createdAt:"desc"}},_count:{select:{messages:!0,tasks:!0,documents:!0,invoices:!0,contracts:!0,feedbacks:!0}}}});if(!i)throw new l.hD("Project not found",404,"NOT_FOUND");return(0,l.r6)(i)}),u=(0,l.FB)(async(e,{params:t})=>{await (0,l.ZT)(e),(0,l.yX)(e,["PUT"]);let{id:r}=t,i=(0,l.S)(c.Or),n=await i(e);if(!await s.z.project.findUnique({where:{id:r}}))throw new l.hD("Project not found",404,"NOT_FOUND");if(n.orderId&&!await s.z.order.findUnique({where:{id:n.orderId}}))throw new l.hD("Order not found",404,"ORDER_NOT_FOUND");if(n.clientId&&!await s.z.client.findUnique({where:{id:n.clientId}}))throw new l.hD("Client not found",404,"CLIENT_NOT_FOUND");let a=await s.z.project.update({where:{id:r},data:n,include:{client:{select:{id:!0,companyName:!0,contactName:!0,contactEmail:!0}},order:{select:{id:!0,orderNumber:!0,totalAmount:!0,status:!0}},services:{select:{id:!0,name:!0,price:!0}},technologies:{select:{id:!0,name:!0,iconUrl:!0}},_count:{select:{messages:!0,tasks:!0,documents:!0,invoices:!0}}}});return(0,l.r6)(a,"Project updated successfully")}),p=(0,l.FB)(async(e,{params:t})=>{await (0,l.ZT)(e),(0,l.yX)(e,["DELETE"]);let{id:r}=t,i=await s.z.project.findUnique({where:{id:r},include:{invoices:{select:{id:!0}},contracts:{select:{id:!0}}}});if(!i)throw new l.hD("Project not found",404,"NOT_FOUND");if(i.invoices.length>0)throw new l.hD("Cannot delete project with invoices. Please handle invoices first.",400,"PROJECT_HAS_INVOICES");if(i.contracts.length>0)throw new l.hD("Cannot delete project with contracts. Please handle contracts first.",400,"PROJECT_HAS_CONTRACTS");return await s.z.projectTask.deleteMany({where:{projectId:r}}),await s.z.projectDocument.deleteMany({where:{projectId:r}}),await s.z.message.deleteMany({where:{projectId:r}}),await s.z.feedback.deleteMany({where:{projectId:r}}),await s.z.project.delete({where:{id:r}}),(0,l.r6)(null,"Project deleted successfully")}),m=(0,l.FB)(async(e,{params:t})=>{await (0,l.ZT)(e),(0,l.yX)(e,["PATCH"]);let{id:r}=t,i=await e.json();if(!await s.z.project.findUnique({where:{id:r}}))throw new l.hD("Project not found",404,"NOT_FOUND");let n={};for(let e of["status","isFeatured","displayOrder"])e in i&&(n[e]=i[e]);if(0===Object.keys(n).length)throw new l.hD("No valid fields to update",400,"NO_VALID_FIELDS");let a=await s.z.project.update({where:{id:r},data:n,include:{client:{select:{id:!0,companyName:!0}},order:{select:{id:!0,orderNumber:!0}}}});return(0,l.r6)(a,"Project updated successfully")}),z=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/projects/[id]/route",pathname:"/api/projects/[id]",filename:"route",bundlePath:"app/api/projects/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:h}=z;function w(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var i=r(96330);let n=globalThis.prisma??new i.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(e,t,r)=>{"use strict";r.d(t,{FB:()=>m,Hx:()=>p,S:()=>d,ZT:()=>f,du:()=>N,hD:()=>o,oC:()=>g,r6:()=>s,vA:()=>u,vS:()=>c,yX:()=>z,z9:()=>h,zJ:()=>w});var i=r(32190),n=r(45697),a=r(96330);class o extends Error{constructor(e,t=500,r){super(e),this.message=e,this.statusCode=t,this.code=r,this.name="ApiError"}}function s(e,t,r=200){return i.NextResponse.json({success:!0,data:e,message:t},{status:r})}function l(e,t=500,r){let n=e instanceof Error?e.message:e;return i.NextResponse.json({success:!1,error:n,code:r},{status:t})}function c(e,t,r,n,a){let o=Math.ceil(n/r);return i.NextResponse.json({success:!0,data:e,message:a,pagination:{page:t,limit:r,total:n,totalPages:o}})}function d(e){return async t=>{try{let r=await t.json();return e.parse(r)}catch(e){if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");throw new o(`Validation error: ${t}`,400,"VALIDATION_ERROR")}throw new o("Invalid request body",400,"INVALID_BODY")}}}function u(e){let{searchParams:t}=new URL(e.url);return{page:parseInt(t.get("page")||"1"),limit:Math.min(parseInt(t.get("limit")||"10"),100),search:t.get("search")||void 0,sortBy:t.get("sortBy")||void 0,sortOrder:t.get("sortOrder")||"desc",filter:t.get("filter")||void 0}}function p(e,t){return{skip:(e-1)*t,take:t}}function m(e){return async(t,r)=>{try{return await e(t,r)}catch(e){if(console.error("API Error:",e),e instanceof o)return l(e.message,e.statusCode,e.code);if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");return l(`Validation error: ${t}`,400,"VALIDATION_ERROR")}if(e instanceof a.Prisma.PrismaClientKnownRequestError)switch(e.code){case"P2002":return l("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return l("Record not found",404,"NOT_FOUND");case"P2003":return l("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return l("Database error occurred",500,"DATABASE_ERROR")}return l("Internal server error",500,"INTERNAL_ERROR")}}}function z(e,t){if(!t.includes(e.method))throw new o(`Method ${e.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function g(e){let{getServerSession:t}=await r.e(5426).then(r.bind(r,35426)),{authOptions:i}=await Promise.all([r.e(1024),r.e(2909)]).then(r.bind(r,12909)),n=await t(i);if(!n||!n.user)throw new o("Authentication required",401,"UNAUTHORIZED");return{id:n.user.id,email:n.user.email,role:n.user.role,name:n.user.name}}async function f(e){let t=await g(e);if("ADMIN"!==t.role)throw new o("Admin access required",403,"FORBIDDEN");return t}function h(e){return e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function w(e,t){return e?{OR:t.map(t=>({[t]:{contains:e,mode:"insensitive"}}))}:{}}function N(e,t="desc"){return e?{[e]:t}:{createdAt:t}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85463:(e,t,r)=>{"use strict";r.d(t,{$b:()=>g,AF:()=>d,Gn:()=>f,N6:()=>p,Or:()=>s,Wc:()=>m,ZC:()=>h,Zi:()=>o,c5:()=>a,j6:()=>n,mQ:()=>z,nx:()=>u,ue:()=>c,xc:()=>l});var i=r(45697);i.z.object({email:i.z.string().email(),firstName:i.z.string().optional(),lastName:i.z.string().optional(),imageUrl:i.z.string().url().optional(),role:i.z.enum(["ADMIN","USER","CLIENT"]).default("USER")}).partial();let n=i.z.object({categoryId:i.z.string(),name:i.z.string().min(1).max(255),description:i.z.string().min(1),iconClass:i.z.string().max(100).optional(),price:i.z.number().positive(),discountRate:i.z.number().int().min(0).max(100).optional(),totalDiscount:i.z.number().optional(),manager:i.z.string().max(50).optional(),isActive:i.z.boolean().default(!0),displayOrder:i.z.number().int().default(0)}),a=n.partial(),o=i.z.object({orderId:i.z.string(),clientId:i.z.string().optional(),name:i.z.string().min(1),description:i.z.string().min(1),goals:i.z.string().optional(),manager:i.z.string().max(10).optional(),startDate:i.z.date().optional(),completionDate:i.z.date().optional(),estimatedCost:i.z.number().positive().optional(),estimatedTime:i.z.string().max(10).optional(),estimatedEffort:i.z.string().max(10).optional(),status:i.z.enum(["PLANNING","IN_PROGRESS","COMPLETED","ON_HOLD","CANCELLED"]).default("PLANNING"),isFeatured:i.z.boolean().default(!1),displayOrder:i.z.number().int().default(0),imageUrl:i.z.string().url().optional(),projectUrl:i.z.string().url().optional(),githubUrl:i.z.string().url().optional()}),s=o.partial(),l=i.z.object({userId:i.z.string().optional(),companyName:i.z.string().min(1).max(200),contactName:i.z.string().min(1).max(100),contactPosition:i.z.string().max(100).optional(),contactEmail:i.z.string().email().max(100),contactPhone:i.z.string().max(20).optional(),contactFax:i.z.string().max(20).optional(),companyWebsite:i.z.string().max(100).optional(),address:i.z.string().min(1).max(200),city:i.z.string().min(1).max(100),state:i.z.string().min(1).max(50),zipCode:i.z.string().min(1).max(20),country:i.z.string().min(1).max(100),logoUrl:i.z.string().max(500).optional(),notes:i.z.string().optional()}),c=l.partial(),d=i.z.object({authorId:i.z.string(),title:i.z.string().min(1).max(255),slug:i.z.string().min(1).max(255),content:i.z.string().min(1),excerpt:i.z.string().optional(),featuredImageUrl:i.z.string().max(500).optional(),isPublished:i.z.boolean().default(!1),publishedAt:i.z.date().optional(),categories:i.z.string().optional(),tags:i.z.string().optional()}),u=d.partial(),p=i.z.object({firstName:i.z.string().min(1),lastName:i.z.string().min(1),position:i.z.string().min(1),department:i.z.string().optional(),phone:i.z.string().min(1).max(12),email:i.z.string().email().max(255).optional(),salary:i.z.number().positive().optional(),payrollMethod:i.z.string().max(10).optional(),resumeUrl:i.z.string().max(500).optional(),notes:i.z.string().optional(),bio:i.z.string().optional(),photoUrl:i.z.string().max(500).optional(),linkedInUrl:i.z.string().max(500).optional(),twitterUrl:i.z.string().max(500).optional(),githubUrl:i.z.string().max(500).optional(),displayOrder:i.z.number().int().default(0),isActive:i.z.boolean().default(!0)}),m=p.partial(),z=i.z.object({name:i.z.string().min(1),description:i.z.string().optional(),iconUrl:i.z.string().url().optional(),displayOrder:i.z.number().int().default(0),isActive:i.z.boolean().default(!0)}),g=z.partial();i.z.object({clientName:i.z.string().min(1).max(100),clientTitle:i.z.string().min(1).max(100),clientCompany:i.z.string().min(1).max(100),clientPhotoUrl:i.z.string().max(500).optional(),content:i.z.string().min(1),rating:i.z.number().int().min(1).max(5).default(5),isFeatured:i.z.boolean().default(!1),displayOrder:i.z.number().int().default(0)}).partial();let f=i.z.object({userId:i.z.string().optional(),name:i.z.string().min(1),email:i.z.string().email(),phone:i.z.string().optional(),company:i.z.string().optional(),subject:i.z.string().min(1),message:i.z.string().min(1),status:i.z.enum(["NEW","IN_PROGRESS","RESOLVED","CLOSED"]).default("NEW")}),h=f.partial();i.z.object({name:i.z.string().min(1),description:i.z.string().optional(),parentId:i.z.string().optional(),isActive:i.z.boolean().default(!0),displayOrder:i.z.number().int().default(0)}).partial(),i.z.object({clientId:i.z.string(),orderNumber:i.z.string().min(1),description:i.z.string().optional(),totalAmount:i.z.number().positive(),status:i.z.enum(["PENDING","CONFIRMED","IN_PROGRESS","COMPLETED","CANCELLED"]).default("PENDING"),orderDate:i.z.date().default(()=>new Date)}).partial(),i.z.object({clientId:i.z.string(),projectId:i.z.string().optional(),orderId:i.z.string().optional(),contractId:i.z.string().optional(),invoiceNumber:i.z.string().min(1),description:i.z.string().optional(),subtotal:i.z.number().positive(),taxAmount:i.z.number().default(0),totalAmount:i.z.number().positive(),status:i.z.enum(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),issueDate:i.z.date().default(()=>new Date),dueDate:i.z.date(),paidAt:i.z.date().optional()}).partial(),i.z.object({title:i.z.string().min(1),description:i.z.string().min(1),requirements:i.z.string().min(1),location:i.z.string().min(1),employmentType:i.z.string().min(1),salaryMin:i.z.number().positive().optional(),salaryMax:i.z.number().positive().optional(),salaryCurrency:i.z.string().default("USD"),isRemote:i.z.boolean().default(!1),isActive:i.z.boolean().default(!0),expiresAt:i.z.date().optional()}).partial()},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580,5697],()=>r(10332));module.exports=i})();