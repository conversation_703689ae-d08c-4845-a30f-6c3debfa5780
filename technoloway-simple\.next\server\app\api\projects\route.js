(()=>{var t={};t.id=64,t.ids=[64],t.modules={2773:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>E,routeModule:()=>z,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{DELETE:()=>p,GET:()=>u,POST:()=>d,PUT:()=>m});var n=r(96559),a=r(48088),o=r(37719),s=r(31183),l=r(53171),c=r(85463);let u=(0,l.FB)(async t=>{let{page:e,limit:r,search:i,sortBy:n,sortOrder:a,filter:o}=(0,l.vA)(t),{skip:c,take:u}=(0,l.Hx)(e,r),d={};i&&Object.assign(d,(0,l.zJ)(i,["name","description","goals"])),o&&["PLANNING","IN_PROGRESS","COMPLETED","ON_HOLD","CANCELLED"].includes(o)?d.status=o:"featured"===o?d.isFeatured=!0:"active"===o&&(d.status={in:["PLANNING","IN_PROGRESS"]});let m=await s.z.project.count({where:d}),p=await s.z.project.findMany({where:d,include:{client:{select:{id:!0,companyName:!0,contactName:!0,contactEmail:!0}},order:{select:{id:!0,orderNumber:!0,totalAmount:!0}},services:{select:{id:!0,name:!0,price:!0}},technologies:{select:{id:!0,name:!0,iconUrl:!0}},_count:{select:{messages:!0,tasks:!0,documents:!0,invoices:!0}}},orderBy:(0,l.du)(n,a),skip:c,take:u});return(0,l.vS)(p,e,r,m)}),d=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["POST"]);let e=(0,l.S)(c.Zi),r=await e(t);if(!await s.z.order.findUnique({where:{id:r.orderId}}))throw Error("Order not found");if(r.clientId&&!await s.z.client.findUnique({where:{id:r.clientId}}))throw Error("Client not found");let i=await s.z.project.create({data:r,include:{client:{select:{id:!0,companyName:!0,contactName:!0,contactEmail:!0}},order:{select:{id:!0,orderNumber:!0,totalAmount:!0}}}});return(0,l.r6)(i,"Project created successfully",201)}),m=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["PUT"]);let{ids:e,data:r}=await t.json();if(!Array.isArray(e)||0===e.length)throw Error("Invalid project IDs provided");let i=(0,l.S)(c.Or),n=await i({json:()=>r}),a=await s.z.project.updateMany({where:{id:{in:e}},data:n});return(0,l.r6)({count:a.count},`${a.count} projects updated successfully`)}),p=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["DELETE"]);let{ids:e}=await t.json();if(!Array.isArray(e)||0===e.length)throw Error("Invalid project IDs provided");let r=await s.z.project.findMany({where:{id:{in:e},OR:[{invoices:{some:{}}},{contracts:{some:{}}}]},select:{id:!0,name:!0}});if(r.length>0){let t=r.map(t=>t.name).join(", ");throw Error(`Cannot delete projects with invoices or contracts: ${t}. Please handle these records first.`)}await s.z.projectTask.deleteMany({where:{projectId:{in:e}}}),await s.z.projectDocument.deleteMany({where:{projectId:{in:e}}}),await s.z.message.deleteMany({where:{projectId:{in:e}}});let i=await s.z.project.deleteMany({where:{id:{in:e}}});return(0,l.r6)({count:i.count},`${i.count} projects deleted successfully`)}),z=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\projects\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:h}=z;function E(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(t,e,r)=>{"use strict";r.d(e,{z:()=>n});var i=r(96330);let n=globalThis.prisma??new i.PrismaClient},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(t,e,r)=>{"use strict";r.d(e,{FB:()=>p,Hx:()=>m,S:()=>u,ZT:()=>f,du:()=>E,hD:()=>o,r6:()=>s,vA:()=>d,vS:()=>c,yX:()=>z,zJ:()=>h});var i=r(32190),n=r(45697),a=r(96330);class o extends Error{constructor(t,e=500,r){super(t),this.message=t,this.statusCode=e,this.code=r,this.name="ApiError"}}function s(t,e,r=200){return i.NextResponse.json({success:!0,data:t,message:e},{status:r})}function l(t,e=500,r){let n=t instanceof Error?t.message:t;return i.NextResponse.json({success:!1,error:n,code:r},{status:e})}function c(t,e,r,n,a){let o=Math.ceil(n/r);return i.NextResponse.json({success:!0,data:t,message:a,pagination:{page:e,limit:r,total:n,totalPages:o}})}function u(t){return async e=>{try{let r=await e.json();return t.parse(r)}catch(t){if(t instanceof n.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");throw new o(`Validation error: ${e}`,400,"VALIDATION_ERROR")}throw new o("Invalid request body",400,"INVALID_BODY")}}}function d(t){let{searchParams:e}=new URL(t.url);return{page:parseInt(e.get("page")||"1"),limit:Math.min(parseInt(e.get("limit")||"10"),100),search:e.get("search")||void 0,sortBy:e.get("sortBy")||void 0,sortOrder:e.get("sortOrder")||"desc",filter:e.get("filter")||void 0}}function m(t,e){return{skip:(t-1)*e,take:e}}function p(t){return async(e,r)=>{try{return await t(e,r)}catch(t){if(console.error("API Error:",t),t instanceof o)return l(t.message,t.statusCode,t.code);if(t instanceof n.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");return l(`Validation error: ${e}`,400,"VALIDATION_ERROR")}if(t instanceof a.Prisma.PrismaClientKnownRequestError)switch(t.code){case"P2002":return l("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return l("Record not found",404,"NOT_FOUND");case"P2003":return l("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return l("Database error occurred",500,"DATABASE_ERROR")}return l("Internal server error",500,"INTERNAL_ERROR")}}}function z(t,e){if(!e.includes(t.method))throw new o(`Method ${t.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function g(t){if(!t.headers.get("authorization"))throw new o("Authentication required",401,"UNAUTHORIZED");return{id:"admin-user-id",email:"<EMAIL>",role:"ADMIN"}}async function f(t){let e=await g(t);if("ADMIN"!==e.role)throw new o("Admin access required",403,"FORBIDDEN");return e}function h(t,e){return t?{OR:e.map(e=>({[e]:{contains:t,mode:"insensitive"}}))}:{}}function E(t,e="desc"){return t?{[t]:e}:{createdAt:e}}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},85463:(t,e,r)=>{"use strict";r.d(e,{Or:()=>s,Zi:()=>o,c5:()=>a,j6:()=>n,ue:()=>c,xc:()=>l});var i=r(45697);i.z.object({email:i.z.string().email(),firstName:i.z.string().optional(),lastName:i.z.string().optional(),imageUrl:i.z.string().url().optional(),role:i.z.enum(["ADMIN","USER","CLIENT"]).default("USER")}).partial();let n=i.z.object({categoryId:i.z.string(),name:i.z.string().min(1).max(255),description:i.z.string().min(1),iconClass:i.z.string().max(100).optional(),price:i.z.number().positive(),discountRate:i.z.number().int().min(0).max(100).optional(),totalDiscount:i.z.number().optional(),manager:i.z.string().max(50).optional(),isActive:i.z.boolean().default(!0),displayOrder:i.z.number().int().default(0)}),a=n.partial(),o=i.z.object({orderId:i.z.string(),clientId:i.z.string().optional(),name:i.z.string().min(1),description:i.z.string().min(1),goals:i.z.string().optional(),manager:i.z.string().max(10).optional(),startDate:i.z.date().optional(),completionDate:i.z.date().optional(),estimatedCost:i.z.number().positive().optional(),estimatedTime:i.z.string().max(10).optional(),estimatedEffort:i.z.string().max(10).optional(),status:i.z.enum(["PLANNING","IN_PROGRESS","COMPLETED","ON_HOLD","CANCELLED"]).default("PLANNING"),isFeatured:i.z.boolean().default(!1),displayOrder:i.z.number().int().default(0),imageUrl:i.z.string().url().optional(),projectUrl:i.z.string().url().optional(),githubUrl:i.z.string().url().optional()}),s=o.partial(),l=i.z.object({userId:i.z.string().optional(),companyName:i.z.string().min(1).max(200),contactName:i.z.string().min(1).max(100),contactPosition:i.z.string().max(100).optional(),contactEmail:i.z.string().email().max(100),contactPhone:i.z.string().max(20).optional(),contactFax:i.z.string().max(20).optional(),companyWebsite:i.z.string().max(100).optional(),address:i.z.string().min(1).max(200),city:i.z.string().min(1).max(100),state:i.z.string().min(1).max(50),zipCode:i.z.string().min(1).max(20),country:i.z.string().min(1).max(100),logoUrl:i.z.string().max(500).optional(),notes:i.z.string().optional()}),c=l.partial();i.z.object({authorId:i.z.string(),title:i.z.string().min(1).max(255),slug:i.z.string().min(1).max(255),content:i.z.string().min(1),excerpt:i.z.string().optional(),featuredImageUrl:i.z.string().max(500).optional(),isPublished:i.z.boolean().default(!1),publishedAt:i.z.date().optional(),categories:i.z.string().optional(),tags:i.z.string().optional()}).partial(),i.z.object({firstName:i.z.string().min(1),lastName:i.z.string().min(1),position:i.z.string().min(1),department:i.z.string().optional(),phone:i.z.string().min(1).max(12),email:i.z.string().email().max(255).optional(),salary:i.z.number().positive().optional(),payrollMethod:i.z.string().max(10).optional(),resumeUrl:i.z.string().max(500).optional(),notes:i.z.string().optional(),bio:i.z.string().optional(),photoUrl:i.z.string().max(500).optional(),linkedInUrl:i.z.string().max(500).optional(),twitterUrl:i.z.string().max(500).optional(),githubUrl:i.z.string().max(500).optional(),displayOrder:i.z.number().int().default(0),isActive:i.z.boolean().default(!0)}).partial(),i.z.object({name:i.z.string().min(1),description:i.z.string().optional(),iconUrl:i.z.string().url().optional(),displayOrder:i.z.number().int().default(0),isActive:i.z.boolean().default(!0)}).partial(),i.z.object({clientName:i.z.string().min(1).max(100),clientTitle:i.z.string().min(1).max(100),clientCompany:i.z.string().min(1).max(100),clientPhotoUrl:i.z.string().max(500).optional(),content:i.z.string().min(1),rating:i.z.number().int().min(1).max(5).default(5),isFeatured:i.z.boolean().default(!1),displayOrder:i.z.number().int().default(0)}).partial(),i.z.object({userId:i.z.string().optional(),name:i.z.string().min(1),email:i.z.string().email(),phone:i.z.string().optional(),company:i.z.string().optional(),subject:i.z.string().min(1),message:i.z.string().min(1),status:i.z.enum(["NEW","IN_PROGRESS","RESOLVED","CLOSED"]).default("NEW")}).partial(),i.z.object({name:i.z.string().min(1),description:i.z.string().optional(),parentId:i.z.string().optional(),isActive:i.z.boolean().default(!0),displayOrder:i.z.number().int().default(0)}).partial(),i.z.object({clientId:i.z.string(),orderNumber:i.z.string().min(1),description:i.z.string().optional(),totalAmount:i.z.number().positive(),status:i.z.enum(["PENDING","CONFIRMED","IN_PROGRESS","COMPLETED","CANCELLED"]).default("PENDING"),orderDate:i.z.date().default(()=>new Date)}).partial(),i.z.object({clientId:i.z.string(),projectId:i.z.string().optional(),orderId:i.z.string().optional(),contractId:i.z.string().optional(),invoiceNumber:i.z.string().min(1),description:i.z.string().optional(),subtotal:i.z.number().positive(),taxAmount:i.z.number().default(0),totalAmount:i.z.number().positive(),status:i.z.enum(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),issueDate:i.z.date().default(()=>new Date),dueDate:i.z.date(),paidAt:i.z.date().optional()}).partial(),i.z.object({title:i.z.string().min(1),description:i.z.string().min(1),requirements:i.z.string().min(1),location:i.z.string().min(1),employmentType:i.z.string().min(1),salaryMin:i.z.number().positive().optional(),salaryMax:i.z.number().positive().optional(),salaryCurrency:i.z.string().default("USD"),isRemote:i.z.boolean().default(!1),isActive:i.z.boolean().default(!0),expiresAt:i.z.date().optional()}).partial()},96330:t=>{"use strict";t.exports=require("@prisma/client")},96487:()=>{}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),i=e.X(0,[4447,580,5697],()=>r(2773));module.exports=i})();