(()=>{var e={};e.id=1252,e.ids=[1252],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,i)=>{"use strict";i.d(t,{z:()=>n});var r=i(96330);let n=globalThis.prisma??new r.PrismaClient},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48835:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>w,routeModule:()=>z,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var r={};i.r(r),i.d(r,{DELETE:()=>p,GET:()=>d,PATCH:()=>m,PUT:()=>u});var n=i(96559),a=i(48088),o=i(37719),s=i(31183),l=i(53171),c=i(85463);let d=(0,l.FB)(async(e,{params:t})=>{let{id:i}=t,r=await s.z.service.findUnique({where:{id:i},include:{category:{select:{id:!0,name:!0,description:!0}},serviceOptions:{include:{features:!0},orderBy:{createdAt:"asc"}},projects:{select:{id:!0,name:!0,status:!0,client:{select:{id:!0,companyName:!0}}},take:5,orderBy:{createdAt:"desc"}},_count:{select:{projects:!0,orderDetails:!0,serviceOptions:!0}}}});if(!r)throw new l.hD("Service not found",404,"NOT_FOUND");return(0,l.r6)(r)}),u=(0,l.FB)(async(e,{params:t})=>{await (0,l.ZT)(e),(0,l.yX)(e,["PUT"]);let{id:i}=t,r=(0,l.S)(c.c5),n=await r(e);if(!await s.z.service.findUnique({where:{id:i}}))throw new l.hD("Service not found",404,"NOT_FOUND");if(n.categoryId&&!await s.z.category.findUnique({where:{id:n.categoryId}}))throw new l.hD("Category not found",404,"CATEGORY_NOT_FOUND");let a=await s.z.service.update({where:{id:i},data:n,include:{category:{select:{id:!0,name:!0,description:!0}},serviceOptions:{include:{features:!0}},_count:{select:{projects:!0,orderDetails:!0,serviceOptions:!0}}}});return(0,l.r6)(a,"Service updated successfully")}),p=(0,l.FB)(async(e,{params:t})=>{await (0,l.ZT)(e),(0,l.yX)(e,["DELETE"]);let{id:i}=t,r=await s.z.service.findUnique({where:{id:i},include:{projects:{select:{id:!0}},orderDetails:{select:{id:!0}},serviceOptions:{select:{id:!0}}}});if(!r)throw new l.hD("Service not found",404,"NOT_FOUND");if(r.projects.length>0)throw new l.hD("Cannot delete service that is used in projects. Please remove it from projects first.",400,"SERVICE_IN_USE");if(r.orderDetails.length>0)throw new l.hD("Cannot delete service that is used in orders. Please remove it from orders first.",400,"SERVICE_IN_USE");return r.serviceOptions.length>0&&await s.z.serviceOption.deleteMany({where:{serviceId:i}}),await s.z.service.delete({where:{id:i}}),(0,l.r6)(null,"Service deleted successfully")}),m=(0,l.FB)(async(e,{params:t})=>{await (0,l.ZT)(e),(0,l.yX)(e,["PATCH"]);let{id:i}=t,r=await e.json();if(!await s.z.service.findUnique({where:{id:i}}))throw new l.hD("Service not found",404,"NOT_FOUND");let n={};for(let e of["isActive","displayOrder","isFeatured"])e in r&&(n[e]=r[e]);if(0===Object.keys(n).length)throw new l.hD("No valid fields to update",400,"NO_VALID_FIELDS");let a=await s.z.service.update({where:{id:i},data:n,include:{category:{select:{id:!0,name:!0}}}});return(0,l.r6)(a,"Service updated successfully")}),z=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/services/[id]/route",pathname:"/api/services/[id]",filename:"route",bundlePath:"app/api/services/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:h}=z;function w(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},53171:(e,t,i)=>{"use strict";i.d(t,{FB:()=>m,Hx:()=>p,S:()=>d,ZT:()=>f,du:()=>w,hD:()=>o,r6:()=>s,vA:()=>u,vS:()=>c,yX:()=>z,zJ:()=>h});var r=i(32190),n=i(45697),a=i(96330);class o extends Error{constructor(e,t=500,i){super(e),this.message=e,this.statusCode=t,this.code=i,this.name="ApiError"}}function s(e,t,i=200){return r.NextResponse.json({success:!0,data:e,message:t},{status:i})}function l(e,t=500,i){let n=e instanceof Error?e.message:e;return r.NextResponse.json({success:!1,error:n,code:i},{status:t})}function c(e,t,i,n,a){let o=Math.ceil(n/i);return r.NextResponse.json({success:!0,data:e,message:a,pagination:{page:t,limit:i,total:n,totalPages:o}})}function d(e){return async t=>{try{let i=await t.json();return e.parse(i)}catch(e){if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");throw new o(`Validation error: ${t}`,400,"VALIDATION_ERROR")}throw new o("Invalid request body",400,"INVALID_BODY")}}}function u(e){let{searchParams:t}=new URL(e.url);return{page:parseInt(t.get("page")||"1"),limit:Math.min(parseInt(t.get("limit")||"10"),100),search:t.get("search")||void 0,sortBy:t.get("sortBy")||void 0,sortOrder:t.get("sortOrder")||"desc",filter:t.get("filter")||void 0}}function p(e,t){return{skip:(e-1)*t,take:t}}function m(e){return async(t,i)=>{try{return await e(t,i)}catch(e){if(console.error("API Error:",e),e instanceof o)return l(e.message,e.statusCode,e.code);if(e instanceof n.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");return l(`Validation error: ${t}`,400,"VALIDATION_ERROR")}if(e instanceof a.Prisma.PrismaClientKnownRequestError)switch(e.code){case"P2002":return l("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return l("Record not found",404,"NOT_FOUND");case"P2003":return l("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return l("Database error occurred",500,"DATABASE_ERROR")}return l("Internal server error",500,"INTERNAL_ERROR")}}}function z(e,t){if(!t.includes(e.method))throw new o(`Method ${e.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function g(e){if(!e.headers.get("authorization"))throw new o("Authentication required",401,"UNAUTHORIZED");return{id:"admin-user-id",email:"<EMAIL>",role:"ADMIN"}}async function f(e){let t=await g(e);if("ADMIN"!==t.role)throw new o("Admin access required",403,"FORBIDDEN");return t}function h(e,t){return e?{OR:t.map(t=>({[t]:{contains:e,mode:"insensitive"}}))}:{}}function w(e,t="desc"){return e?{[e]:t}:{createdAt:t}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},85463:(e,t,i)=>{"use strict";i.d(t,{Or:()=>s,Zi:()=>o,c5:()=>a,j6:()=>n,ue:()=>c,xc:()=>l});var r=i(45697);r.z.object({email:r.z.string().email(),firstName:r.z.string().optional(),lastName:r.z.string().optional(),imageUrl:r.z.string().url().optional(),role:r.z.enum(["ADMIN","USER","CLIENT"]).default("USER")}).partial();let n=r.z.object({categoryId:r.z.string(),name:r.z.string().min(1).max(255),description:r.z.string().min(1),iconClass:r.z.string().max(100).optional(),price:r.z.number().positive(),discountRate:r.z.number().int().min(0).max(100).optional(),totalDiscount:r.z.number().optional(),manager:r.z.string().max(50).optional(),isActive:r.z.boolean().default(!0),displayOrder:r.z.number().int().default(0)}),a=n.partial(),o=r.z.object({orderId:r.z.string(),clientId:r.z.string().optional(),name:r.z.string().min(1),description:r.z.string().min(1),goals:r.z.string().optional(),manager:r.z.string().max(10).optional(),startDate:r.z.date().optional(),completionDate:r.z.date().optional(),estimatedCost:r.z.number().positive().optional(),estimatedTime:r.z.string().max(10).optional(),estimatedEffort:r.z.string().max(10).optional(),status:r.z.enum(["PLANNING","IN_PROGRESS","COMPLETED","ON_HOLD","CANCELLED"]).default("PLANNING"),isFeatured:r.z.boolean().default(!1),displayOrder:r.z.number().int().default(0),imageUrl:r.z.string().url().optional(),projectUrl:r.z.string().url().optional(),githubUrl:r.z.string().url().optional()}),s=o.partial(),l=r.z.object({userId:r.z.string().optional(),companyName:r.z.string().min(1).max(200),contactName:r.z.string().min(1).max(100),contactPosition:r.z.string().max(100).optional(),contactEmail:r.z.string().email().max(100),contactPhone:r.z.string().max(20).optional(),contactFax:r.z.string().max(20).optional(),companyWebsite:r.z.string().max(100).optional(),address:r.z.string().min(1).max(200),city:r.z.string().min(1).max(100),state:r.z.string().min(1).max(50),zipCode:r.z.string().min(1).max(20),country:r.z.string().min(1).max(100),logoUrl:r.z.string().max(500).optional(),notes:r.z.string().optional()}),c=l.partial();r.z.object({authorId:r.z.string(),title:r.z.string().min(1).max(255),slug:r.z.string().min(1).max(255),content:r.z.string().min(1),excerpt:r.z.string().optional(),featuredImageUrl:r.z.string().max(500).optional(),isPublished:r.z.boolean().default(!1),publishedAt:r.z.date().optional(),categories:r.z.string().optional(),tags:r.z.string().optional()}).partial(),r.z.object({firstName:r.z.string().min(1),lastName:r.z.string().min(1),position:r.z.string().min(1),department:r.z.string().optional(),phone:r.z.string().min(1).max(12),email:r.z.string().email().max(255).optional(),salary:r.z.number().positive().optional(),payrollMethod:r.z.string().max(10).optional(),resumeUrl:r.z.string().max(500).optional(),notes:r.z.string().optional(),bio:r.z.string().optional(),photoUrl:r.z.string().max(500).optional(),linkedInUrl:r.z.string().max(500).optional(),twitterUrl:r.z.string().max(500).optional(),githubUrl:r.z.string().max(500).optional(),displayOrder:r.z.number().int().default(0),isActive:r.z.boolean().default(!0)}).partial(),r.z.object({name:r.z.string().min(1),description:r.z.string().optional(),iconUrl:r.z.string().url().optional(),displayOrder:r.z.number().int().default(0),isActive:r.z.boolean().default(!0)}).partial(),r.z.object({clientName:r.z.string().min(1).max(100),clientTitle:r.z.string().min(1).max(100),clientCompany:r.z.string().min(1).max(100),clientPhotoUrl:r.z.string().max(500).optional(),content:r.z.string().min(1),rating:r.z.number().int().min(1).max(5).default(5),isFeatured:r.z.boolean().default(!1),displayOrder:r.z.number().int().default(0)}).partial(),r.z.object({userId:r.z.string().optional(),name:r.z.string().min(1),email:r.z.string().email(),phone:r.z.string().optional(),company:r.z.string().optional(),subject:r.z.string().min(1),message:r.z.string().min(1),status:r.z.enum(["NEW","IN_PROGRESS","RESOLVED","CLOSED"]).default("NEW")}).partial(),r.z.object({name:r.z.string().min(1),description:r.z.string().optional(),parentId:r.z.string().optional(),isActive:r.z.boolean().default(!0),displayOrder:r.z.number().int().default(0)}).partial(),r.z.object({clientId:r.z.string(),orderNumber:r.z.string().min(1),description:r.z.string().optional(),totalAmount:r.z.number().positive(),status:r.z.enum(["PENDING","CONFIRMED","IN_PROGRESS","COMPLETED","CANCELLED"]).default("PENDING"),orderDate:r.z.date().default(()=>new Date)}).partial(),r.z.object({clientId:r.z.string(),projectId:r.z.string().optional(),orderId:r.z.string().optional(),contractId:r.z.string().optional(),invoiceNumber:r.z.string().min(1),description:r.z.string().optional(),subtotal:r.z.number().positive(),taxAmount:r.z.number().default(0),totalAmount:r.z.number().positive(),status:r.z.enum(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),issueDate:r.z.date().default(()=>new Date),dueDate:r.z.date(),paidAt:r.z.date().optional()}).partial(),r.z.object({title:r.z.string().min(1),description:r.z.string().min(1),requirements:r.z.string().min(1),location:r.z.string().min(1),employmentType:r.z.string().min(1),salaryMin:r.z.number().positive().optional(),salaryMax:r.z.number().positive().optional(),salaryCurrency:r.z.string().default("USD"),isRemote:r.z.boolean().default(!1),isActive:r.z.boolean().default(!0),expiresAt:r.z.date().optional()}).partial()},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[4447,580,5697],()=>i(48835));module.exports=r})();