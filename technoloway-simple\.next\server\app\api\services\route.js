(()=>{var t={};t.id=1438,t.ids=[1438],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:t=>{"use strict";t.exports=require("querystring")},12412:t=>{"use strict";t.exports=require("assert")},28354:t=>{"use strict";t.exports=require("util")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(t,e,r)=>{"use strict";r.d(e,{z:()=>n});var i=r(96330);let n=globalThis.prisma??new i.PrismaClient},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(t,e,r)=>{"use strict";r.d(e,{FB:()=>d,Hx:()=>m,S:()=>c,ZT:()=>f,du:()=>v,hD:()=>s,oC:()=>g,r6:()=>o,vA:()=>p,vS:()=>u,yX:()=>z,z9:()=>x,zJ:()=>y});var i=r(32190),n=r(45697),a=r(96330);class s extends Error{constructor(t,e=500,r){super(t),this.message=t,this.statusCode=e,this.code=r,this.name="ApiError"}}function o(t,e,r=200){return i.NextResponse.json({success:!0,data:t,message:e},{status:r})}function l(t,e=500,r){let n=t instanceof Error?t.message:t;return i.NextResponse.json({success:!1,error:n,code:r},{status:e})}function u(t,e,r,n,a){let s=Math.ceil(n/r);return i.NextResponse.json({success:!0,data:t,message:a,pagination:{page:e,limit:r,total:n,totalPages:s}})}function c(t){return async e=>{try{let r=await e.json();return t.parse(r)}catch(t){if(t instanceof n.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");throw new s(`Validation error: ${e}`,400,"VALIDATION_ERROR")}throw new s("Invalid request body",400,"INVALID_BODY")}}}function p(t){let{searchParams:e}=new URL(t.url);return{page:parseInt(e.get("page")||"1"),limit:Math.min(parseInt(e.get("limit")||"10"),100),search:e.get("search")||void 0,sortBy:e.get("sortBy")||void 0,sortOrder:e.get("sortOrder")||"desc",filter:e.get("filter")||void 0}}function m(t,e){return{skip:(t-1)*e,take:e}}function d(t){return async(e,r)=>{try{return await t(e,r)}catch(t){if(console.error("API Error:",t),t instanceof s)return l(t.message,t.statusCode,t.code);if(t instanceof n.G){let e=t.errors.map(t=>`${t.path.join(".")}: ${t.message}`).join(", ");return l(`Validation error: ${e}`,400,"VALIDATION_ERROR")}if(t instanceof a.Prisma.PrismaClientKnownRequestError)switch(t.code){case"P2002":return l("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return l("Record not found",404,"NOT_FOUND");case"P2003":return l("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return l("Database error occurred",500,"DATABASE_ERROR")}return l("Internal server error",500,"INTERNAL_ERROR")}}}function z(t,e){if(!e.includes(t.method))throw new s(`Method ${t.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function g(t){let{getServerSession:e}=await r.e(5426).then(r.bind(r,35426)),{authOptions:i}=await Promise.all([r.e(1024),r.e(2909)]).then(r.bind(r,12909)),n=await e(i);if(!n||!n.user)throw new s("Authentication required",401,"UNAUTHORIZED");return{id:n.user.id,email:n.user.email,role:n.user.role,name:n.user.name}}async function f(t){let e=await g(t);if("ADMIN"!==e.role)throw new s("Admin access required",403,"FORBIDDEN");return e}function x(t){return t.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function y(t,e){return t?{OR:e.map(e=>({[e]:{contains:t,mode:"insensitive"}}))}:{}}function v(t,e="desc"){return t?{[t]:e}:{createdAt:e}}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:t=>{"use strict";t.exports=require("zlib")},78335:()=>{},79428:t=>{"use strict";t.exports=require("buffer")},79551:t=>{"use strict";t.exports=require("url")},81630:t=>{"use strict";t.exports=require("http")},84537:(t,e,r)=>{"use strict";r.r(e),r.d(e,{patchFetch:()=>y,routeModule:()=>z,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{DELETE:()=>d,GET:()=>c,POST:()=>p,PUT:()=>m});var n=r(96559),a=r(48088),s=r(37719),o=r(31183),l=r(53171),u=r(85463);let c=(0,l.FB)(async t=>{let{page:e,limit:r,search:i,sortBy:n,sortOrder:a,filter:s}=(0,l.vA)(t),{skip:u,take:c}=(0,l.Hx)(e,r),p={};i&&Object.assign(p,(0,l.zJ)(i,["name","description"])),"active"===s?p.isActive=!0:"inactive"===s&&(p.isActive=!1);let m=await o.z.service.count({where:p}),d=await o.z.service.findMany({where:p,include:{category:{select:{id:!0,name:!0}},_count:{select:{projects:!0,orderDetails:!0}}},orderBy:(0,l.du)(n,a),skip:u,take:c});return(0,l.vS)(d,e,r,m)}),p=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["POST"]);let e=(0,l.S)(u.j6),r=await e(t);if(!await o.z.category.findUnique({where:{id:r.categoryId}}))throw Error("Category not found");let i=await o.z.service.create({data:r,include:{category:{select:{id:!0,name:!0}}}});return(0,l.r6)(i,"Service created successfully",201)}),m=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["PUT"]);let{ids:e,data:r}=await t.json();if(!Array.isArray(e)||0===e.length)throw Error("Invalid service IDs provided");let i=(0,l.S)(u.c5),n=await i({json:()=>r}),a=await o.z.service.updateMany({where:{id:{in:e}},data:n});return(0,l.r6)({count:a.count},`${a.count} services updated successfully`)}),d=(0,l.FB)(async t=>{await (0,l.ZT)(t),(0,l.yX)(t,["DELETE"]);let{ids:e}=await t.json();if(!Array.isArray(e)||0===e.length)throw Error("Invalid service IDs provided");let r=await o.z.service.findMany({where:{id:{in:e},OR:[{projects:{some:{}}},{orderDetails:{some:{}}}]},select:{id:!0,name:!0}});if(r.length>0){let t=r.map(t=>t.name).join(", ");throw Error(`Cannot delete services that are in use: ${t}. Please remove them from projects and orders first.`)}let i=await o.z.service.deleteMany({where:{id:{in:e}}});return(0,l.r6)({count:i.count},`${i.count} services deleted successfully`)}),z=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/services/route",pathname:"/api/services",filename:"route",bundlePath:"app/api/services/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\services\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:x}=z;function y(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},85463:(t,e,r)=>{"use strict";r.d(e,{$b:()=>g,AF:()=>c,Gn:()=>f,N6:()=>m,Or:()=>o,Wc:()=>d,ZC:()=>x,Zi:()=>s,c5:()=>a,j6:()=>n,mQ:()=>z,nx:()=>p,ue:()=>u,xc:()=>l});var i=r(45697);i.z.object({email:i.z.string().email(),firstName:i.z.string().optional(),lastName:i.z.string().optional(),imageUrl:i.z.string().url().optional(),role:i.z.enum(["ADMIN","USER","CLIENT"]).default("USER")}).partial();let n=i.z.object({categoryId:i.z.string(),name:i.z.string().min(1).max(255),description:i.z.string().min(1),iconClass:i.z.string().max(100).optional(),price:i.z.number().positive(),discountRate:i.z.number().int().min(0).max(100).optional(),totalDiscount:i.z.number().optional(),manager:i.z.string().max(50).optional(),isActive:i.z.boolean().default(!0),displayOrder:i.z.number().int().default(0)}),a=n.partial(),s=i.z.object({orderId:i.z.string(),clientId:i.z.string().optional(),name:i.z.string().min(1),description:i.z.string().min(1),goals:i.z.string().optional(),manager:i.z.string().max(10).optional(),startDate:i.z.date().optional(),completionDate:i.z.date().optional(),estimatedCost:i.z.number().positive().optional(),estimatedTime:i.z.string().max(10).optional(),estimatedEffort:i.z.string().max(10).optional(),status:i.z.enum(["PLANNING","IN_PROGRESS","COMPLETED","ON_HOLD","CANCELLED"]).default("PLANNING"),isFeatured:i.z.boolean().default(!1),displayOrder:i.z.number().int().default(0),imageUrl:i.z.string().url().optional(),projectUrl:i.z.string().url().optional(),githubUrl:i.z.string().url().optional()}),o=s.partial(),l=i.z.object({userId:i.z.string().optional(),companyName:i.z.string().min(1).max(200),contactName:i.z.string().min(1).max(100),contactPosition:i.z.string().max(100).optional(),contactEmail:i.z.string().email().max(100),contactPhone:i.z.string().max(20).optional(),contactFax:i.z.string().max(20).optional(),companyWebsite:i.z.string().max(100).optional(),address:i.z.string().min(1).max(200),city:i.z.string().min(1).max(100),state:i.z.string().min(1).max(50),zipCode:i.z.string().min(1).max(20),country:i.z.string().min(1).max(100),logoUrl:i.z.string().max(500).optional(),notes:i.z.string().optional()}),u=l.partial(),c=i.z.object({authorId:i.z.string(),title:i.z.string().min(1).max(255),slug:i.z.string().min(1).max(255),content:i.z.string().min(1),excerpt:i.z.string().optional(),featuredImageUrl:i.z.string().max(500).optional(),isPublished:i.z.boolean().default(!1),publishedAt:i.z.date().optional(),categories:i.z.string().optional(),tags:i.z.string().optional()}),p=c.partial(),m=i.z.object({firstName:i.z.string().min(1),lastName:i.z.string().min(1),position:i.z.string().min(1),department:i.z.string().optional(),phone:i.z.string().min(1).max(12),email:i.z.string().email().max(255).optional(),salary:i.z.number().positive().optional(),payrollMethod:i.z.string().max(10).optional(),resumeUrl:i.z.string().max(500).optional(),notes:i.z.string().optional(),bio:i.z.string().optional(),photoUrl:i.z.string().max(500).optional(),linkedInUrl:i.z.string().max(500).optional(),twitterUrl:i.z.string().max(500).optional(),githubUrl:i.z.string().max(500).optional(),displayOrder:i.z.number().int().default(0),isActive:i.z.boolean().default(!0)}),d=m.partial(),z=i.z.object({name:i.z.string().min(1),description:i.z.string().optional(),iconUrl:i.z.string().url().optional(),displayOrder:i.z.number().int().default(0),isActive:i.z.boolean().default(!0)}),g=z.partial();i.z.object({clientName:i.z.string().min(1).max(100),clientTitle:i.z.string().min(1).max(100),clientCompany:i.z.string().min(1).max(100),clientPhotoUrl:i.z.string().max(500).optional(),content:i.z.string().min(1),rating:i.z.number().int().min(1).max(5).default(5),isFeatured:i.z.boolean().default(!1),displayOrder:i.z.number().int().default(0)}).partial();let f=i.z.object({userId:i.z.string().optional(),name:i.z.string().min(1),email:i.z.string().email(),phone:i.z.string().optional(),company:i.z.string().optional(),subject:i.z.string().min(1),message:i.z.string().min(1),status:i.z.enum(["NEW","IN_PROGRESS","RESOLVED","CLOSED"]).default("NEW")}),x=f.partial();i.z.object({name:i.z.string().min(1),description:i.z.string().optional(),parentId:i.z.string().optional(),isActive:i.z.boolean().default(!0),displayOrder:i.z.number().int().default(0)}).partial(),i.z.object({clientId:i.z.string(),orderNumber:i.z.string().min(1),description:i.z.string().optional(),totalAmount:i.z.number().positive(),status:i.z.enum(["PENDING","CONFIRMED","IN_PROGRESS","COMPLETED","CANCELLED"]).default("PENDING"),orderDate:i.z.date().default(()=>new Date)}).partial(),i.z.object({clientId:i.z.string(),projectId:i.z.string().optional(),orderId:i.z.string().optional(),contractId:i.z.string().optional(),invoiceNumber:i.z.string().min(1),description:i.z.string().optional(),subtotal:i.z.number().positive(),taxAmount:i.z.number().default(0),totalAmount:i.z.number().positive(),status:i.z.enum(["DRAFT","SENT","PAID","OVERDUE","CANCELLED"]).default("DRAFT"),issueDate:i.z.date().default(()=>new Date),dueDate:i.z.date(),paidAt:i.z.date().optional()}).partial(),i.z.object({title:i.z.string().min(1),description:i.z.string().min(1),requirements:i.z.string().min(1),location:i.z.string().min(1),employmentType:i.z.string().min(1),salaryMin:i.z.number().positive().optional(),salaryMax:i.z.number().positive().optional(),salaryCurrency:i.z.string().default("USD"),isRemote:i.z.boolean().default(!1),isActive:i.z.boolean().default(!0),expiresAt:i.z.date().optional()}).partial()},94735:t=>{"use strict";t.exports=require("events")},96330:t=>{"use strict";t.exports=require("@prisma/client")},96487:()=>{}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),i=e.X(0,[4243,580,5697],()=>r(84537));module.exports=i})();