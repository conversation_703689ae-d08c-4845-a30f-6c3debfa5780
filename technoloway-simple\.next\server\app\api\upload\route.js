(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53171:(e,r,t)=>{"use strict";t.d(r,{FB:()=>m,Hx:()=>d,S:()=>l,ZT:()=>h,du:()=>x,hD:()=>a,oC:()=>g,r6:()=>o,vA:()=>p,vS:()=>c,yX:()=>f,z9:()=>w,zJ:()=>y});var i=t(32190),s=t(45697),n=t(96330);class a extends Error{constructor(e,r=500,t){super(e),this.message=e,this.statusCode=r,this.code=t,this.name="ApiError"}}function o(e,r,t=200){return i.NextResponse.json({success:!0,data:e,message:r},{status:t})}function u(e,r=500,t){let s=e instanceof Error?e.message:e;return i.NextResponse.json({success:!1,error:s,code:t},{status:r})}function c(e,r,t,s,n){let a=Math.ceil(s/t);return i.NextResponse.json({success:!0,data:e,message:n,pagination:{page:r,limit:t,total:s,totalPages:a}})}function l(e){return async r=>{try{let t=await r.json();return e.parse(t)}catch(e){if(e instanceof s.G){let r=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");throw new a(`Validation error: ${r}`,400,"VALIDATION_ERROR")}throw new a("Invalid request body",400,"INVALID_BODY")}}}function p(e){let{searchParams:r}=new URL(e.url);return{page:parseInt(r.get("page")||"1"),limit:Math.min(parseInt(r.get("limit")||"10"),100),search:r.get("search")||void 0,sortBy:r.get("sortBy")||void 0,sortOrder:r.get("sortOrder")||"desc",filter:r.get("filter")||void 0}}function d(e,r){return{skip:(e-1)*r,take:r}}function m(e){return async(r,t)=>{try{return await e(r,t)}catch(e){if(console.error("API Error:",e),e instanceof a)return u(e.message,e.statusCode,e.code);if(e instanceof s.G){let r=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`).join(", ");return u(`Validation error: ${r}`,400,"VALIDATION_ERROR")}if(e instanceof n.Prisma.PrismaClientKnownRequestError)switch(e.code){case"P2002":return u("A record with this data already exists",409,"DUPLICATE_RECORD");case"P2025":return u("Record not found",404,"NOT_FOUND");case"P2003":return u("Foreign key constraint failed",400,"FOREIGN_KEY_ERROR");default:return u("Database error occurred",500,"DATABASE_ERROR")}return u("Internal server error",500,"INTERNAL_ERROR")}}}function f(e,r){if(!r.includes(e.method))throw new a(`Method ${e.method} not allowed`,405,"METHOD_NOT_ALLOWED")}async function g(e){let{getServerSession:r}=await t.e(5426).then(t.bind(t,35426)),{authOptions:i}=await Promise.all([t.e(1024),t.e(2909)]).then(t.bind(t,12909)),s=await r(i);if(!s||!s.user)throw new a("Authentication required",401,"UNAUTHORIZED");return{id:s.user.id,email:s.user.email,role:s.user.role,name:s.user.name}}async function h(e){let r=await g(e);if("ADMIN"!==r.role)throw new a("Admin access required",403,"FORBIDDEN");return r}function w(e){return e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function y(e,r){return e?{OR:r.map(r=>({[r]:{contains:e,mode:"insensitive"}}))}:{}}function x(e,r="desc"){return e?{[e]:r}:{createdAt:r}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88631:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>U,routeModule:()=>_,serverHooks:()=>j,workAsyncStorage:()=>L,workUnitAsyncStorage:()=>P});var i={};t.r(i),t.d(i,{GET:()=>T,POST:()=>F});var s=t(96559),n=t(48088),a=t(37719),o=t(53171);let u=require("fs/promises"),c=require("fs");var l=t(33873),p=t.n(l);let d=require("node:crypto");var m=t.n(d);let f={randomUUID:m().randomUUID},g=new Uint8Array(256),h=g.length,w=[];for(let e=0;e<256;++e)w.push((e+256).toString(16).slice(1));let y=function(e,r,t){if(f.randomUUID&&!r&&!e)return f.randomUUID();let i=(e=e||{}).random||(e.rng||function(){return h>g.length-16&&(m().randomFillSync(g),h=0),g.slice(h,h+=16)})();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,r){t=t||0;for(let e=0;e<16;++e)r[t+e]=i[e];return r}return function(e,r=0){return(w[e[r+0]]+w[e[r+1]]+w[e[r+2]]+w[e[r+3]]+"-"+w[e[r+4]]+w[e[r+5]]+"-"+w[e[r+6]]+w[e[r+7]]+"-"+w[e[r+8]]+w[e[r+9]]+"-"+w[e[r+10]]+w[e[r+11]]+w[e[r+12]]+w[e[r+13]]+w[e[r+14]]+w[e[r+15]]).toLowerCase()}(i)},x=require("sharp");var E=t.n(x);let I={maxFileSize:parseInt(process.env.MAX_FILE_SIZE||"10485760"),allowedTypes:(process.env.ALLOWED_FILE_TYPES||"image/jpeg,image/png,image/gif,image/webp,application/pdf").split(","),uploadDir:process.env.UPLOAD_DIR||"./public/uploads",imageQuality:85,maxImageWidth:1920,maxImageHeight:1080};class v extends Error{constructor(e,r){super(e),this.code=r,this.name="FileUploadError"}}async function R(e){let r=e?p().join(I.uploadDir,e):I.uploadDir;return(0,c.existsSync)(r)||await (0,u.mkdir)(r,{recursive:!0}),r}async function A(e,r,t){let{width:i,height:s,quality:n}={width:t?.width||I.maxImageWidth,height:t?.height||I.maxImageHeight,quality:t?.quality||I.imageQuality};return E()(e).resize(i,s,{fit:"inside",withoutEnlargement:!0}).jpeg({quality:n}).toBuffer()}async function D(e,r,t=!0){if(!I.allowedTypes.includes(e.type))throw new v(`File type ${e.type} is not allowed`,"INVALID_FILE_TYPE");if(!(e.size<=I.maxFileSize))throw new v(`File size ${e.size} exceeds maximum allowed size of ${I.maxFileSize} bytes`,"FILE_TOO_LARGE");let i=function(e){let r=p().extname(e),t=p().basename(e,r),i=Date.now(),s=y().slice(0,8);return`${t}-${i}-${s}${r}`}(e.name),s=await R(r),n=p().join(s,i),a=await e.arrayBuffer(),o=Buffer.from(a);if(t&&e.type.startsWith("image/"))try{o=await A(o,i)}catch(e){console.warn("Image processing failed, using original:",e)}await (0,u.writeFile)(n,o);let c=n.replace("./public","").replace(/\\/g,"/");return{filename:i,originalName:e.name,size:o.length,mimeType:e.type,url:c,path:n}}async function q(e,r,t=!0){return Promise.all(e.map(e=>D(e,r,t)))}async function O(e){let r=await e.formData(),t=[],i={};for(let[e,s]of r.entries())s instanceof File?t.push(s):i[e]=s;return{files:t,fields:i}}let F=(0,o.FB)(async e=>{await (0,o.oC)(e),(0,o.yX)(e,["POST"]);try{let{files:r,fields:t}=await O(e);if(0===r.length)throw Error("No files provided");let i=t.category||"general",s=(await q(r,i,!0)).map(e=>({...e,category:function(e){return e.startsWith("image/")?"image":e.startsWith("video/")?"video":e.startsWith("audio/")?"audio":e.includes("pdf")?"pdf":e.includes("word")||e.includes("document")?"document":e.includes("spreadsheet")||e.includes("excel")?"spreadsheet":"other"}(e.mimeType)}));return(0,o.r6)({files:s,count:s.length},`${s.length} file(s) uploaded successfully`)}catch(e){if(e instanceof v)throw e;throw Error(`Upload failed: ${e instanceof Error?e.message:"Unknown error"}`)}}),T=(0,o.FB)(async e=>(await (0,o.oC)(e),(0,o.r6)({maxFileSize:parseInt(process.env.MAX_FILE_SIZE||"10485760"),allowedTypes:(process.env.ALLOWED_FILE_TYPES||"image/jpeg,image/png,image/gif,image/webp,application/pdf").split(","),categories:["general","profile","project","blog","document","logo"]}))),_=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:L,workUnitAsyncStorage:P,serverHooks:j}=_;function U(){return(0,a.patchFetch)({workAsyncStorage:L,workUnitAsyncStorage:P})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4243,580,5697],()=>t(88631));module.exports=i})();