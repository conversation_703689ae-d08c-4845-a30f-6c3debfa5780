(()=>{var e={};e.id=977,e.ids=[977],e.modules={137:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29658:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>rd});var s,i,n,o,l=r(60687),d=r(43210),u=r(26001),c=e=>"checkbox"===e.type,h=e=>e instanceof Date,m=e=>null==e;let p=e=>"object"==typeof e;var f=e=>!m(e)&&!Array.isArray(e)&&p(e)&&!h(e),y=e=>f(e)&&e.target?c(e.target)?e.target.checked:e.target.value:e,g=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,v=(e,t)=>e.has(g(t)),_=e=>{let t=e.constructor&&e.constructor.prototype;return f(t)&&t.hasOwnProperty("isPrototypeOf")},b="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function x(e){let t,r=Array.isArray(e),a="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(b&&(e instanceof Blob||a))&&(r||f(e))))return e;else if(t=r?[]:{},r||_(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=x(e[r]));else t=e;return t}var w=e=>Array.isArray(e)?e.filter(Boolean):[],k=e=>void 0===e,j=(e,t,r)=>{if(!t||!f(e))return r;let a=w(t.split(/[,[\].]+?/)).reduce((e,t)=>m(e)?e:e[t],e);return k(a)||a===e?k(e[t])?r:e[t]:a},N=e=>"boolean"==typeof e,A=e=>/^\w*$/.test(e),T=e=>w(e.replace(/["|']|\]/g,"").split(/\.|\[/)),S=(e,t,r)=>{let a=-1,s=A(t)?[t]:T(t),i=s.length,n=i-1;for(;++a<i;){let t=s[a],i=r;if(a!==n){let r=e[t];i=f(r)||Array.isArray(r)?r:isNaN(+s[a+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};let O={BLUR:"blur",FOCUS_OUT:"focusout"},C={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},E={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},P=d.createContext(null);var Z=(e,t,r,a=!0)=>{let s={defaultValues:t._defaultValues};for(let i in e)Object.defineProperty(s,i,{get:()=>(t._proxyFormState[i]!==C.all&&(t._proxyFormState[i]=!a||C.all),r&&(r[i]=!0),e[i])});return s};let F="undefined"!=typeof window?d.useLayoutEffect:d.useEffect;var V=e=>"string"==typeof e,I=(e,t,r,a,s)=>V(e)?(a&&t.watch.add(e),j(r,e,s)):Array.isArray(e)?e.map(e=>(a&&t.watch.add(e),j(r,e))):(a&&(t.watchAll=!0),r),D=(e,t,r,a,s)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[a]:s||!0}}:{},R=e=>Array.isArray(e)?e:[e],M=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},$=e=>m(e)||!p(e);function L(e,t){if($(e)||$(t))return e===t;if(h(e)&&h(t))return e.getTime()===t.getTime();let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let s of r){let r=e[s];if(!a.includes(s))return!1;if("ref"!==s){let e=t[s];if(h(r)&&h(e)||f(r)&&f(e)||Array.isArray(r)&&Array.isArray(e)?!L(r,e):r!==e)return!1}}return!0}var z=e=>f(e)&&!Object.keys(e).length,U=e=>"file"===e.type,B=e=>"function"==typeof e,q=e=>{if(!b)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},W=e=>"select-multiple"===e.type,K=e=>"radio"===e.type,G=e=>K(e)||c(e),H=e=>q(e)&&e.isConnected;function Y(e,t){let r=Array.isArray(t)?t:A(t)?[t]:T(t),a=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,a=0;for(;a<r;)e=k(e)?a++:e[t[a++]];return e}(e,r),s=r.length-1,i=r[s];return a&&delete a[i],0!==s&&(f(a)&&z(a)||Array.isArray(a)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!k(e[t]))return!1;return!0}(a))&&Y(e,r.slice(0,-1)),e}var J=e=>{for(let t in e)if(B(e[t]))return!0;return!1};function X(e,t={}){let r=Array.isArray(e);if(f(e)||r)for(let r in e)Array.isArray(e[r])||f(e[r])&&!J(e[r])?(t[r]=Array.isArray(e[r])?[]:{},X(e[r],t[r])):m(e[r])||(t[r]=!0);return t}var Q=(e,t)=>(function e(t,r,a){let s=Array.isArray(t);if(f(t)||s)for(let s in t)Array.isArray(t[s])||f(t[s])&&!J(t[s])?k(r)||$(a[s])?a[s]=Array.isArray(t[s])?X(t[s],[]):{...X(t[s])}:e(t[s],m(r)?{}:r[s],a[s]):a[s]=!L(t[s],r[s]);return a})(e,t,X(t));let ee={value:!1,isValid:!1},et={value:!0,isValid:!0};var er=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!k(e[0].attributes.value)?k(e[0].value)||""===e[0].value?et:{value:e[0].value,isValid:!0}:et:ee}return ee},ea=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:a})=>k(e)?e:t?""===e?NaN:e?+e:e:r&&V(e)?new Date(e):a?a(e):e;let es={isValid:!1,value:null};var ei=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,es):es;function en(e){let t=e.ref;return U(t)?t.files:K(t)?ei(e.refs).value:W(t)?[...t.selectedOptions].map(({value:e})=>e):c(t)?er(e.refs).value:ea(k(t.value)?e.ref.value:t.value,e)}var eo=(e,t,r,a)=>{let s={};for(let r of e){let e=j(t,r);e&&S(s,r,e._f)}return{criteriaMode:r,names:[...e],fields:s,shouldUseNativeValidation:a}},el=e=>e instanceof RegExp,ed=e=>k(e)?e:el(e)?e.source:f(e)?el(e.value)?e.value.source:e.value:e,eu=e=>({isOnSubmit:!e||e===C.onSubmit,isOnBlur:e===C.onBlur,isOnChange:e===C.onChange,isOnAll:e===C.all,isOnTouch:e===C.onTouched});let ec="AsyncFunction";var eh=e=>!!e&&!!e.validate&&!!(B(e.validate)&&e.validate.constructor.name===ec||f(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===ec)),em=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),ep=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ef=(e,t,r,a)=>{for(let s of r||Object.keys(e)){let r=j(e,s);if(r){let{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],s)&&!a)return!0;else if(e.ref&&t(e.ref,e.name)&&!a)return!0;else if(ef(i,t))break}else if(f(i)&&ef(i,t))break}}};function ey(e,t,r){let a=j(e,r);if(a||A(r))return{error:a,name:r};let s=r.split(".");for(;s.length;){let a=s.join("."),i=j(t,a),n=j(e,a);if(i&&!Array.isArray(i)&&r!==a)break;if(n&&n.type)return{name:a,error:n};if(n&&n.root&&n.root.type)return{name:`${a}.root`,error:n.root};s.pop()}return{name:r}}var eg=(e,t,r,a)=>{r(e);let{name:s,...i}=e;return z(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!a||C.all))},ev=(e,t,r)=>!e||!t||e===t||R(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),e_=(e,t,r,a,s)=>!s.isOnAll&&(!r&&s.isOnTouch?!(t||e):(r?a.isOnBlur:s.isOnBlur)?!e:(r?!a.isOnChange:!s.isOnChange)||e),eb=(e,t)=>!w(j(e,t)).length&&Y(e,t),ex=(e,t,r)=>{let a=R(j(e,r));return S(a,"root",t[r]),S(e,r,a),e},ew=e=>V(e);function ek(e,t,r="validate"){if(ew(e)||Array.isArray(e)&&e.every(ew)||N(e)&&!e)return{type:r,message:ew(e)?e:"",ref:t}}var ej=e=>f(e)&&!el(e)?e:{value:e,message:""},eN=async(e,t,r,a,s,i)=>{let{ref:n,refs:o,required:l,maxLength:d,minLength:u,min:h,max:p,pattern:y,validate:g,name:v,valueAsNumber:_,mount:b}=e._f,x=j(r,v);if(!b||t.has(v))return{};let w=o?o[0]:n,A=e=>{s&&w.reportValidity&&(w.setCustomValidity(N(e)?"":e||""),w.reportValidity())},T={},S=K(n),O=c(n),C=(_||U(n))&&k(n.value)&&k(x)||q(n)&&""===n.value||""===x||Array.isArray(x)&&!x.length,P=D.bind(null,v,a,T),Z=(e,t,r,a=E.maxLength,s=E.minLength)=>{let i=e?t:r;T[v]={type:e?a:s,message:i,ref:n,...P(e?a:s,i)}};if(i?!Array.isArray(x)||!x.length:l&&(!(S||O)&&(C||m(x))||N(x)&&!x||O&&!er(o).isValid||S&&!ei(o).isValid)){let{value:e,message:t}=ew(l)?{value:!!l,message:l}:ej(l);if(e&&(T[v]={type:E.required,message:t,ref:w,...P(E.required,t)},!a))return A(t),T}if(!C&&(!m(h)||!m(p))){let e,t,r=ej(p),s=ej(h);if(m(x)||isNaN(x)){let a=n.valueAsDate||new Date(x),i=e=>new Date(new Date().toDateString()+" "+e),o="time"==n.type,l="week"==n.type;V(r.value)&&x&&(e=o?i(x)>i(r.value):l?x>r.value:a>new Date(r.value)),V(s.value)&&x&&(t=o?i(x)<i(s.value):l?x<s.value:a<new Date(s.value))}else{let a=n.valueAsNumber||(x?+x:x);m(r.value)||(e=a>r.value),m(s.value)||(t=a<s.value)}if((e||t)&&(Z(!!e,r.message,s.message,E.max,E.min),!a))return A(T[v].message),T}if((d||u)&&!C&&(V(x)||i&&Array.isArray(x))){let e=ej(d),t=ej(u),r=!m(e.value)&&x.length>+e.value,s=!m(t.value)&&x.length<+t.value;if((r||s)&&(Z(r,e.message,t.message),!a))return A(T[v].message),T}if(y&&!C&&V(x)){let{value:e,message:t}=ej(y);if(el(e)&&!x.match(e)&&(T[v]={type:E.pattern,message:t,ref:n,...P(E.pattern,t)},!a))return A(t),T}if(g){if(B(g)){let e=ek(await g(x,r),w);if(e&&(T[v]={...e,...P(E.validate,e.message)},!a))return A(e.message),T}else if(f(g)){let e={};for(let t in g){if(!z(e)&&!a)break;let s=ek(await g[t](x,r),w,t);s&&(e={...s,...P(t,s.message)},A(s.message),a&&(T[v]=e))}if(!z(e)&&(T[v]={ref:w,...e},!a))return T}}return A(!0),T};let eA={mode:C.onSubmit,reValidateMode:C.onChange,shouldFocusError:!0},eT=(e,t,r)=>{if(e&&"reportValidity"in e){let a=j(r,t);e.setCustomValidity(a&&a.message||""),e.reportValidity()}},eS=(e,t)=>{for(let r in t.fields){let a=t.fields[r];a&&a.ref&&"reportValidity"in a.ref?eT(a.ref,r,e):a&&a.refs&&a.refs.forEach(t=>eT(t,r,e))}},eO=(e,t)=>{t.shouldUseNativeValidation&&eS(e,t);let r={};for(let a in e){let s=j(t.fields,a),i=Object.assign(e[a]||{},{ref:s&&s.ref});if(eC(t.names||Object.keys(e),a)){let e=Object.assign({},j(r,a));S(e,"root",i),S(r,a,e)}else S(r,a,i)}return r},eC=(e,t)=>{let r=eE(t);return e.some(e=>eE(e).match(`^${r}\\.\\d+`))};function eE(e){return e.replace(/\]|\[/g,"")}function eP(e,t,r){function a(r,a){var s;for(let i in Object.defineProperty(r,"_zod",{value:r._zod??{},enumerable:!1}),(s=r._zod).traits??(s.traits=new Set),r._zod.traits.add(e),t(r,a),n.prototype)i in r||Object.defineProperty(r,i,{value:n.prototype[i].bind(r)});r._zod.constr=n,r._zod.def=a}let s=r?.Parent??Object;class i extends s{}function n(e){var t;let s=r?.Parent?new i:this;for(let r of(a(s,e),(t=s._zod).deferred??(t.deferred=[]),s._zod.deferred))r();return s}return Object.defineProperty(i,"name",{value:e}),Object.defineProperty(n,"init",{value:a}),Object.defineProperty(n,Symbol.hasInstance,{value:t=>!!r?.Parent&&t instanceof r.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(n,"name",{value:e}),n}Symbol("zod_brand");class eZ extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let eF={};function eV(e){return e&&Object.assign(eF,e),eF}function eI(e,t){return"bigint"==typeof t?t.toString():t}function eD(e){return"string"==typeof e?e:e?.message}function eR(e,t,r){let a={...e,path:e.path??[]};return e.message||(a.message=eD(e.inst?._zod.def?.error?.(e))??eD(t?.error?.(e))??eD(r.customError?.(e))??eD(r.localeError?.(e))??"Invalid input"),delete a.inst,delete a.continue,t?.reportInput||delete a.input,a}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE;let eM=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(t,eI,2),enumerable:!0})},e$=eP("$ZodError",eM),eL=eP("$ZodError",eM,{Parent:Error}),ez=(e,t,r,a)=>{let s=r?Object.assign(r,{async:!1}):{async:!1},i=e._zod.run({value:t,issues:[]},s);if(i instanceof Promise)throw new eZ;if(i.issues.length){let e=new(a?.Err??eL)(i.issues.map(e=>eR(e,s,eV())));throw Error.captureStackTrace(e,a?.callee),e}return i.value},eU=async(e,t,r,a)=>{let s=r?Object.assign(r,{async:!0}):{async:!0},i=e._zod.run({value:t,issues:[]},s);if(i instanceof Promise&&(i=await i),i.issues.length){let e=new(a?.Err??eL)(i.issues.map(e=>eR(e,s,eV())));throw Error.captureStackTrace(e,a?.callee),e}return i.value};function eB(e,t,r,a){let s=Math.abs(e),i=s%10,n=s%100;return n>=11&&n<=19?a:1===i?t:i>=2&&i<=4?r:a}let eq=e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t};function eW(e,t,r,a){let s=Math.abs(e),i=s%10,n=s%100;return n>=11&&n<=19?a:1===i?t:i>=2&&i<=4?r:a}let eK=e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t};Symbol("ZodOutput"),Symbol("ZodInput");function eG(e,t){try{var r=e()}catch(e){return t(e)}return r&&r.then?r.then(void 0,t):r}!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),a={};for(let e of r)a[e]=t[e];return e.objectValues(a)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(s||(s={})),(i||(i={})).mergeShapes=(e,t)=>({...e,...t});let eH=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),eY=e=>{switch(typeof e){case"undefined":return eH.undefined;case"string":return eH.string;case"number":return Number.isNaN(e)?eH.nan:eH.number;case"boolean":return eH.boolean;case"function":return eH.function;case"bigint":return eH.bigint;case"symbol":return eH.symbol;case"object":if(Array.isArray(e))return eH.array;if(null===e)return eH.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return eH.promise;if("undefined"!=typeof Map&&e instanceof Map)return eH.map;if("undefined"!=typeof Set&&e instanceof Set)return eH.set;if("undefined"!=typeof Date&&e instanceof Date)return eH.date;return eH.object;default:return eH.unknown}},eJ=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class eX extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},a=e=>{for(let s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(a);else if("invalid_return_type"===s.code)a(s.returnTypeError);else if("invalid_arguments"===s.code)a(s.argumentsError);else if(0===s.path.length)r._errors.push(t(s));else{let e=r,a=0;for(;a<s.path.length;){let r=s.path[a];a===s.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(s))):e[r]=e[r]||{_errors:[]},e=e[r],a++}}};return a(this),r}static assert(e){if(!(e instanceof eX))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):r.push(e(a));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}eX.create=e=>new eX(e);let eQ=(e,t)=>{let r;switch(e.code){case eJ.invalid_type:r=e.received===eH.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case eJ.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case eJ.unrecognized_keys:r=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case eJ.invalid_union:r="Invalid input";break;case eJ.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case eJ.invalid_enum_value:r=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case eJ.invalid_arguments:r="Invalid function arguments";break;case eJ.invalid_return_type:r="Invalid function return type";break;case eJ.invalid_date:r="Invalid date";break;case eJ.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case eJ.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case eJ.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case eJ.custom:r="Invalid input";break;case eJ.invalid_intersection_types:r="Intersection results could not be merged";break;case eJ.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case eJ.not_finite:r="Number must be finite";break;default:r=t.defaultError,s.assertNever(e)}return{message:r}},e0=e=>{let{data:t,path:r,errorMaps:a,issueData:s}=e,i=[...r,...s.path||[]],n={...s,path:i};if(void 0!==s.message)return{...s,path:i,message:s.message};let o="";for(let e of a.filter(e=>!!e).slice().reverse())o=e(n,{data:t,defaultError:o}).message;return{...s,path:i,message:o}};function e1(e,t){let r=e0({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,eQ,eQ==eQ?void 0:eQ].filter(e=>!!e)});e.common.issues.push(r)}class e2{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let a of t){if("aborted"===a.status)return e4;"dirty"===a.status&&e.dirty(),r.push(a.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,a=await e.value;r.push({key:t,value:a})}return e2.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let a of t){let{key:t,value:s}=a;if("aborted"===t.status||"aborted"===s.status)return e4;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==s.value||a.alwaysSet)&&(r[t.value]=s.value)}return{status:e.value,value:r}}}let e4=Object.freeze({status:"aborted"}),e5=e=>({status:"dirty",value:e}),e9=e=>({status:"valid",value:e}),e3=e=>"aborted"===e.status,e6=e=>"dirty"===e.status,e7=e=>"valid"===e.status,e8=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(n||(n={}));class te{constructor(e,t,r,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let tt=(e,t)=>{if(e7(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new eX(e.common.issues);return this._error=t,this._error}}};function tr(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:a,description:s}=e;if(t&&(r||a))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:s}:{errorMap:(t,s)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??s.defaultError}:void 0===s.data?{message:i??a??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:i??r??s.defaultError}},description:s}}class ta{get description(){return this._def.description}_getType(e){return eY(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:eY(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new e2,ctx:{common:e.parent.common,data:e.data,parsedType:eY(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(e8(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:eY(e)},a=this._parseSync({data:e,path:r.path,parent:r});return tt(r,a)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:eY(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return e7(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>e7(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:eY(e)},a=this._parse({data:e,path:r.path,parent:r});return tt(r,await (e8(a)?a:Promise.resolve(a)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,a)=>{let s=e(t),i=()=>a.addIssue({code:eJ.custom,...r(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then(e=>!!e||(i(),!1)):!!s||(i(),!1)})}refinement(e,t){return this._refinement((r,a)=>!!e(r)||(a.addIssue("function"==typeof t?t(r,a):t),!1))}_refinement(e){return new tY({schema:this,typeName:o.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return tJ.create(this,this._def)}nullable(){return tX.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return tZ.create(this)}promise(){return tH.create(this,this._def)}or(e){return tV.create([this,e],this._def)}and(e){return tR.create(this,e,this._def)}transform(e){return new tY({...tr(this._def),schema:this,typeName:o.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new tQ({...tr(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:o.ZodDefault})}brand(){return new t2({typeName:o.ZodBranded,type:this,...tr(this._def)})}catch(e){return new t0({...tr(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:o.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return t4.create(this,e)}readonly(){return t5.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let ts=/^c[^\s-]{8,}$/i,ti=/^[0-9a-z]+$/,tn=/^[0-9A-HJKMNP-TV-Z]{26}$/i,to=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,tl=/^[a-z0-9_-]{21}$/i,td=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,tu=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,tc=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,th=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,tm=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,tp=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,tf=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,ty=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,tg=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,tv="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",t_=RegExp(`^${tv}$`);function tb(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}class tx extends ta{_parse(e){var t,r,i,n;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==eH.string){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.string,received:t.parsedType}),e4}let l=new e2;for(let d of this._def.checks)if("min"===d.kind)e.data.length<d.value&&(e1(o=this._getOrReturnCtx(e,o),{code:eJ.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),l.dirty());else if("max"===d.kind)e.data.length>d.value&&(e1(o=this._getOrReturnCtx(e,o),{code:eJ.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),l.dirty());else if("length"===d.kind){let t=e.data.length>d.value,r=e.data.length<d.value;(t||r)&&(o=this._getOrReturnCtx(e,o),t?e1(o,{code:eJ.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}):r&&e1(o,{code:eJ.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}),l.dirty())}else if("email"===d.kind)tc.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"email",code:eJ.invalid_string,message:d.message}),l.dirty());else if("emoji"===d.kind)a||(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:eJ.invalid_string,message:d.message}),l.dirty());else if("uuid"===d.kind)to.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:eJ.invalid_string,message:d.message}),l.dirty());else if("nanoid"===d.kind)tl.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:eJ.invalid_string,message:d.message}),l.dirty());else if("cuid"===d.kind)ts.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:eJ.invalid_string,message:d.message}),l.dirty());else if("cuid2"===d.kind)ti.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:eJ.invalid_string,message:d.message}),l.dirty());else if("ulid"===d.kind)tn.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:eJ.invalid_string,message:d.message}),l.dirty());else if("url"===d.kind)try{new URL(e.data)}catch{e1(o=this._getOrReturnCtx(e,o),{validation:"url",code:eJ.invalid_string,message:d.message}),l.dirty()}else"regex"===d.kind?(d.regex.lastIndex=0,d.regex.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"regex",code:eJ.invalid_string,message:d.message}),l.dirty())):"trim"===d.kind?e.data=e.data.trim():"includes"===d.kind?e.data.includes(d.value,d.position)||(e1(o=this._getOrReturnCtx(e,o),{code:eJ.invalid_string,validation:{includes:d.value,position:d.position},message:d.message}),l.dirty()):"toLowerCase"===d.kind?e.data=e.data.toLowerCase():"toUpperCase"===d.kind?e.data=e.data.toUpperCase():"startsWith"===d.kind?e.data.startsWith(d.value)||(e1(o=this._getOrReturnCtx(e,o),{code:eJ.invalid_string,validation:{startsWith:d.value},message:d.message}),l.dirty()):"endsWith"===d.kind?e.data.endsWith(d.value)||(e1(o=this._getOrReturnCtx(e,o),{code:eJ.invalid_string,validation:{endsWith:d.value},message:d.message}),l.dirty()):"datetime"===d.kind?(function(e){let t=`${tv}T${tb(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)})(d).test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{code:eJ.invalid_string,validation:"datetime",message:d.message}),l.dirty()):"date"===d.kind?t_.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{code:eJ.invalid_string,validation:"date",message:d.message}),l.dirty()):"time"===d.kind?RegExp(`^${tb(d)}$`).test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{code:eJ.invalid_string,validation:"time",message:d.message}),l.dirty()):"duration"===d.kind?tu.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"duration",code:eJ.invalid_string,message:d.message}),l.dirty()):"ip"===d.kind?(t=e.data,!(("v4"===(r=d.version)||!r)&&th.test(t)||("v6"===r||!r)&&tp.test(t))&&1&&(e1(o=this._getOrReturnCtx(e,o),{validation:"ip",code:eJ.invalid_string,message:d.message}),l.dirty())):"jwt"===d.kind?!function(e,t){if(!td.test(e))return!1;try{let[r]=e.split("."),a=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(a));if("object"!=typeof s||null===s||"typ"in s&&s?.typ!=="JWT"||!s.alg||t&&s.alg!==t)return!1;return!0}catch{return!1}}(e.data,d.alg)&&(e1(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:eJ.invalid_string,message:d.message}),l.dirty()):"cidr"===d.kind?(i=e.data,!(("v4"===(n=d.version)||!n)&&tm.test(i)||("v6"===n||!n)&&tf.test(i))&&1&&(e1(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:eJ.invalid_string,message:d.message}),l.dirty())):"base64"===d.kind?ty.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"base64",code:eJ.invalid_string,message:d.message}),l.dirty()):"base64url"===d.kind?tg.test(e.data)||(e1(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:eJ.invalid_string,message:d.message}),l.dirty()):s.assertNever(d);return{status:l.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:eJ.invalid_string,...n.errToObj(r)})}_addCheck(e){return new tx({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...n.errToObj(e)})}url(e){return this._addCheck({kind:"url",...n.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...n.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...n.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...n.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...n.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...n.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...n.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...n.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...n.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...n.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...n.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...n.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...n.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...n.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...n.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...n.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...n.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...n.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...n.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...n.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...n.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...n.errToObj(t)})}nonempty(e){return this.min(1,n.errToObj(e))}trim(){return new tx({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new tx({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new tx({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}tx.create=e=>new tx({checks:[],typeName:o.ZodString,coerce:e?.coerce??!1,...tr(e)});class tw extends ta{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==eH.number){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.number,received:t.parsedType}),e4}let r=new e2;for(let a of this._def.checks)"int"===a.kind?s.isInteger(e.data)||(e1(t=this._getOrReturnCtx(e,t),{code:eJ.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty()):"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"multipleOf"===a.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,s=r>a?r:a;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}(e.data,a.value)&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(e1(t=this._getOrReturnCtx(e,t),{code:eJ.not_finite,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,a){return new tw({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(a)}]})}_addCheck(e){return new tw({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:n.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:n.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:n.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:n.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}tw.create=e=>new tw({checks:[],typeName:o.ZodNumber,coerce:e?.coerce||!1,...tr(e)});class tk extends ta{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==eH.bigint)return this._getInvalidInput(e);let r=new e2;for(let a of this._def.checks)"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.bigint,received:t.parsedType}),e4}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,a){return new tk({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(a)}]})}_addCheck(e){return new tk({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}tk.create=e=>new tk({checks:[],typeName:o.ZodBigInt,coerce:e?.coerce??!1,...tr(e)});class tj extends ta{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==eH.boolean){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.boolean,received:t.parsedType}),e4}return e9(e.data)}}tj.create=e=>new tj({typeName:o.ZodBoolean,coerce:e?.coerce||!1,...tr(e)});class tN extends ta{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==eH.date){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.date,received:t.parsedType}),e4}if(Number.isNaN(e.data.getTime()))return e1(this._getOrReturnCtx(e),{code:eJ.invalid_date}),e4;let r=new e2;for(let a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),r.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(e1(t=this._getOrReturnCtx(e,t),{code:eJ.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),r.dirty()):s.assertNever(a);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new tN({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:n.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:n.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}tN.create=e=>new tN({checks:[],coerce:e?.coerce||!1,typeName:o.ZodDate,...tr(e)});class tA extends ta{_parse(e){if(this._getType(e)!==eH.symbol){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.symbol,received:t.parsedType}),e4}return e9(e.data)}}tA.create=e=>new tA({typeName:o.ZodSymbol,...tr(e)});class tT extends ta{_parse(e){if(this._getType(e)!==eH.undefined){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.undefined,received:t.parsedType}),e4}return e9(e.data)}}tT.create=e=>new tT({typeName:o.ZodUndefined,...tr(e)});class tS extends ta{_parse(e){if(this._getType(e)!==eH.null){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.null,received:t.parsedType}),e4}return e9(e.data)}}tS.create=e=>new tS({typeName:o.ZodNull,...tr(e)});class tO extends ta{constructor(){super(...arguments),this._any=!0}_parse(e){return e9(e.data)}}tO.create=e=>new tO({typeName:o.ZodAny,...tr(e)});class tC extends ta{constructor(){super(...arguments),this._unknown=!0}_parse(e){return e9(e.data)}}tC.create=e=>new tC({typeName:o.ZodUnknown,...tr(e)});class tE extends ta{_parse(e){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.never,received:t.parsedType}),e4}}tE.create=e=>new tE({typeName:o.ZodNever,...tr(e)});class tP extends ta{_parse(e){if(this._getType(e)!==eH.undefined){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.void,received:t.parsedType}),e4}return e9(e.data)}}tP.create=e=>new tP({typeName:o.ZodVoid,...tr(e)});class tZ extends ta{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),a=this._def;if(t.parsedType!==eH.array)return e1(t,{code:eJ.invalid_type,expected:eH.array,received:t.parsedType}),e4;if(null!==a.exactLength){let e=t.data.length>a.exactLength.value,s=t.data.length<a.exactLength.value;(e||s)&&(e1(t,{code:e?eJ.too_big:eJ.too_small,minimum:s?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&(e1(t,{code:eJ.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&(e1(t,{code:eJ.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>a.type._parseAsync(new te(t,e,t.path,r)))).then(e=>e2.mergeArray(r,e));let s=[...t.data].map((e,r)=>a.type._parseSync(new te(t,e,t.path,r)));return e2.mergeArray(r,s)}get element(){return this._def.type}min(e,t){return new tZ({...this._def,minLength:{value:e,message:n.toString(t)}})}max(e,t){return new tZ({...this._def,maxLength:{value:e,message:n.toString(t)}})}length(e,t){return new tZ({...this._def,exactLength:{value:e,message:n.toString(t)}})}nonempty(e){return this.min(1,e)}}tZ.create=(e,t)=>new tZ({type:e,minLength:null,maxLength:null,exactLength:null,typeName:o.ZodArray,...tr(t)});class tF extends ta{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==eH.object){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.object,received:t.parsedType}),e4}let{status:t,ctx:r}=this._processInputParams(e),{shape:a,keys:s}=this._getCached(),i=[];if(!(this._def.catchall instanceof tE&&"strip"===this._def.unknownKeys))for(let e in r.data)s.includes(e)||i.push(e);let n=[];for(let e of s){let t=a[e],s=r.data[e];n.push({key:{status:"valid",value:e},value:t._parse(new te(r,s,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof tE){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)n.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)i.length>0&&(e1(r,{code:eJ.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let a=r.data[t];n.push({key:{status:"valid",value:t},value:e._parse(new te(r,a,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of n){let r=await t.key,a=await t.value;e.push({key:r,value:a,alwaysSet:t.alwaysSet})}return e}).then(e=>e2.mergeObjectSync(t,e)):e2.mergeObjectSync(t,n)}get shape(){return this._def.shape()}strict(e){return n.errToObj,new tF({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let a=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:n.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new tF({...this._def,unknownKeys:"strip"})}passthrough(){return new tF({...this._def,unknownKeys:"passthrough"})}extend(e){return new tF({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new tF({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:o.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new tF({...this._def,catchall:e})}pick(e){let t={};for(let r of s.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new tF({...this._def,shape:()=>t})}omit(e){let t={};for(let r of s.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new tF({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof tF){let r={};for(let a in t.shape){let s=t.shape[a];r[a]=tJ.create(e(s))}return new tF({...t._def,shape:()=>r})}if(t instanceof tZ)return new tZ({...t._def,type:e(t.element)});if(t instanceof tJ)return tJ.create(e(t.unwrap()));if(t instanceof tX)return tX.create(e(t.unwrap()));if(t instanceof tM)return tM.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of s.objectKeys(this.shape)){let a=this.shape[r];e&&!e[r]?t[r]=a:t[r]=a.optional()}return new tF({...this._def,shape:()=>t})}required(e){let t={};for(let r of s.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof tJ;)e=e._def.innerType;t[r]=e}return new tF({...this._def,shape:()=>t})}keyof(){return tW(s.objectKeys(this.shape))}}tF.create=(e,t)=>new tF({shape:()=>e,unknownKeys:"strip",catchall:tE.create(),typeName:o.ZodObject,...tr(t)}),tF.strictCreate=(e,t)=>new tF({shape:()=>e,unknownKeys:"strict",catchall:tE.create(),typeName:o.ZodObject,...tr(t)}),tF.lazycreate=(e,t)=>new tF({shape:e,unknownKeys:"strip",catchall:tE.create(),typeName:o.ZodObject,...tr(t)});class tV extends ta{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new eX(e.ctx.common.issues));return e1(t,{code:eJ.invalid_union,unionErrors:r}),e4});{let e,a=[];for(let s of r){let r={...t,common:{...t.common,issues:[]},parent:null},i=s._parseSync({data:t.data,path:t.path,parent:r});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:r}),r.common.issues.length&&a.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let s=a.map(e=>new eX(e));return e1(t,{code:eJ.invalid_union,unionErrors:s}),e4}}get options(){return this._def.options}}tV.create=(e,t)=>new tV({options:e,typeName:o.ZodUnion,...tr(t)});let tI=e=>{if(e instanceof tB)return tI(e.schema);if(e instanceof tY)return tI(e.innerType());if(e instanceof tq)return[e.value];if(e instanceof tK)return e.options;if(e instanceof tG)return s.objectValues(e.enum);else if(e instanceof tQ)return tI(e._def.innerType);else if(e instanceof tT)return[void 0];else if(e instanceof tS)return[null];else if(e instanceof tJ)return[void 0,...tI(e.unwrap())];else if(e instanceof tX)return[null,...tI(e.unwrap())];else if(e instanceof t2)return tI(e.unwrap());else if(e instanceof t5)return tI(e.unwrap());else if(e instanceof t0)return tI(e._def.innerType);else return[]};class tD extends ta{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==eH.object)return e1(t,{code:eJ.invalid_type,expected:eH.object,received:t.parsedType}),e4;let r=this.discriminator,a=t.data[r],s=this.optionsMap.get(a);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(e1(t,{code:eJ.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),e4)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let a=new Map;for(let r of t){let t=tI(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let s of t){if(a.has(s))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);a.set(s,r)}}return new tD({typeName:o.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...tr(r)})}}class tR extends ta{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=(e,a)=>{if(e3(e)||e3(a))return e4;let i=function e(t,r){let a=eY(t),i=eY(r);if(t===r)return{valid:!0,data:t};if(a===eH.object&&i===eH.object){let a=s.objectKeys(r),i=s.objectKeys(t).filter(e=>-1!==a.indexOf(e)),n={...t,...r};for(let a of i){let s=e(t[a],r[a]);if(!s.valid)return{valid:!1};n[a]=s.data}return{valid:!0,data:n}}if(a===eH.array&&i===eH.array){if(t.length!==r.length)return{valid:!1};let a=[];for(let s=0;s<t.length;s++){let i=e(t[s],r[s]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}if(a===eH.date&&i===eH.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,a.value);return i.valid?((e6(e)||e6(a))&&t.dirty(),{status:t.value,value:i.data}):(e1(r,{code:eJ.invalid_intersection_types}),e4)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>a(e,t)):a(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}tR.create=(e,t,r)=>new tR({left:e,right:t,typeName:o.ZodIntersection,...tr(r)});class tM extends ta{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eH.array)return e1(r,{code:eJ.invalid_type,expected:eH.array,received:r.parsedType}),e4;if(r.data.length<this._def.items.length)return e1(r,{code:eJ.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),e4;!this._def.rest&&r.data.length>this._def.items.length&&(e1(r,{code:eJ.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let a=[...r.data].map((e,t)=>{let a=this._def.items[t]||this._def.rest;return a?a._parse(new te(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(a).then(e=>e2.mergeArray(t,e)):e2.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new tM({...this._def,rest:e})}}tM.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new tM({items:e,typeName:o.ZodTuple,rest:null,...tr(t)})};class t$ extends ta{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eH.object)return e1(r,{code:eJ.invalid_type,expected:eH.object,received:r.parsedType}),e4;let a=[],s=this._def.keyType,i=this._def.valueType;for(let e in r.data)a.push({key:s._parse(new te(r,e,r.path,e)),value:i._parse(new te(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?e2.mergeObjectAsync(t,a):e2.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,r){return new t$(t instanceof ta?{keyType:e,valueType:t,typeName:o.ZodRecord,...tr(r)}:{keyType:tx.create(),valueType:e,typeName:o.ZodRecord,...tr(t)})}}class tL extends ta{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eH.map)return e1(r,{code:eJ.invalid_type,expected:eH.map,received:r.parsedType}),e4;let a=this._def.keyType,s=this._def.valueType,i=[...r.data.entries()].map(([e,t],i)=>({key:a._parse(new te(r,e,r.path,[i,"key"])),value:s._parse(new te(r,t,r.path,[i,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of i){let a=await r.key,s=await r.value;if("aborted"===a.status||"aborted"===s.status)return e4;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of i){let a=r.key,s=r.value;if("aborted"===a.status||"aborted"===s.status)return e4;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}}}}tL.create=(e,t,r)=>new tL({valueType:t,keyType:e,typeName:o.ZodMap,...tr(r)});class tz extends ta{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eH.set)return e1(r,{code:eJ.invalid_type,expected:eH.set,received:r.parsedType}),e4;let a=this._def;null!==a.minSize&&r.data.size<a.minSize.value&&(e1(r,{code:eJ.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&r.data.size>a.maxSize.value&&(e1(r,{code:eJ.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());let s=this._def.valueType;function i(e){let r=new Set;for(let a of e){if("aborted"===a.status)return e4;"dirty"===a.status&&t.dirty(),r.add(a.value)}return{status:t.value,value:r}}let n=[...r.data.values()].map((e,t)=>s._parse(new te(r,e,r.path,t)));return r.common.async?Promise.all(n).then(e=>i(e)):i(n)}min(e,t){return new tz({...this._def,minSize:{value:e,message:n.toString(t)}})}max(e,t){return new tz({...this._def,maxSize:{value:e,message:n.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}tz.create=(e,t)=>new tz({valueType:e,minSize:null,maxSize:null,typeName:o.ZodSet,...tr(t)});class tU extends ta{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==eH.function)return e1(t,{code:eJ.invalid_type,expected:eH.function,received:t.parsedType}),e4;function r(e,r){return e0({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,eQ,eQ].filter(e=>!!e),issueData:{code:eJ.invalid_arguments,argumentsError:r}})}function a(e,r){return e0({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,eQ,eQ].filter(e=>!!e),issueData:{code:eJ.invalid_return_type,returnTypeError:r}})}let s={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof tH){let e=this;return e9(async function(...t){let n=new eX([]),o=await e._def.args.parseAsync(t,s).catch(e=>{throw n.addIssue(r(t,e)),n}),l=await Reflect.apply(i,this,o);return await e._def.returns._def.type.parseAsync(l,s).catch(e=>{throw n.addIssue(a(l,e)),n})})}{let e=this;return e9(function(...t){let n=e._def.args.safeParse(t,s);if(!n.success)throw new eX([r(t,n.error)]);let o=Reflect.apply(i,this,n.data),l=e._def.returns.safeParse(o,s);if(!l.success)throw new eX([a(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new tU({...this._def,args:tM.create(e).rest(tC.create())})}returns(e){return new tU({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new tU({args:e||tM.create([]).rest(tC.create()),returns:t||tC.create(),typeName:o.ZodFunction,...tr(r)})}}class tB extends ta{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}tB.create=(e,t)=>new tB({getter:e,typeName:o.ZodLazy,...tr(t)});class tq extends ta{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return e1(t,{received:t.data,code:eJ.invalid_literal,expected:this._def.value}),e4}return{status:"valid",value:e.data}}get value(){return this._def.value}}function tW(e,t){return new tK({values:e,typeName:o.ZodEnum,...tr(t)})}tq.create=(e,t)=>new tq({value:e,typeName:o.ZodLiteral,...tr(t)});class tK extends ta{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return e1(t,{expected:s.joinValues(r),received:t.parsedType,code:eJ.invalid_type}),e4}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return e1(t,{received:t.data,code:eJ.invalid_enum_value,options:r}),e4}return e9(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return tK.create(e,{...this._def,...t})}exclude(e,t=this._def){return tK.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}tK.create=tW;class tG extends ta{_parse(e){let t=s.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==eH.string&&r.parsedType!==eH.number){let e=s.objectValues(t);return e1(r,{expected:s.joinValues(e),received:r.parsedType,code:eJ.invalid_type}),e4}if(this._cache||(this._cache=new Set(s.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=s.objectValues(t);return e1(r,{received:r.data,code:eJ.invalid_enum_value,options:e}),e4}return e9(e.data)}get enum(){return this._def.values}}tG.create=(e,t)=>new tG({values:e,typeName:o.ZodNativeEnum,...tr(t)});class tH extends ta{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==eH.promise&&!1===t.common.async?(e1(t,{code:eJ.invalid_type,expected:eH.promise,received:t.parsedType}),e4):e9((t.parsedType===eH.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}tH.create=(e,t)=>new tH({type:e,typeName:o.ZodPromise,...tr(t)});class tY extends ta{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===o.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=this._def.effect||null,i={addIssue:e=>{e1(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===a.type){let e=a.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return e4;let a=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===a.status?e4:"dirty"===a.status||"dirty"===t.value?e5(a.value):a});{if("aborted"===t.value)return e4;let a=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===a.status?e4:"dirty"===a.status||"dirty"===t.value?e5(a.value):a}}if("refinement"===a.type){let e=e=>{let t=a.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?e4:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===a.status?e4:("dirty"===a.status&&t.dirty(),e(a.value),{status:t.value,value:a.value})}}if("transform"===a.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>e7(e)?Promise.resolve(a.transform(e.value,i)).then(e=>({status:t.value,value:e})):e4);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!e7(e))return e4;let s=a.transform(e.value,i);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}s.assertNever(a)}}tY.create=(e,t,r)=>new tY({schema:e,typeName:o.ZodEffects,effect:t,...tr(r)}),tY.createWithPreprocess=(e,t,r)=>new tY({schema:t,effect:{type:"preprocess",transform:e},typeName:o.ZodEffects,...tr(r)});class tJ extends ta{_parse(e){return this._getType(e)===eH.undefined?e9(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}tJ.create=(e,t)=>new tJ({innerType:e,typeName:o.ZodOptional,...tr(t)});class tX extends ta{_parse(e){return this._getType(e)===eH.null?e9(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}tX.create=(e,t)=>new tX({innerType:e,typeName:o.ZodNullable,...tr(t)});class tQ extends ta{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===eH.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}tQ.create=(e,t)=>new tQ({innerType:e,typeName:o.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...tr(t)});class t0 extends ta{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return e8(a)?a.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new eX(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new eX(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}t0.create=(e,t)=>new t0({innerType:e,typeName:o.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...tr(t)});class t1 extends ta{_parse(e){if(this._getType(e)!==eH.nan){let t=this._getOrReturnCtx(e);return e1(t,{code:eJ.invalid_type,expected:eH.nan,received:t.parsedType}),e4}return{status:"valid",value:e.data}}}t1.create=e=>new t1({typeName:o.ZodNaN,...tr(e)}),Symbol("zod_brand");class t2 extends ta{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class t4 extends ta{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?e4:"dirty"===e.status?(t.dirty(),e5(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?e4:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new t4({in:e,out:t,typeName:o.ZodPipeline})}}class t5 extends ta{_parse(e){let t=this._def.innerType._parse(e),r=e=>(e7(e)&&(e.value=Object.freeze(e.value)),e);return e8(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}t5.create=(e,t)=>new t5({innerType:e,typeName:o.ZodReadonly,...tr(t)}),tF.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(o||(o={}));let t9=tx.create;tw.create,t1.create,tk.create;let t3=tj.create;tN.create,tA.create,tT.create,tS.create,tO.create,tC.create,tE.create,tP.create,tZ.create;let t6=tF.create;tF.strictCreate,tV.create,tD.create,tR.create,tM.create,t$.create,tL.create,tz.create,tU.create,tB.create,tq.create,tK.create,tG.create,tH.create,tY.create,tJ.create,tX.create,tY.createWithPreprocess,t4.create;var t7=r(64859),t8=r(137),re=r(36942),rt=r(58089),rr=r(59168),ra=r(71843),rs=r(94101);let ri=t6({firstName:t9().min(2,"First name must be at least 2 characters"),lastName:t9().min(2,"Last name must be at least 2 characters"),email:t9().email("Please enter a valid email address"),phone:t9().min(10,"Please enter a valid phone number").optional(),company:t9().optional(),projectType:t9().min(1,"Please select a project type"),budget:t9().min(1,"Please select a budget range"),timeline:t9().min(1,"Please select a timeline"),message:t9().min(10,"Message must be at least 10 characters"),agreeToTerms:t3().refine(e=>!0===e,"You must agree to the terms")}),rn=["Web Development","Mobile App Development","E-commerce Platform","Custom Software","API Development","Cloud Solutions","UI/UX Design","Consulting","Other"],ro=["Under $10,000","$10,000 - $25,000","$25,000 - $50,000","$50,000 - $100,000","Over $100,000","Not sure yet"],rl=["ASAP","1-2 months","3-6 months","6-12 months","More than 1 year","Flexible"];function rd(){let[e,t]=(0,d.useState)(!1),[r,a]=(0,d.useState)("idle"),{register:s,handleSubmit:i,formState:{errors:n},reset:o}=function(e={}){let t=d.useRef(void 0),r=d.useRef(void 0),[a,s]=d.useState({isDirty:!1,isValidating:!1,isLoading:B(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:B(e.defaultValues)?void 0:e.defaultValues});!t.current&&(t.current={...e.formControl?e.formControl:function(e={}){let t,r={...eA,...e},a={submitCount:0,isDirty:!1,isReady:!1,isLoading:B(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},s={},i=(f(r.defaultValues)||f(r.values))&&x(r.defaultValues||r.values)||{},n=r.shouldUnregister?{}:x(i),o={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},d=0,u={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},p={...u},g={array:M(),state:M()},_=r.criteriaMode===C.all,A=e=>t=>{clearTimeout(d),d=setTimeout(e,t)},T=async e=>{if(!r.disabled&&(u.isValid||p.isValid||e)){let e=r.resolver?z((await $()).errors):await J(s,!0);e!==a.isValid&&g.state.next({isValid:e})}},E=(e,t)=>{!r.disabled&&(u.isValidating||u.validatingFields||p.isValidating||p.validatingFields)&&((e||Array.from(l.mount)).forEach(e=>{e&&(t?S(a.validatingFields,e,t):Y(a.validatingFields,e))}),g.state.next({validatingFields:a.validatingFields,isValidating:!z(a.validatingFields)}))},P=(e,t)=>{S(a.errors,e,t),g.state.next({errors:a.errors})},Z=(e,t,r,a)=>{let l=j(s,e);if(l){let s=j(n,e,k(r)?j(i,e):r);k(s)||a&&a.defaultChecked||t?S(n,e,t?s:en(l._f)):et(e,s),o.mount&&T()}},F=(e,t,s,n,o)=>{let l=!1,d=!1,c={name:e};if(!r.disabled){if(!s||n){(u.isDirty||p.isDirty)&&(d=a.isDirty,a.isDirty=c.isDirty=X(),l=d!==c.isDirty);let r=L(j(i,e),t);d=!!j(a.dirtyFields,e),r?Y(a.dirtyFields,e):S(a.dirtyFields,e,!0),c.dirtyFields=a.dirtyFields,l=l||(u.dirtyFields||p.dirtyFields)&&!r!==d}if(s){let t=j(a.touchedFields,e);t||(S(a.touchedFields,e,s),c.touchedFields=a.touchedFields,l=l||(u.touchedFields||p.touchedFields)&&t!==s)}l&&o&&g.state.next(c)}return l?c:{}},D=(e,s,i,n)=>{let o=j(a.errors,e),l=(u.isValid||p.isValid)&&N(s)&&a.isValid!==s;if(r.delayError&&i?(t=A(()=>P(e,i)))(r.delayError):(clearTimeout(d),t=null,i?S(a.errors,e,i):Y(a.errors,e)),(i?!L(o,i):o)||!z(n)||l){let t={...n,...l&&N(s)?{isValid:s}:{},errors:a.errors,name:e};a={...a,...t},g.state.next(t)}},$=async e=>{E(e,!0);let t=await r.resolver(n,r.context,eo(e||l.mount,s,r.criteriaMode,r.shouldUseNativeValidation));return E(e),t},K=async e=>{let{errors:t}=await $(e);if(e)for(let r of e){let e=j(t,r);e?S(a.errors,r,e):Y(a.errors,r)}else a.errors=t;return t},J=async(e,t,s={valid:!0})=>{for(let i in e){let o=e[i];if(o){let{_f:e,...d}=o;if(e){let d=l.array.has(e.name),c=o._f&&eh(o._f);c&&u.validatingFields&&E([i],!0);let h=await eN(o,l.disabled,n,_,r.shouldUseNativeValidation&&!t,d);if(c&&u.validatingFields&&E([i]),h[e.name]&&(s.valid=!1,t))break;t||(j(h,e.name)?d?ex(a.errors,h,e.name):S(a.errors,e.name,h[e.name]):Y(a.errors,e.name))}z(d)||await J(d,t,s)}}return s.valid},X=(e,t)=>!r.disabled&&(e&&t&&S(n,e,t),!L(ew(),i)),ee=(e,t,r)=>I(e,l,{...o.mount?n:k(t)?i:V(e)?{[e]:t}:t},r,t),et=(e,t,r={})=>{let a=j(s,e),i=t;if(a){let r=a._f;r&&(r.disabled||S(n,e,ea(t,r)),i=q(r.ref)&&m(t)?"":t,W(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?c(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):r.refs.forEach(e=>e.checked=e.value===i):U(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||g.state.next({name:e,values:x(n)})))}(r.shouldDirty||r.shouldTouch)&&F(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&ec(e)},er=(e,t,r)=>{for(let a in t){if(!t.hasOwnProperty(a))return;let i=t[a],n=e+"."+a,o=j(s,n);(l.array.has(e)||f(i)||o&&!o._f)&&!h(i)?er(n,i,r):et(n,i,r)}},es=(e,t,r={})=>{let d=j(s,e),c=l.array.has(e),h=x(t);S(n,e,h),c?(g.array.next({name:e,values:x(n)}),(u.isDirty||u.dirtyFields||p.isDirty||p.dirtyFields)&&r.shouldDirty&&g.state.next({name:e,dirtyFields:Q(i,n),isDirty:X(e,h)})):!d||d._f||m(h)?et(e,h,r):er(e,h,r),ep(e,l)&&g.state.next({...a}),g.state.next({name:o.mount?e:void 0,values:x(n)})},ei=async e=>{o.mount=!0;let i=e.target,d=i.name,c=!0,m=j(s,d),f=e=>{c=Number.isNaN(e)||h(e)&&isNaN(e.getTime())||L(e,j(n,d,e))},v=eu(r.mode),b=eu(r.reValidateMode);if(m){let o,h,w=i.type?en(m._f):y(e),k=e.type===O.BLUR||e.type===O.FOCUS_OUT,N=!em(m._f)&&!r.resolver&&!j(a.errors,d)&&!m._f.deps||e_(k,j(a.touchedFields,d),a.isSubmitted,b,v),A=ep(d,l,k);S(n,d,w),k?(m._f.onBlur&&m._f.onBlur(e),t&&t(0)):m._f.onChange&&m._f.onChange(e);let C=F(d,w,k),P=!z(C)||A;if(k||g.state.next({name:d,type:e.type,values:x(n)}),N)return(u.isValid||p.isValid)&&("onBlur"===r.mode?k&&T():k||T()),P&&g.state.next({name:d,...A?{}:C});if(!k&&A&&g.state.next({...a}),r.resolver){let{errors:e}=await $([d]);if(f(w),c){let t=ey(a.errors,s,d),r=ey(e,s,t.name||d);o=r.error,d=r.name,h=z(e)}}else E([d],!0),o=(await eN(m,l.disabled,n,_,r.shouldUseNativeValidation))[d],E([d]),f(w),c&&(o?h=!1:(u.isValid||p.isValid)&&(h=await J(s,!0)));c&&(m._f.deps&&ec(m._f.deps),D(d,h,o,C))}},el=(e,t)=>{if(j(a.errors,t)&&e.focus)return e.focus(),1},ec=async(e,t={})=>{let i,n,o=R(e);if(r.resolver){let t=await K(k(e)?e:o);i=z(t),n=e?!o.some(e=>j(t,e)):i}else e?((n=(await Promise.all(o.map(async e=>{let t=j(s,e);return await J(t&&t._f?{[e]:t}:t)}))).every(Boolean))||a.isValid)&&T():n=i=await J(s);return g.state.next({...!V(e)||(u.isValid||p.isValid)&&i!==a.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:a.errors}),t.shouldFocus&&!n&&ef(s,el,e?o:l.mount),n},ew=e=>{let t={...o.mount?n:i};return k(e)?t:V(e)?j(t,e):e.map(e=>j(t,e))},ek=(e,t)=>({invalid:!!j((t||a).errors,e),isDirty:!!j((t||a).dirtyFields,e),error:j((t||a).errors,e),isValidating:!!j(a.validatingFields,e),isTouched:!!j((t||a).touchedFields,e)}),ej=(e,t,r)=>{let i=(j(s,e,{_f:{}})._f||{}).ref,{ref:n,message:o,type:l,...d}=j(a.errors,e)||{};S(a.errors,e,{...d,...t,ref:i}),g.state.next({name:e,errors:a.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},eT=e=>g.state.subscribe({next:t=>{ev(e.name,t.name,e.exact)&&eg(t,e.formState||u,eV,e.reRenderRoot)&&e.callback({values:{...n},...a,...t})}}).unsubscribe,eS=(e,t={})=>{for(let o of e?R(e):l.mount)l.mount.delete(o),l.array.delete(o),t.keepValue||(Y(s,o),Y(n,o)),t.keepError||Y(a.errors,o),t.keepDirty||Y(a.dirtyFields,o),t.keepTouched||Y(a.touchedFields,o),t.keepIsValidating||Y(a.validatingFields,o),r.shouldUnregister||t.keepDefaultValue||Y(i,o);g.state.next({values:x(n)}),g.state.next({...a,...!t.keepDirty?{}:{isDirty:X()}}),t.keepIsValid||T()},eO=({disabled:e,name:t})=>{(N(e)&&o.mount||e||l.disabled.has(t))&&(e?l.disabled.add(t):l.disabled.delete(t))},eC=(e,t={})=>{let a=j(s,e),n=N(t.disabled)||N(r.disabled);return S(s,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...t}}),l.mount.add(e),a?eO({disabled:N(t.disabled)?t.disabled:r.disabled,name:e}):Z(e,!0,t.value),{...n?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ed(t.min),max:ed(t.max),minLength:ed(t.minLength),maxLength:ed(t.maxLength),pattern:ed(t.pattern)}:{},name:e,onChange:ei,onBlur:ei,ref:n=>{if(n){eC(e,t),a=j(s,e);let r=k(n.value)&&n.querySelectorAll&&n.querySelectorAll("input,select,textarea")[0]||n,o=G(r),l=a._f.refs||[];(o?l.find(e=>e===r):r===a._f.ref)||(S(s,e,{_f:{...a._f,...o?{refs:[...l.filter(H),r,...Array.isArray(j(i,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),Z(e,!1,void 0,r))}else(a=j(s,e,{}))._f&&(a._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(v(l.array,e)&&o.action)&&l.unMount.add(e)}}},eE=()=>r.shouldFocusError&&ef(s,el,l.mount),eP=(e,t)=>async i=>{let o;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let d=x(n);if(g.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await $();a.errors=e,d=t}else await J(s);if(l.disabled.size)for(let e of l.disabled)S(d,e,void 0);if(Y(a.errors,"root"),z(a.errors)){g.state.next({errors:{}});try{await e(d,i)}catch(e){o=e}}else t&&await t({...a.errors},i),eE(),setTimeout(eE);if(g.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:z(a.errors)&&!o,submitCount:a.submitCount+1,errors:a.errors}),o)throw o},eZ=(e,t={})=>{let d=e?x(e):i,c=x(d),h=z(e),m=h?i:c;if(t.keepDefaultValues||(i=d),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...l.mount,...Object.keys(Q(i,n))])))j(a.dirtyFields,e)?S(m,e,j(n,e)):es(e,j(m,e));else{if(b&&k(e))for(let e of l.mount){let t=j(s,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(q(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of l.mount)es(e,j(m,e))}n=x(m),g.array.next({values:{...m}}),g.state.next({values:{...m}})}l={mount:t.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!u.isValid||!!t.keepIsValid||!!t.keepDirtyValues,o.watch=!!r.shouldUnregister,g.state.next({submitCount:t.keepSubmitCount?a.submitCount:0,isDirty:!h&&(t.keepDirty?a.isDirty:!!(t.keepDefaultValues&&!L(e,i))),isSubmitted:!!t.keepIsSubmitted&&a.isSubmitted,dirtyFields:h?{}:t.keepDirtyValues?t.keepDefaultValues&&n?Q(i,n):a.dirtyFields:t.keepDefaultValues&&e?Q(i,e):t.keepDirty?a.dirtyFields:{},touchedFields:t.keepTouched?a.touchedFields:{},errors:t.keepErrors?a.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&a.isSubmitSuccessful,isSubmitting:!1})},eF=(e,t)=>eZ(B(e)?e(n):e,t),eV=e=>{a={...a,...e}},eI={control:{register:eC,unregister:eS,getFieldState:ek,handleSubmit:eP,setError:ej,_subscribe:eT,_runSchema:$,_focusError:eE,_getWatch:ee,_getDirty:X,_setValid:T,_setFieldArray:(e,t=[],l,d,c=!0,h=!0)=>{if(d&&l&&!r.disabled){if(o.action=!0,h&&Array.isArray(j(s,e))){let t=l(j(s,e),d.argA,d.argB);c&&S(s,e,t)}if(h&&Array.isArray(j(a.errors,e))){let t=l(j(a.errors,e),d.argA,d.argB);c&&S(a.errors,e,t),eb(a.errors,e)}if((u.touchedFields||p.touchedFields)&&h&&Array.isArray(j(a.touchedFields,e))){let t=l(j(a.touchedFields,e),d.argA,d.argB);c&&S(a.touchedFields,e,t)}(u.dirtyFields||p.dirtyFields)&&(a.dirtyFields=Q(i,n)),g.state.next({name:e,isDirty:X(e,t),dirtyFields:a.dirtyFields,errors:a.errors,isValid:a.isValid})}else S(n,e,t)},_setDisabledField:eO,_setErrors:e=>{a.errors=e,g.state.next({errors:a.errors,isValid:!1})},_getFieldArray:e=>w(j(o.mount?n:i,e,r.shouldUnregister?j(i,e,[]):[])),_reset:eZ,_resetDefaultValues:()=>B(r.defaultValues)&&r.defaultValues().then(e=>{eF(e,r.resetOptions),g.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of l.unMount){let t=j(s,e);t&&(t._f.refs?t._f.refs.every(e=>!H(e)):!H(t._f.ref))&&eS(e)}l.unMount=new Set},_disableForm:e=>{N(e)&&(g.state.next({disabled:e}),ef(s,(t,r)=>{let a=j(s,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach(t=>{t.disabled=a._f.disabled||e}))},0,!1))},_subjects:g,_proxyFormState:u,get _fields(){return s},get _formValues(){return n},get _state(){return o},set _state(value){o=value},get _defaultValues(){return i},get _names(){return l},set _names(value){l=value},get _formState(){return a},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(o.mount=!0,p={...p,...e.formState},eT({...e,formState:p})),trigger:ec,register:eC,handleSubmit:eP,watch:(e,t)=>B(e)?g.state.subscribe({next:r=>e(ee(void 0,t),r)}):ee(e,t,!0),setValue:es,getValues:ew,reset:eF,resetField:(e,t={})=>{j(s,e)&&(k(t.defaultValue)?es(e,x(j(i,e))):(es(e,t.defaultValue),S(i,e,x(t.defaultValue))),t.keepTouched||Y(a.touchedFields,e),t.keepDirty||(Y(a.dirtyFields,e),a.isDirty=t.defaultValue?X(e,x(j(i,e))):X()),!t.keepError&&(Y(a.errors,e),u.isValid&&T()),g.state.next({...a}))},clearErrors:e=>{e&&R(e).forEach(e=>Y(a.errors,e)),g.state.next({errors:e?a.errors:{}})},unregister:eS,setError:ej,setFocus:(e,t={})=>{let r=j(s,e),a=r&&r._f;if(a){let e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&B(e.select)&&e.select())}},getFieldState:ek};return{...eI,formControl:eI}}(e),formState:a},e.formControl&&e.defaultValues&&!B(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));let i=t.current.control;return i._options=e,F(()=>{let e=i._subscribe({formState:i._proxyFormState,callback:()=>s({...i._formState}),reRenderRoot:!0});return s(e=>({...e,isReady:!0})),i._formState.isReady=!0,e},[i]),d.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),d.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),d.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),d.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),d.useEffect(()=>{if(i._proxyFormState.isDirty){let e=i._getDirty();e!==a.isDirty&&i._subjects.state.next({isDirty:e})}},[i,a.isDirty]),d.useEffect(()=>{e.values&&!L(e.values,r.current)?(i._reset(e.values,i._options.resetOptions),r.current=e.values,s(e=>({...e}))):i._resetDefaultValues()},[i,e.values]),d.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=Z(a,i),t.current}({resolver:function(e,t,r){if(void 0===r&&(r={}),"_def"in e&&"object"==typeof e._def&&"typeName"in e._def)return function(t,a,s){try{return Promise.resolve(eG(function(){return Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](t,void 0)).then(function(e){return s.shouldUseNativeValidation&&eS({},s),{errors:{},values:r.raw?Object.assign({},t):e}})},function(e){if(Array.isArray(null==e?void 0:e.issues))return{values:{},errors:eO(function(e,t){for(var r={};e.length;){var a=e[0],s=a.code,i=a.message,n=a.path.join(".");if(!r[n])if("unionErrors"in a){var o=a.unionErrors[0].errors[0];r[n]={message:o.message,type:o.code}}else r[n]={message:i,type:s};if("unionErrors"in a&&a.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var l=r[n].types,d=l&&l[a.code];r[n]=D(n,t,r,s,d?[].concat(d,a.message):a.message)}e.shift()}return r}(e.errors,!s.shouldUseNativeValidation&&"all"===s.criteriaMode),s)};throw e}))}catch(e){return Promise.reject(e)}};if("_zod"in e&&"object"==typeof e._zod)return function(t,a,s){try{return Promise.resolve(eG(function(){return Promise.resolve(("sync"===r.mode?ez:eU)(e,t,void 0)).then(function(e){return s.shouldUseNativeValidation&&eS({},s),{errors:{},values:r.raw?Object.assign({},t):e}})},function(e){if(e instanceof e$)return{values:{},errors:eO(function(e,t){for(var r={};e.length;){var a=e[0],s=a.code,i=a.message,n=a.path.join(".");if(!r[n])if("invalid_union"===a.code){var o=a.errors[0][0];r[n]={message:o.message,type:o.code}}else r[n]={message:i,type:s};if("invalid_union"===a.code&&a.errors.forEach(function(t){return t.forEach(function(t){return e.push(t)})}),t){var l=r[n].types,d=l&&l[a.code];r[n]=D(n,t,r,s,d?[].concat(d,a.message):a.message)}e.shift()}return r}(e.issues,!s.shouldUseNativeValidation&&"all"===s.criteriaMode),s)};throw e}))}catch(e){return Promise.reject(e)}};throw Error("Invalid input: not a Zod schema")}(ri)}),p=async e=>{t(!0),a("idle");try{await new Promise(e=>setTimeout(e,2e3)),console.log("Form data:",e),a("success"),o()}catch(e){a("error")}finally{t(!1)}};return(0,l.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,l.jsx)(ra.Y,{}),(0,l.jsxs)("main",{className:"pt-20",children:[(0,l.jsx)("section",{className:"relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)(u.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center max-w-4xl mx-auto",children:[(0,l.jsxs)(u.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl",children:["Let's Build Something ",(0,l.jsx)("span",{className:"gradient-text",children:"Amazing"})]}),(0,l.jsx)(u.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl",children:"Ready to transform your ideas into reality? Get in touch with our team and let's discuss how we can help bring your vision to life."})]})})}),(0,l.jsx)("section",{className:"py-16 bg-white",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:[{icon:t7.A,title:"Email Us",content:"<EMAIL>",subContent:"We respond within 24 hours"},{icon:t8.A,title:"Call Us",content:"+****************",subContent:"Mon-Fri 9AM-6PM EST"},{icon:re.A,title:"Visit Us",content:"123 Tech Street, San Francisco, CA 94105",subContent:"By appointment only"}].map((e,t)=>(0,l.jsxs)(u.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"text-center",children:[(0,l.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mx-auto mb-4",children:(0,l.jsx)(e.icon,{className:"w-8 h-8 text-blue-600"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,l.jsx)("p",{className:"text-gray-700 font-medium",children:e.content}),(0,l.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.subContent})]},e.title))})})}),(0,l.jsx)("section",{className:"py-16 bg-gray-50",children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsxs)(u.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Start Your Project"}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"Fill out the form below and we'll get back to you within 24 hours with a detailed proposal."})]}),(0,l.jsxs)(u.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"bg-white rounded-2xl shadow-lg p-8",children:["success"===r&&(0,l.jsxs)(u.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"mb-8 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center",children:[(0,l.jsx)(rt.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Message sent successfully!"}),(0,l.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"Thank you for reaching out. We'll get back to you within 24 hours."})]})]}),"error"===r&&(0,l.jsxs)(u.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"mb-8 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center",children:[(0,l.jsx)(rr.A,{className:"w-5 h-5 text-red-500 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error sending message"}),(0,l.jsx)("p",{className:"text-sm text-red-700 mt-1",children:"Please try again or contact us <NAME_EMAIL>"})]})]}),(0,l.jsxs)("form",{onSubmit:i(p),className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),(0,l.jsx)("input",{...s("firstName"),type:"text",id:"firstName",className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.firstName?"border-red-300":"border-gray-300"}`,placeholder:"John"}),n.firstName&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.firstName.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),(0,l.jsx)("input",{...s("lastName"),type:"text",id:"lastName",className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.lastName?"border-red-300":"border-gray-300"}`,placeholder:"Doe"}),n.lastName&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.lastName.message})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,l.jsx)("input",{...s("email"),type:"email",id:"email",className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.email?"border-red-300":"border-gray-300"}`,placeholder:"<EMAIL>"}),n.email&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.email.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,l.jsx)("input",{...s("phone"),type:"tel",id:"phone",className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.phone?"border-red-300":"border-gray-300"}`,placeholder:"+****************"}),n.phone&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.phone.message})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Name"}),(0,l.jsx)("input",{...s("company"),type:"text",id:"company",className:"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Your Company"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"projectType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Project Type *"}),(0,l.jsxs)("select",{...s("projectType"),id:"projectType",className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.projectType?"border-red-300":"border-gray-300"}`,children:[(0,l.jsx)("option",{value:"",children:"Select type"}),rn.map(e=>(0,l.jsx)("option",{value:e,children:e},e))]}),n.projectType&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.projectType.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"budget",className:"block text-sm font-medium text-gray-700 mb-2",children:"Budget Range *"}),(0,l.jsxs)("select",{...s("budget"),id:"budget",className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.budget?"border-red-300":"border-gray-300"}`,children:[(0,l.jsx)("option",{value:"",children:"Select budget"}),ro.map(e=>(0,l.jsx)("option",{value:e,children:e},e))]}),n.budget&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.budget.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"timeline",className:"block text-sm font-medium text-gray-700 mb-2",children:"Timeline *"}),(0,l.jsxs)("select",{...s("timeline"),id:"timeline",className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.timeline?"border-red-300":"border-gray-300"}`,children:[(0,l.jsx)("option",{value:"",children:"Select timeline"}),rl.map(e=>(0,l.jsx)("option",{value:e,children:e},e))]}),n.timeline&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.timeline.message})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Project Description *"}),(0,l.jsx)("textarea",{...s("message"),id:"message",rows:6,className:`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${n.message?"border-red-300":"border-gray-300"}`,placeholder:"Tell us about your project, goals, and any specific requirements..."}),n.message&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.message.message})]}),(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)("div",{className:"flex items-center h-5",children:(0,l.jsx)("input",{...s("agreeToTerms"),id:"agreeToTerms",type:"checkbox",className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"})}),(0,l.jsxs)("div",{className:"ml-3 text-sm",children:[(0,l.jsxs)("label",{htmlFor:"agreeToTerms",className:"text-gray-700",children:["I agree to the"," ",(0,l.jsx)("a",{href:"/legal/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,l.jsx)("a",{href:"/legal/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"}),"*"]}),n.agreeToTerms&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n.agreeToTerms.message})]})]}),(0,l.jsx)("div",{children:(0,l.jsx)("button",{type:"submit",disabled:e,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:e?(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Sending Message..."]}):"Send Message"})})]})]})]})})}),(0,l.jsx)("section",{className:"py-16 bg-white",children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)(u.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Frequently Asked Questions"}),(0,l.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Quick answers to common questions about our services and process."})]}),(0,l.jsx)("div",{className:"max-w-3xl mx-auto space-y-6",children:[{question:"How long does a typical project take?",answer:"Project timelines vary based on complexity and scope. Simple websites typically take 4-6 weeks, while complex applications can take 3-6 months. We provide detailed timelines during our initial consultation."},{question:"What is your development process?",answer:"We follow an agile development methodology with regular check-ins and updates. Our process includes discovery, planning, design, development, testing, and deployment phases with continuous client collaboration."},{question:"Do you provide ongoing support and maintenance?",answer:"Yes, we offer comprehensive support and maintenance packages to ensure your application stays secure, updated, and performing optimally. We provide different tiers of support based on your needs."},{question:"Can you work with our existing team?",answer:"Absolutely! We can integrate with your existing development team, provide consulting services, or work as an extension of your team. We adapt our approach to fit your organizational needs."}].map((e,t)=>(0,l.jsxs)(u.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-gray-50 rounded-lg p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.question}),(0,l.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.answer})]},t))})]})})]}),(0,l.jsx)(rs.w,{})]})}},33873:e=>{"use strict";e.exports=require("path")},36942:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},43839:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Project By AI\\\\Technoloway\\\\Technoloway (Processing)\\\\technoloway-simple\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\contact\\page.tsx","default")},44318:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>h,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43839)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\technoloway-simple\\src\\app\\contact\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},58089:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},59168:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64859:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},79551:e=>{"use strict";e.exports=require("url")},81641:(e,t,r)=>{Promise.resolve().then(r.bind(r,29658))},91369:(e,t,r)=>{Promise.resolve().then(r.bind(r,43839))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,9945,6818,5816],()=>r(44318));module.exports=a})();