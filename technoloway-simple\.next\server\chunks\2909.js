"use strict";exports.id=2909,exports.ids=[2909],exports.modules={12909:(e,s,r)=>{r.d(s,{authOptions:()=>c});var a=r(13581),i=r(36344),l=r(65752),n=r(60890),t=r(31183),o=r(85665),p=r.n(o);let c={adapter:(0,n.y)(t.z),providers:[(0,a.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let s=await t.z.user.findUnique({where:{email:e.email}});return s&&s.password&&await p().compare(e.password,s.password)?{id:s.id,email:s.email,name:`${s.firstName} ${s.lastName}`,role:s.role,image:s.imageUrl}:null}}),...process.env.GOOGLE_CLIENT_ID&&process.env.GOOGLE_CLIENT_SECRET?[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,profile:e=>({id:e.sub,name:e.name,email:e.email,image:e.picture,role:"USER"})})]:[],...process.env.GITHUB_ID&&process.env.GITHUB_SECRET?[(0,l.A)({clientId:process.env.GITHUB_ID,clientSecret:process.env.GITHUB_SECRET,profile:e=>({id:e.id.toString(),name:e.name||e.login,email:e.email||"",image:e.avatar_url,role:"USER"})})]:[]],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:s})=>(s&&(e.role=s.role),e),session:async({session:e,token:s})=>(s&&(e.user.id=s.sub,e.user.role=s.role),e)},pages:{signIn:"/auth/signin",signOut:"/auth/signout",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET};async function m(){let{getServerSession:e}=await r.e(5426).then(r.bind(r,35426));return e(c)}},31183:(e,s,r)=>{r.d(s,{z:()=>i});var a=r(96330);let i=globalThis.prisma??new a.PrismaClient}};