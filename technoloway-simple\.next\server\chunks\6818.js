exports.id=6818,exports.ids=[6818],exports.modules={2015:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return h},parseParameter:function(){return l}});let r=n(46143),i=n(71437),a=n(53293),o=n(72887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function c(e,t,n){let r={},l=1,c=[];for(let h of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>h.startsWith(e)),o=h.match(s);if(e&&o&&o[2]){let{key:t,optional:n,repeat:i}=u(o[2]);r[t]={pos:l++,repeat:i,optional:n},c.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:i}=u(o[2]);r[e]={pos:l++,repeat:t,optional:i},n&&o[1]&&c.push("/"+(0,a.escapeStringRegexp)(o[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";n&&o[1]&&(s=s.substring(1)),c.push(s)}else c.push("/"+(0,a.escapeStringRegexp)(h));t&&o&&o[3]&&c.push((0,a.escapeStringRegexp)(o[3]))}return{parameterizedRoute:c.join(""),groups:r}}function h(e,t){let{includeSuffix:n=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:o}=c(e,n,r),s=a;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:o}}function d(e){let t,{interceptionMarker:n,getSafeRouteKey:r,segment:i,routeKeys:o,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:c,optional:h,repeat:d}=u(i),f=c.replace(/\W/g,"");s&&(f=""+s+f);let p=!1;(0===f.length||f.length>30)&&(p=!0),isNaN(parseInt(f.slice(0,1)))||(p=!0),p&&(f=r());let m=f in o;s?o[f]=""+s+c:o[f]=c;let g=n?(0,a.escapeStringRegexp)(n):"";return t=m&&l?"\\k<"+f+">":d?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",h?"(?:/"+g+t+")?":"/"+g+t}function f(e,t,n,l,u){let c,h=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},p=[];for(let c of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),o=c.match(s);if(e&&o&&o[2])p.push(d({getSafeRouteKey:h,interceptionMarker:o[1],segment:o[2],routeKeys:f,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(o&&o[2]){l&&o[1]&&p.push("/"+(0,a.escapeStringRegexp)(o[1]));let e=d({getSafeRouteKey:h,segment:o[2],routeKeys:f,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&o[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,a.escapeStringRegexp)(c));n&&o&&o[3]&&p.push((0,a.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:f}}function p(e,t){var n,r,i;let a=f(e,t.prefixRouteKeys,null!=(n=t.includeSuffix)&&n,null!=(r=t.includePrefix)&&r,null!=(i=t.backreferenceDuplicateKeys)&&i),o=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...h(e,t),namedRegex:"^"+o+"$",routeKeys:a.routeKeys}}function m(e,t){let{parameterizedRoute:n}=c(e,!1,!1),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:i}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(r?"(?:(/.*)?)":"")+"$"}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],i=n[0];if(Array.isArray(r)&&Array.isArray(i)){if(r[0]!==i[0]||r[2]!==i[2])return!0}else if(r!==i)return!0;if(t[4])return!n[4];if(n[4])return!0;let a=Object.values(t[1])[0],o=Object.values(n[1])[0];return!a||!o||e(a,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},5144:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let r=n(51550),i=n(59656);var a=i._("_maxConcurrency"),o=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,n,i=new Promise((e,r)=>{t=e,n=r}),a=async()=>{try{r._(this,o)[o]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,o)[o]--,r._(this,l)[l]()}};return r._(this,s)[s].push({promiseFn:i,task:a}),r._(this,l)[l](),i}bump(e){let t=r._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,s)[s].splice(t,1)[0];r._(this,s)[s].unshift(e),r._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),r._(this,a)[a]=e,r._(this,o)[o]=0,r._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(r._(this,o)[o]<r._(this,a)[a]||e)&&r._(this,s)[s].length>0){var t;null==(t=r._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return h}});let r=n(59008),i=n(59154),a=n(75076);function o(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function s(e,t,n){return o(e,t===i.PrefetchKind.FULL,n)}function l(e){let{url:t,nextUrl:n,tree:r,prefetchCache:a,kind:s,allowAliasing:l=!0}=e,u=function(e,t,n,r,a){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[n,null])){let n=o(e,!0,s),l=o(e,!1,s),u=e.search?n:l,c=r.get(u);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let h=r.get(l);if(a&&e.search&&t!==i.PrefetchKind.FULL&&h&&!h.key.includes("%"))return{...h,aliased:!0}}if(t!==i.PrefetchKind.FULL&&a){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,n,a,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:s||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:n,prefetchCache:r,url:a,data:o,kind:l}=e,u=o.couldBeIntercepted?s(a,l,t):s(a,l),c={treeAtTimeOfPrefetch:n,data:Promise.resolve(o),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:a};return r.set(u,c),c}function c(e){let{url:t,kind:n,tree:o,nextUrl:l,prefetchCache:u}=e,c=s(t,n),h=a.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:o,nextUrl:l,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:i}=e,a=r.get(i);if(!a)return;let o=s(t,a.kind,n);return r.set(o,{...a,key:o}),r.delete(i),o}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=n?n:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:o,data:h,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,d),d}function h(e){for(let[t,n]of e)p(n)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:a}=e;return -1!==a?Date.now()<n+a?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+d?r?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<n+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<n+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6341:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return f}});let r=n(79551),i=n(11959),a=n(12437),o=n(2015),s=n(78034),l=n(15526),u=n(72887),c=n(74722),h=n(46143),d=n(47912);function f(e,t,n){let i=(0,r.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let r=e!==h.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(h.NEXT_QUERY_PARAM_PREFIX),a=e!==h.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(h.NEXT_INTERCEPTION_MARKER_PREFIX);(r||a||t.includes(e)||n&&Object.keys(n.groups).includes(e))&&delete i.query[e]}e.url=(0,r.format)(i)}function p(e,t,n){if(!n)return e;for(let r of Object.keys(n.groups)){let i,{optional:a,repeat:o}=n.groups[r],s=`[${o?"...":""}${r}]`;a&&(s=`[${s}]`);let l=t[r];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,i)}return e}function m(e,t,n,r){let i={};for(let a of Object.keys(t.groups)){let o=e[a];"string"==typeof o?o=(0,c.normalizeRscURL)(o):Array.isArray(o)&&(o=o.map(c.normalizeRscURL));let s=n[a],l=t.groups[a].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(o)?o.some(t=>t.includes(e)):null==o?void 0:o.includes(e)):null==o?void 0:o.includes(s))||void 0===o&&!(l&&r))return{params:{},hasValidParams:!1};l&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${a}]]`))&&(o=void 0,delete e[a]),o&&"string"==typeof o&&t.groups[a].repeat&&(o=o.split("/")),o&&(i[a]=o)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:n,rewrites:r,pageIsDynamic:c,trailingSlash:h,caseSensitive:g}){let y,v,b;return c&&(y=(0,o.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(v=(0,s.getRouteMatcher)(y))(e)),{handleRewrites:function(o,s){let d={},f=s.pathname,p=r=>{let u=(0,a.getPathMatch)(r.source+(h?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!s.pathname)return!1;let p=u(s.pathname);if((r.has||r.missing)&&p){let e=(0,l.matchHas)(o,s.query,r.has,r.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:a,destQuery:o}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:p,query:s.query});if(a.protocol)return!0;if(Object.assign(d,o,p),Object.assign(s.query,a.query),delete a.query,Object.assign(s,a),!(f=s.pathname))return!1;if(n&&(f=f.replace(RegExp(`^${n}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(f,t.locales);f=e.pathname,s.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(f===e)return!0;if(c&&v){let e=v(f);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])p(e);if(f!==e){let t=!1;for(let e of r.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(f||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of r.fallback||[])if(t=p(e))break}}return d},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:n}=y,r=(0,s.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let n=(0,d.normalizeNextQueryParam)(e);n&&(r[n]=t,delete r[e])}let i={};for(let e of Object.keys(n)){let a=n[e];if(!a)continue;let o=t[a],s=r[e];if(!o.optional&&!s)return null;i[o.pos]=s}return i}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>y&&b?m(e,y,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>f(e,t,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}function y(e,t){return"string"==typeof e[h.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[h.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[h.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6361:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let r=n(96127);function i(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6510:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(43210);let i=r.forwardRef(function({title:e,titleId:t,...n},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))})},7044:(e,t,n)=>{"use strict";n.d(t,{B:()=>r});let r="undefined"!=typeof window},8304:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return o},getExtensionRegexString:function(){return l},isMetadataPage:function(){return h},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let r=n(12958),i=n(74722),a=n(70554),o={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,n){let i=(n?"":"?")+"$",a=`\\d?${n?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${o.icon.filename}${a}${l(o.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${o.apple.filename}${a}${l(o.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${o.openGraph.filename}${a}${l(o.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${o.twitter.filename}${a}${l(o.twitter.extensions,t)}${i}`)],u=(0,r.normalizePathSep)(e);return s.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function h(e){return!(0,a.isAppRouteRoute)(e)&&u(e,[],!1)}function d(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&u(t,[],!1)}},8830:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return h},handleAliasedPrefetchEntry:function(){return c}});let r=n(83913),i=n(89752),a=n(86770),o=n(57391),s=n(33123),l=n(33898),u=n(59435);function c(e,t,n,c,d){let f,p=t.tree,m=t.cache,g=(0,o.createHrefFromUrl)(c);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=h(n,Object.fromEntries(c.searchParams));let{seedData:o,isRootRender:u,pathToSegment:d}=t,y=["",...d];n=h(n,Object.fromEntries(c.searchParams));let v=(0,a.applyRouterStatePatchToTree)(y,p,n,g),b=(0,i.createEmptyCacheNode)();if(u&&o){let t=o[1];b.loading=o[3],b.rsc=t,function e(t,n,i,a,o){if(0!==Object.keys(a[1]).length)for(let l in a[1]){let u,c=a[1][l],h=c[0],d=(0,s.createRouterCacheKey)(h),f=null!==o&&void 0!==o[2][l]?o[2][l]:null;if(null!==f){let e=f[1],n=f[3];u={lazyData:null,rsc:h.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=n.parallelRoutes.get(l);p?p.set(d,u):n.parallelRoutes.set(l,new Map([[d,u]])),e(t,u,i,c,f)}}(e,b,m,n,o)}else b.rsc=m.rsc,b.prefetchRsc=m.prefetchRsc,b.loading=m.loading,b.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,m,t);v&&(p=v,m=b,f=!0)}return!!f&&(d.patchedTree=p,d.cache=m,d.canonicalUrl=g,d.hashFragment=c.hash,(0,u.handleMutable)(t,d))}function h(e,t){let[n,i,...a]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),i,...a];let o={};for(let[e,n]of Object.entries(i))o[e]=h(n,t);return[n,o,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12157:(e,t,n)=>{"use strict";n.d(t,{L:()=>r});let r=(0,n(43210).createContext)({})},12437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let r=n(35362);function i(e,t){let n=[],i=(0,r.pathToRegexp)(e,n,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,n);return(e,r)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of n)"number"==typeof e.name&&delete i.params[e.name];return{...r,...i.params}}}},12958:(e,t)=>{"use strict";function n(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return n}})},15124:(e,t,n)=>{"use strict";n.d(t,{E:()=>i});var r=n(43210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},15526:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return h},prepareDestination:function(){return d}});let r=n(35362),i=n(53293),a=n(76759),o=n(71437),s=n(88212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,n,r){void 0===n&&(n=[]),void 0===r&&(r=[]);let i={},a=n=>{let r,a=n.key;switch(n.type){case"header":a=a.toLowerCase(),r=e.headers[a];break;case"cookie":r="cookies"in e?e.cookies[n.key]:(0,s.getCookieParser)(e.headers)()[n.key];break;case"query":r=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!n.value&&r)return i[function(e){let t="";for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);(r>64&&r<91||r>96&&r<123)&&(t+=e[n])}return t}(a)]=r,!0;if(r){let e=RegExp("^"+n.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===n.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!n.every(e=>a(e))||r.some(e=>a(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let n of Object.keys(t))e.includes(":"+n)&&(e=e.replace(RegExp(":"+n+"\\*","g"),":"+n+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+n+"\\?","g"),":"+n+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+n+"\\+","g"),":"+n+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+n+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+n));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function h(e){let t=e.destination;for(let n of Object.keys({...e.params,...e.query}))n&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(n),"g"),"__ESC_COLON_"+n));let n=(0,a.parseUrl)(t),r=n.pathname;r&&(r=l(r));let o=n.href;o&&(o=l(o));let s=n.hostname;s&&(s=l(s));let u=n.hash;return u&&(u=l(u)),{...n,pathname:r,hostname:s,href:o,hash:u}}function d(e){let t,n,i=Object.assign({},e.query),a=h(e),{hostname:s,query:u}=a,d=a.pathname;a.hash&&(d=""+d+a.hash);let f=[],p=[];for(let e of((0,r.pathToRegexp)(d,p),p))f.push(e.name);if(s){let e=[];for(let t of((0,r.pathToRegexp)(s,e),e))f.push(t.name)}let m=(0,r.compile)(d,{validate:!1});for(let[n,i]of(s&&(t=(0,r.compile)(s,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[n]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[n]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>f.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(d))for(let t of d.split("/")){let n=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(n){"(..)(..)"===n?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=n;break}}try{let[r,i]=(n=m(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=r,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:n,destQuery:u,parsedDestination:a}}},16189:(e,t,n)=>{"use strict";var r=n(65773);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}})},18171:(e,t,n)=>{"use strict";n.d(t,{s:()=>i});var r=n(74479);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},18468:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let o=a.length<=2,[s,l]=a,u=(0,r.createRouterCacheKey)(l),c=n.parallelRoutes.get(s);if(!c)return;let h=t.parallelRoutes.get(s);if(h&&h!==c||(h=new Map(c),t.parallelRoutes.set(s,h)),o)return void h.delete(u);let d=c.get(u),f=h.get(u);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},h.set(u,f)),e(f,d,(0,i.getNextFlightSegmentPath)(a)))}}});let r=n(33123),i=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{"use strict";function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},21279:(e,t,n)=>{"use strict";n.d(t,{t:()=>r});let r=(0,n(43210).createContext)(null)},22308:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,i,,o]=t;for(let s in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=n,t[3]="refresh"),i)e(i[s],n)}},refreshInactiveParallelSegments:function(){return o}});let r=n(56928),i=n(59008),a=n(83913);async function o(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:n,updatedTree:a,updatedCache:o,includeNextUrl:l,fetchedSegments:u,rootTree:c=a,canonicalUrl:h}=e,[,d,f,p]=a,m=[];if(f&&f!==h&&"refresh"===p&&!u.has(f)){u.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,o,o,e)});m.push(e)}for(let e in d){let r=s({navigatedAt:t,state:n,updatedTree:d[e],updatedCache:o,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:h});m.push(r)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),n(44827);let r=n(42785);function i(e,t,n){void 0===n&&(n=!0);let i=new URL("http://n"),a=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:o,searchParams:s,search:l,hash:u,href:c,origin:h}=new URL(e,a);if(h!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:n?(0,r.searchParamsToUrlQuery)(s):void 0,search:l,hash:u,href:c.slice(h.length)}}},24642:(e,t)=>{"use strict";function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25232:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:x,isExternalUrl:E,navigateType:R,shouldScroll:T,allowAliasing:_}=n,w={},{hash:S}=x,A=(0,i.createHrefFromUrl)(x),M="push"===R;if((0,g.prunePrefetchCache)(t.prefetchCache),w.preserveCustomHistoryState=!1,w.pendingPush=M,E)return b(t,w,x.toString(),M);if(document.getElementById("__next-page-redirect"))return b(t,w,A,M);let j=(0,g.getOrCreatePrefetchCacheEntry)({url:x,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:_}),{treeAtTimeOfPrefetch:O,data:C}=j;return d.prefetchQueue.bump(C),C.then(d=>{let{flightData:g,canonicalUrl:E,postponed:R}=d,_=Date.now(),C=!1;if(j.lastUsedTime||(j.lastUsedTime=_,C=!0),j.aliased){let r=(0,v.handleAliasedPrefetchEntry)(_,t,g,x,w);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return b(t,w,g,M);let D=E?(0,i.createHrefFromUrl)(E):A;if(S&&t.canonicalUrl.split("#",1)[0]===D.split("#",1)[0])return w.onlyHashChange=!0,w.canonicalUrl=D,w.shouldScroll=T,w.hashFragment=S,w.scrollableSegments=[],(0,c.handleMutable)(t,w);let L=t.tree,k=t.cache,N=[];for(let e of g){let{pathToSegment:n,seedData:i,head:c,isHeadPartial:d,isRootRender:g}=e,v=e.tree,E=["",...n],T=(0,o.applyRouterStatePatchToTree)(E,L,v,A);if(null===T&&(T=(0,o.applyRouterStatePatchToTree)(E,O,v,A)),null!==T){if(i&&g&&R){let e=(0,m.startPPRNavigation)(_,k,L,v,i,c,d,!1,N);if(null!==e){if(null===e.route)return b(t,w,A,M);T=e.route;let n=e.node;null!==n&&(w.cache=n);let i=e.dynamicRequestTree;if(null!==i){let n=(0,r.fetchServerResponse)(x,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,n)}}else T=v}else{if((0,l.isNavigatingToNewRootLayout)(L,T))return b(t,w,A,M);let r=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(j.status!==u.PrefetchCacheEntryStatus.stale||C?i=(0,h.applyFlightData)(_,k,r,e,j):(i=function(e,t,n,r){let i=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),P(r).map(e=>[...n,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,a),i=!0;return i}(r,k,n,v),j.lastUsedTime=_),(0,s.shouldHardNavigate)(E,L)?(r.rsc=k.rsc,r.prefetchRsc=k.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(r,k,n),w.cache=r):i&&(w.cache=r,k=r),P(v))){let e=[...n,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&N.push(e)}}L=T}}return w.patchedTree=L,w.canonicalUrl=D,w.scrollableSegments=N,w.hashFragment=S,w.shouldScroll=T,(0,c.handleMutable)(t,w)},()=>t)}}});let r=n(59008),i=n(57391),a=n(18468),o=n(86770),s=n(65951),l=n(2030),u=n(59154),c=n(59435),h=n(56928),d=n(75076),f=n(89752),p=n(83913),m=n(65956),g=n(5334),y=n(97464),v=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function P(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,i]of Object.entries(r))for(let r of P(i))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,n)=>{"use strict";function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26001:(e,t,n)=>{"use strict";let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function a(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function o(e,t,n,r){if("function"==typeof t){let[i,o]=a(r);t=t(void 0!==n?n:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=a(r);t=t(void 0!==n?n:e.custom,i,o)}return t}function s(e,t,n){let r=e.getProps();return o(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>aS});let u=e=>e,c={},h=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function f(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,o=h.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,a=!1,o=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){o.has(t)&&(c.schedule(t),e()),l++,t(s)}let c={schedule:(e,t=!1,a=!1)=>{let s=a&&i?n:r;return t&&o.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),o.delete(e)},process:e=>{if(s=e,i){a=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&d.value&&d.value.frameloop[t].push(l),l=0,n.clear(),i=!1,a&&(a=!1,c.process(e))}};return c}(a,t?n:void 0),e),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:g,postRender:y}=o,v=()=>{let a=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),g.process(i),y.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(v))},b=()=>{n=!0,r=!0,i.isProcessing||e(v)};return{schedule:h.reduce((e,t)=>{let r=o[t];return e[t]=(e,t=!1,i=!1)=>(n||b(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<h.length;t++)o[h[t]].cancel(e)},state:i,steps:o}}let{schedule:p,cancel:m,state:g,steps:y}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),P=new Set(["width","height","top","left","right","bottom",...v]);function x(e,t){-1===e.indexOf(t)&&e.push(t)}function E(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class R{constructor(){this.subscriptions=[]}add(e){return x(this.subscriptions,e),()=>E(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function T(){r=void 0}let _={now:()=>(void 0===r&&_.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(T)}},w=e=>!isNaN(parseFloat(e)),S={current:void 0};class A{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=_.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=_.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=w(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new R);let n=this.events[e].add(t);return"change"===e?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return S.current&&S.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=_.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(e,t){return new A(e,t)}let j=e=>Array.isArray(e),O=e=>!!(e&&e.getVelocity);function C(e,t){let n=e.getValue("willChange");if(O(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let D=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+D("framerAppearId"),k=(e,t)=>n=>t(e(n)),N=(...e)=>e.reduce(k),U=(e,t,n)=>n>t?t:n<e?e:n,I=e=>1e3*e,V=e=>e/1e3,F={layout:0,mainThread:0,waapi:0},B=()=>{},H=()=>{},$=e=>t=>"string"==typeof t&&t.startsWith(e),z=$("--"),W=$("var(--"),K=e=>!!W(e)&&X.test(e.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,q={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},Y={...q,transform:e=>U(0,1,e)},G={...q,default:1},Q=e=>Math.round(1e5*e)/1e5,Z=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,a,o,s]=r.match(Z);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(o),alpha:void 0!==s?parseFloat(s):1}},en=e=>U(0,255,e),er={...q,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Q(Y.transform(r))+")"},ea={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},eo=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),es=eo("deg"),el=eo("%"),eu=eo("px"),ec=eo("vh"),eh=eo("vw"),ed={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Q(t))+", "+el.transform(Q(n))+", "+Q(Y.transform(r))+")"},ep={test:e=>ei.test(e)||ea.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):ea.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e)},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ey="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],a=0,o=t.replace(ev,e=>(ep.test(e)?(r.color.push(a),i.push(ey),n.push(ep.parse(e))):e.startsWith("var(")?(r.var.push(a),i.push("var"),n.push(e)):(r.number.push(a),i.push(eg),n.push(parseFloat(e))),++a,"${}")).split("${}");return{values:n,split:o,indexes:r,types:i}}function eP(e){return eb(e).values}function ex(e){let{split:t,types:n}=eb(e),r=t.length;return e=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],void 0!==e[a]){let t=n[a];t===eg?i+=Q(e[a]):t===ey?i+=ep.transform(e[a]):i+=e[a]}return i}}let eE=e=>"number"==typeof e?0:e,eR={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Z)?.length||0)+(e.match(em)?.length||0)>0},parse:eP,createTransformer:ex,getAnimatableNone:function(e){let t=eP(e);return ex(e)(t.map(eE))}};function eT(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function e_(e,t){return n=>n>0?t:e}let ew=(e,t,n)=>e+(t-e)*n,eS=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eA=[ea,ei,ef],eM=e=>eA.find(t=>t.test(e));function ej(e){let t=eM(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ef&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,a=0,o=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,s=2*n-r;i=eT(s,r,e+1/3),a=eT(s,r,e),o=eT(s,r,e-1/3)}else i=a=o=n;return{red:Math.round(255*i),green:Math.round(255*a),blue:Math.round(255*o),alpha:r}}(n)),n}let eO=(e,t)=>{let n=ej(e),r=ej(t);if(!n||!r)return e_(e,t);let i={...n};return e=>(i.red=eS(n.red,r.red,e),i.green=eS(n.green,r.green,e),i.blue=eS(n.blue,r.blue,e),i.alpha=ew(n.alpha,r.alpha,e),ei.transform(i))},eC=new Set(["none","hidden"]);function eD(e,t){return n=>ew(e,t,n)}function eL(e){return"number"==typeof e?eD:"string"==typeof e?K(e)?e_:ep.test(e)?eO:eU:Array.isArray(e)?ek:"object"==typeof e?ep.test(e)?eO:eN:e_}function ek(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>eL(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eN(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=eL(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eU=(e,t)=>{let n=eR.createTransformer(t),r=eb(e),i=eb(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eC.has(e)&&!i.values.length||eC.has(t)&&!r.values.length?function(e,t){return eC.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):N(ek(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let a=t.types[i],o=e.indexes[a][r[a]],s=e.values[o]??0;n[i]=s,r[a]++}return n}(r,i),i.values),n):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),e_(e,t))};function eI(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?ew(e,t,n):eL(e)(e,t)}let eV=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:_.now()}},eF=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=e(t/(i-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function eB(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function eH(e,t,n){var r,i;let a=Math.max(t-5,0);return r=n-e(a),(i=t-a)?1e3/i*r:0}let e$={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ez(e,t){return e*Math.sqrt(1-t*t)}let eW=["duration","bounce"],eK=["stiffness","damping","mass"];function eX(e,t){return t.some(t=>void 0!==e[t])}function eq(e=e$.visualDuration,t=e$.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:a}=r,o=r.keyframes[0],s=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:e$.velocity,stiffness:e$.stiffness,damping:e$.damping,mass:e$.mass,isResolvedFromDuration:!1,...e};if(!eX(e,eK)&&eX(e,eW))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*U(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:e$.mass,stiffness:r,damping:i}}else{let n=function({duration:e=e$.duration,bounce:t=e$.bounce,velocity:n=e$.velocity,mass:r=e$.mass}){let i,a;B(e<=I(e$.maxDuration),"Spring duration must be 10 seconds or less");let o=1-t;o=U(e$.minDamping,e$.maxDamping,o),e=U(e$.minDuration,e$.maxDuration,V(e)),o<1?(i=t=>{let r=t*o,i=r*e;return .001-(r-n)/ez(t,o)*Math.exp(-i)},a=t=>{let r=t*o*e,a=Math.pow(o,2)*Math.pow(t,2)*e,s=Math.exp(-r),l=ez(Math.pow(t,2),o);return(r*n+n-a)*s*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),a=t=>e*e*(n-t)*Math.exp(-t*e));let s=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,a,5/e);if(e=I(e),isNaN(s))return{stiffness:e$.stiffness,damping:e$.damping,duration:e};{let t=Math.pow(s,2)*r;return{stiffness:t,damping:2*o*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:e$.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-V(r.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*h)),y=s-o,v=V(Math.sqrt(u/h)),b=5>Math.abs(y);if(i||(i=b?e$.restSpeed.granular:e$.restSpeed.default),a||(a=b?e$.restDelta.granular:e$.restDelta.default),g<1){let e=ez(v,g);n=t=>s-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)n=e=>s-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*v*t),r=Math.min(e*t,300);return s-n*((m+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let P={calculatedDuration:p&&d||null,next:e=>{let t=n(e);if(p)l.done=e>=d;else{let r=0===e?m:0;g<1&&(r=0===e?I(m):eH(n,e,t));let o=Math.abs(s-t)<=a;l.done=Math.abs(r)<=i&&o}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(eB(P),2e4),t=eF(t=>P.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return P}function eY({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:o,min:s,max:l,restDelta:u=.5,restSpeed:c}){let h,d,f=e[0],p={done:!1,value:f},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,g=e=>void 0===s?l:void 0===l||Math.abs(s-e)<Math.abs(l-e)?s:l,y=n*t,v=f+y,b=void 0===o?v:o(v);b!==v&&(y=b-f);let P=e=>-y*Math.exp(-e/r),x=e=>b+P(e),E=e=>{let t=P(e),n=x(e);p.done=Math.abs(t)<=u,p.value=p.done?b:n},R=e=>{m(p.value)&&(h=e,d=eq({keyframes:[p.value,g(p.value)],velocity:eH(x,e,p.value),damping:i,stiffness:a,restDelta:u,restSpeed:c}))};return R(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,E(e),R(e)),void 0!==h&&e>=h)?d.next(e-h):(t||E(e),p)}}}eq.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eB(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:V(i)}}(e,100,eq);return e.ease=t.ease,e.duration=I(t.duration),e.type="keyframes",e};let eG=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eQ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let a,o,s=0;do(a=eG(o=t+(n-t)/2,r,i)-e)>0?n=o:t=o;while(Math.abs(a)>1e-7&&++s<12);return o})(t,0,1,e,n);return e=>0===e||1===e?e:eG(i(e),t,r)}let eZ=eQ(.42,0,1,1),eJ=eQ(0,0,.58,1),e0=eQ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e3=e=>t=>1-e(1-t),e5=eQ(.33,1.53,.69,.99),e4=e3(e5),e9=e2(e4),e7=e=>(e*=2)<1?.5*e4(e):.5*(2-Math.pow(2,-10*(e-1))),e6=e=>1-Math.sin(Math.acos(e)),e8=e3(e6),te=e2(e6),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eZ,easeInOut:e0,easeOut:eJ,circIn:e6,circInOut:te,circOut:e8,backIn:e4,backInOut:e9,backOut:e5,anticipate:e7},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){H(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eQ(t,n,r,i)}return tr(e)?(H(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},ta=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function to({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let a=e1(r)?r.map(ti):ti(r),o={done:!1,value:t[0]},s=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let a=e.length;if(H(a===t.length,"Both input and output ranges must be the same length"),1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];let o=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,n){let r=[],i=n||c.mix||eI,a=e.length-1;for(let n=0;n<a;n++){let a=i(e[n],e[n+1]);t&&(a=N(Array.isArray(t)?t[n]||u:t,a)),r.push(a)}return r}(t,r,i),l=s.length,h=n=>{if(o&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=ta(e[r],e[r+1],n);return s[r](i)};return n?t=>h(U(e[0],e[a-1],t)):h}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=ta(0,t,r);e.push(ew(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(a)?a:t.map(()=>a||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=s(t),o.done=t>=e,o)}}let ts=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let a=e.filter(ts),o=i<0||t&&"loop"!==n&&t%2==1?0:a.length-1;return o&&void 0!==r?r:a[o]}let tu={decay:eY,inertia:eY,tween:to,keyframes:to,spring:eq};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class th{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let td=e=>e/100;class tf extends th{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==_.now()&&this.tick(_.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},F.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=to,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=e,{keyframes:o}=e,s=t||to;s!==to&&"number"!=typeof o[0]&&(this.mixKeyframes=N(td,eI(o[0],o[1])),o=[0,100]);let l=s({...e,keyframes:o});"mirror"===i&&(this.mirroredGenerator=s({...e,keyframes:[...o].reverse(),velocity:-a})),null===l.calculatedDuration&&(l.calculatedDuration=eB(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:s}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/o,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===h?(n=1-n,d&&(n-=d/o)):"mirror"===h&&(b=a)),v=U(0,1,n)*o}let P=y?{done:!1,value:u[0]}:b.next(v);i&&(P.value=i(P.value));let{done:x}=P;y||null===s||(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let E=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return E&&f!==eY&&(P.value=tl(u,this.options,m,this.speed)),p&&p(P.value),E&&this.finish(),P}then(e,t){return this.finished.then(e,t)}get duration(){return V(this.calculatedDuration)}get time(){return V(this.currentTime)}set time(e){e=I(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(_.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=V(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eV,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(_.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,F.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>ty(tp(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ty=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tP={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tb,scale:e=>(tv(e)+tb(e))/2,rotateX:e=>ty(tp(Math.atan2(e[6],e[5]))),rotateY:e=>ty(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tx(e){return+!!e.includes("scale")}function tE(e,t){let n,r;if(!e||"none"===e)return tx(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tP,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tx(t);let a=n[t],o=r[1].split(",").map(tT);return"function"==typeof a?a(o):o[a]}let tR=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tE(n,t)};function tT(e){return parseFloat(e.trim())}let t_=e=>e===q||e===eu,tw=new Set(["x","y","z"]),tS=v.filter(e=>!tw.has(e)),tA={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tE(t,"x"),y:(e,{transform:t})=>tE(t,"y")};tA.translateX=tA.x,tA.translateY=tA.y;let tM=new Set,tj=!1,tO=!1,tC=!1;function tD(){if(tO){let e=Array.from(tM).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tS.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tO=!1,tj=!1,tM.forEach(e=>e.complete(tC)),tM.clear()}function tL(){tM.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tO=!0)})}class tk{constructor(e,t,n,r,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(tM.add(this),tj||(tj=!0,p.read(tL),p.resolveKeyframes(tD))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,a);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tM.delete(this)}cancel(){"scheduled"===this.state&&(tM.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tN=e=>e.startsWith("--");function tU(e){let t;return()=>(void 0===t&&(t=e()),t)}let tI=tU(()=>void 0!==window.ScrollTimeline),tV={},tF=function(e,t){let n=tU(e);return()=>tV[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tB=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tB([0,.65,.55,1]),circOut:tB([.55,0,1,.45]),backIn:tB([.31,.01,.66,-.59]),backOut:tB([.33,1.53,.69,.99])};function t$(e){return"function"==typeof e&&"applyToOptions"in e}class tz extends th{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:o,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=e,H("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return t$(e)&&tF()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:a=0,repeatType:o="loop",ease:s="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let h=function e(t,n){if(t)return"function"==typeof t?tF()?eF(t,n):"ease-out":tt(t)?tB(t):Array.isArray(t)?t.map(t=>e(t,n)||tH.easeOut):tH[t]}(s,i);Array.isArray(h)&&(c.easing=h),d.value&&F.waapi++;let f={delay:r,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return d.value&&p.finished.finally(()=>{F.waapi--}),p}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tN(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return V(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return V(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=I(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tI())?(this.animation.timeline=e,u):t(this)}}let tW={anticipate:e7,backInOut:e9,circInOut:te};class tK extends tz{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tW&&(e.ease=tW[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...a}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let o=new tf({...a,autoplay:!1}),s=I(this.finishedTime??this.time);t.setWithVelocity(o.sample(s-10).value,o.sample(s).value,10),o.stop()}}let tX=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eR.test(e)||"0"===e)&&!e.startsWith("url("));var tq,tY,tG=n(18171);let tQ=new Set(["opacity","clipPath","filter","transform"]),tZ=tU(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends th{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:a="loop",keyframes:o,name:s,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=_.now();let h={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:a,name:s,motionValue:l,element:u,...c},d=u?.KeyframeResolver||tk;this.keyframeResolver=new d(o,(e,t,n)=>this.onKeyframesResolved(e,t,h,!n),s,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:a,velocity:o,delay:s,isHandoff:l,onUpdate:h}=n;this.resolvedAt=_.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let a=e[e.length-1],o=tX(i,t),s=tX(a,t);return B(o===s,`You are trying to animate ${t} from "${i}" to "${a}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${a} via the \`style\` property.`),!!o&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||t$(n))&&r)}(e,i,a,o)&&((c.instantAnimations||!s)&&h?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let d={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},f=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:a,type:o}=e;if(!(0,tG.s)(t?.owner?.current))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return tZ()&&n&&tQ.has(n)&&("transform"!==n||!l)&&!s&&!r&&"mirror"!==i&&0!==a&&"inertia"!==o}(d)?new tK({...d,element:d.motionValue.owner.current}):new tf(d);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tC=!0,tL(),tD(),tC=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t3={type:"keyframes",duration:.8},t5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t4=(e,{keyframes:t})=>t.length>2?t3:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t5,t9=(e,t,n,r={},i,a)=>o=>{let s=l(r,e)||{},u=s.delay||r.delay||0,{elapsed:h=0}=r;h-=I(u);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-h,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{o(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:a?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:a,repeatType:o,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&Object.assign(d,t4(e,d)),d.duration&&(d.duration=I(d.duration)),d.repeatDelay&&(d.repeatDelay=I(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let f=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,d.duration=0,d.delay=0),d.allowFlatten=!s.type&&!s.ease,f&&!a&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),a=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[a]}(d.keyframes,s);if(void 0!==e)return void p.update(()=>{d.onUpdate(e),d.onComplete()})}return s.isSync?new tf(d):new tJ(d)};function t7(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:a=e.getDefaultTransition(),transitionEnd:o,...u}=t;r&&(a=r);let c=[],h=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||h&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(h,t))continue;let o={delay:n,...l(a||{},t)},s=r.get();if(void 0!==s&&!r.isAnimating&&!Array.isArray(i)&&i===s&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=e.props[L];if(n){let e=window.MotionHandoffAnimation(n,t,p);null!==e&&(o.startTime=e,d=!0)}}C(e,t),r.start(t9(t,r,i,e.shouldReduceMotion&&P.has(t)?{type:!1}:o,e,d));let f=r.animation;f&&c.push(f)}return o&&Promise.all(c).then(()=>{p.update(()=>{o&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=s(e,t)||{};for(let t in i={...i,...n}){var a;let n=j(a=i[t])?a[a.length-1]||0:a;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,M(n))}}(e,o)})}),c}function t6(e,t,n={}){let r=s(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let a=r?()=>Promise.all(t7(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:a=0,staggerChildren:o,staggerDirection:s}=i;return function(e,t,n=0,r=0,i=1,a){let o=[],s=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>s-e*r;return Array.from(e.variantChildren).sort(t8).forEach((e,r)=>{e.notify("AnimationStart",t),o.push(t6(e,t,{...a,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,a+r,o,s,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([a(),o(n.delay)]);{let[e,t]="beforeChildren"===l?[a,o]:[o,a];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,na=[...nn].reverse(),no=nn.length;function ns(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:ns(!0),whileInView:ns(),whileHover:ns(),whileTap:ns(),whileDrag:ns(),whileFocus:ns(),exit:ns()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t6(e,t,n)));else if("string"==typeof t)r=t6(e,t,n);else{let i="function"==typeof t?s(e,t,n.custom):t;r=Promise.all(t7(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,a=t=>(n,r)=>{let i=s(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function o(o){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],h=new Set,d={},f=1/0;for(let t=0;t<no;t++){var p,m;let s=na[t],g=n[s],y=void 0!==l[s]?l[s]:u[s],v=nt(y),b=s===o?g.isActive:null;!1===b&&(f=t);let P=y===u[s]&&y!==l[s]&&v;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),g.protectedKeys={...d},!g.isActive&&null===b||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let x=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!ne(m,p)),E=x||s===o&&g.isActive&&!P&&v||t>f&&v,R=!1,T=Array.isArray(y)?y:[y],_=T.reduce(a(s),{});!1===b&&(_={});let{prevResolvedValues:w={}}=g,S={...w,..._},A=t=>{E=!0,h.has(t)&&(R=!0,h.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in S){let t=_[e],n=w[e];if(d.hasOwnProperty(e))continue;let r=!1;(j(t)&&j(n)?ne(t,n):t===n)?void 0!==t&&h.has(e)?A(e):g.protectedKeys[e]=!0:null!=t?A(e):h.add(e)}g.prevProp=y,g.prevResolvedValues=_,g.isActive&&(d={...d,..._}),r&&e.blockInitialAnimation&&(E=!1);let M=!(P&&x)||R;E&&M&&c.push(...T.map(e=>({animation:e,options:{type:s}})))}if(h.size){let t={};if("boolean"!=typeof l.initial){let n=s(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}h.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:o,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=o(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nh=0;class nd extends nu{constructor(){super(...arguments),this.id=nh++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nf={x:!1,y:!1};function np(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ng(e){return{point:{x:e.pageX,y:e.pageY}}}let ny=e=>t=>nm(t)&&e(t,ng(t));function nv(e,t,n,r){return np(e,t,ny(n),r)}function nb({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nP(e){return e.max-e.min}function nx(e,t,n,r=.5){e.origin=r,e.originPoint=ew(t.min,t.max,e.origin),e.scale=nP(n)/nP(t),e.translate=ew(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nE(e,t,n,r){nx(e.x,t.x,n.x,r?r.originX:void 0),nx(e.y,t.y,n.y,r?r.originY:void 0)}function nR(e,t,n){e.min=n.min+t.min,e.max=e.min+nP(t)}function nT(e,t,n){e.min=t.min-n.min,e.max=e.min+nP(t)}function n_(e,t,n){nT(e.x,t.x,n.x),nT(e.y,t.y,n.y)}let nw=()=>({translate:0,scale:1,origin:0,originPoint:0}),nS=()=>({x:nw(),y:nw()}),nA=()=>({min:0,max:0}),nM=()=>({x:nA(),y:nA()});function nj(e){return[e("x"),e("y")]}function nO(e){return void 0===e||1===e}function nC({scale:e,scaleX:t,scaleY:n}){return!nO(e)||!nO(t)||!nO(n)}function nD(e){return nC(e)||nL(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nL(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nk(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nN(e,t=0,n=1,r,i){e.min=nk(e.min,t,n,r,i),e.max=nk(e.max,t,n,r,i)}function nU(e,{x:t,y:n}){nN(e.x,t.translate,t.scale,t.originPoint),nN(e.y,n.translate,n.scale,n.originPoint)}function nI(e,t){e.min=e.min+t,e.max=e.max+t}function nV(e,t,n,r,i=.5){let a=ew(e.min,e.max,i);nN(e,t,n,a,r)}function nF(e,t){nV(e.x,t.x,t.scaleX,t.scale,t.originX),nV(e.y,t.y,t.scaleY,t.scale,t.originY)}function nB(e,t){return nb(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nH=({current:e})=>e?e.ownerDocument.defaultView:null;function n$(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nz=(e,t)=>Math.abs(e-t);class nW{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nq(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nz(e.x,t.x)**2+nz(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:a,onMove:o}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nK(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=nq("pointercancel"===e.type?this.lastMoveEventInfo:nK(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!nm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let a=nK(ng(e),this.transformPagePoint),{point:o}=a,{timestamp:s}=g;this.history=[{...o,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,nq(a,this.history)),this.removeListeners=N(nv(this.contextWindow,"pointermove",this.handlePointerMove),nv(this.contextWindow,"pointerup",this.handlePointerUp),nv(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nK(e,t){return t?{point:t(e.point)}:e}function nX(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nq({point:e},t){return{point:e,delta:nX(e,nY(t)),offset:nX(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nY(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>I(.1)));)n--;if(!r)return{x:0,y:0};let a=V(i.timestamp-r.timestamp);if(0===a)return{x:0,y:0};let o={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,.1)}}function nY(e){return e[e.length-1]}function nG(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nQ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nZ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nM(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nW(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ng(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nf[e])return null;else return nf[e]=!0,()=>{nf[e]=!1};return nf.x||nf.y?null:(nf.x=nf.y=!0,()=>{nf.x=nf.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nj(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nP(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),C(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:o}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(o),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nj(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nH(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&p.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?ew(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?ew(n,e,r.max):Math.min(e,n)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&n$(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nG(e.x,n,i),y:nG(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nZ(e,"left","right"),y:nZ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nj(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!n$(t))return!1;let r=t.current;H(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let a=function(e,t,n){let r=nB(e,n),{scroll:i}=t;return i&&(nI(r.x,i.offset.x),nI(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),o=(e=i.layout.layoutBox,{x:nQ(e.x,a.x),y:nQ(e.y,a.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=nb(e))}return o}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:o}=this.getProps(),s=this.constraints||{};return Promise.all(nj(o=>{if(!n2(o,t,this.currentDirection))return;let l=s&&s[o]||{};a&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return C(this.visualElement,e),n.start(t9(e,n,0,t,this.visualElement,!1))}stopAnimation(){nj(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nj(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nj(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-ew(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!n$(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nj(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nP(e),i=nP(t);return i>r?n=ta(t.min,t.max-r,e.min):r>i&&(n=ta(e.min,e.max-i,t.min)),U(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nj(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(ew(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=nv(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();n$(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(t);let i=np(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nj(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:o}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n3 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n5=e=>(t,n)=>{e&&p.postRender(()=>e(t,n))};class n4 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nW(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nH(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n5(e),onStart:n5(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&p.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nv(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n9=n(60687);let{schedule:n7}=f(queueMicrotask,!1);var n6=n(43210),n8=n(86044),re=n(12157);let rt=(0,n6.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ra={};class ro extends n6.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in rl)ra[e]=rl[e],z(e)&&(ra[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:a}=n;return a&&(a.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||p.postRender(()=>{let e=a.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n7.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rs(e){let[t,n]=(0,n8.xQ)(),r=(0,n6.useContext)(re.L);return(0,n9.jsx)(ro,{...e,layoutGroup:r,switchLayoutGroup:(0,n6.useContext)(rt),isPresent:t,safeToRemove:n})}let rl={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eR.parse(e);if(r.length>5)return e;let i=eR.createTransformer(e),a=+("number"!=typeof r[0]),o=n.x.scale*t.x,s=n.y.scale*t.y;r[0+a]/=o,r[1+a]/=s;let l=ew(o,s,.5);return"number"==typeof r[2+a]&&(r[2+a]/=l),"number"==typeof r[3+a]&&(r[3+a]/=l),i(r)}}};var ru=n(74479);function rc(e){return(0,ru.G)(e)&&"ownerSVGElement"in e}let rh=(e,t)=>e.depth-t.depth;class rd{constructor(){this.children=[],this.isDirty=!1}add(e){x(this.children,e),this.isDirty=!0}remove(e){E(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rh),this.isDirty=!1,this.children.forEach(e)}}function rf(e){return O(e)?e.get():e}let rp=["TopLeft","TopRight","BottomLeft","BottomRight"],rm=rp.length,rg=e=>"string"==typeof e?parseFloat(e):e,ry=e=>"number"==typeof e||eu.test(e);function rv(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rb=rx(0,.5,e8),rP=rx(.5,.95,u);function rx(e,t,n){return r=>r<e?0:r>t?1:n(ta(e,t,r))}function rE(e,t){e.min=t.min,e.max=t.max}function rR(e,t){rE(e.x,t.x),rE(e.y,t.y)}function rT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function r_(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rw(e,t,[n,r,i],a,o){!function(e,t=0,n=1,r=.5,i,a=e,o=e){if(el.test(t)&&(t=parseFloat(t),t=ew(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let s=ew(a.min,a.max,r);e===a&&(s-=t),e.min=r_(e.min,t,n,s,i),e.max=r_(e.max,t,n,s,i)}(e,t[n],t[r],t[i],t.scale,a,o)}let rS=["x","scaleX","originX"],rA=["y","scaleY","originY"];function rM(e,t,n,r){rw(e.x,t,rS,n?n.x:void 0,r?r.x:void 0),rw(e.y,t,rA,n?n.y:void 0,r?r.y:void 0)}function rj(e){return 0===e.translate&&1===e.scale}function rO(e){return rj(e.x)&&rj(e.y)}function rC(e,t){return e.min===t.min&&e.max===t.max}function rD(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rL(e,t){return rD(e.x,t.x)&&rD(e.y,t.y)}function rk(e){return nP(e.x)/nP(e.y)}function rN(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rU{constructor(){this.members=[]}add(e){x(this.members,e),e.scheduleRender()}remove(e){if(E(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rI={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rV=["","X","Y","Z"],rF={visibility:"hidden"},rB=0;function rH(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function r$({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rB++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(rI.nodes=rI.calculatedTargetDeltas=rI.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rX),d.addProjectionMetrics&&d.addProjectionMetrics(rI)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rd)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new R),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rc(t)&&!(rc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=_.now(),r=({timestamp:i})=>{let a=i-n;a>=250&&(m(r),e(a-t))};return p.setup(r,!0),()=>m(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rZ))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||i.getDefaultTransition()||r9,{onLayoutAnimationStart:o,onLayoutAnimationComplete:s}=i.getProps(),u=!this.targetLayout||!rL(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(a,"layout"),onPlay:o,onComplete:s};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rZ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[L];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rY);return}this.isUpdating||this.nodes.forEach(rG),this.isUpdating=!1,this.nodes.forEach(rQ),this.nodes.forEach(rz),this.nodes.forEach(rW),this.clearAllSnapshots();let e=_.now();g.delta=U(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rq),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nP(this.snapshot.measuredBox.x)||nP(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nM(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rO(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nD(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r8((t=r).x),r8(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nM();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nI(t.x,e.offset.x),nI(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nM();if(rR(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:a}=r;r!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&rR(t,e),nI(t.x,i.offset.x),nI(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nM();rR(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nF(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nD(r.latestValues)&&nF(n,r.latestValues)}return nD(this.latestValues)&&nF(n,this.latestValues),n}removeTransform(e){let t=nM();rR(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nD(n.latestValues))continue;nC(n.latestValues)&&n.updateSnapshot();let r=nM();rR(r,n.measurePageBox()),rM(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nD(this.latestValues)&&rM(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nM(),this.relativeTargetOrigin=nM(),n_(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rR(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nM(),this.targetWithTransforms=nM()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var a,o,s;this.forceRelativeParentToResolveTarget(),a=this.target,o=this.relativeTarget,s=this.relativeParent.target,nR(a.x,o.x,s.x),nR(a.y,o.y,s.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rR(this.target,this.layout.layoutBox),nU(this.target,this.targetDelta)):rR(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nM(),this.relativeTargetOrigin=nM(),n_(this.relativeTargetOrigin,this.target,e.target),rR(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&rI.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nC(this.parent.latestValues)||nL(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rR(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;!function(e,t,n,r=!1){let i,a,o=n.length;if(o){t.x=t.y=1;for(let s=0;s<o;s++){a=(i=n[s]).projectionDelta;let{visualElement:o}=i.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nF(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,nU(e,a)),r&&nD(i.latestValues)&&nF(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nM());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rT(this.prevProjectionDelta.x,this.projectionDelta.x),rT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nE(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.treeScale.x===a&&this.treeScale.y===o&&rN(this.projectionDelta.x,this.prevProjectionDelta.x)&&rN(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s)),d.value&&rI.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nS(),this.projectionDelta=nS(),this.projectionDeltaWithTransform=nS()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},a={...this.latestValues},o=nS();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=nM(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r4));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r3(o.x,e.x,r),r3(o.y,e.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p,m,g;n_(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=s,g=r,r5(f.x,p.x,m.x,g),r5(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,d=n,rC(u.x,d.x)&&rC(u.y,d.y))&&(this.isProjectionDirty=!1),n||(n=nM()),rR(n,this.relativeTarget)}l&&(this.animationValues=a,function(e,t,n,r,i,a){i?(e.opacity=ew(0,n.opacity??1,rb(r)),e.opacityExit=ew(t.opacity??1,0,rP(r))):a&&(e.opacity=ew(t.opacity??1,n.opacity??1,r));for(let i=0;i<rm;i++){let a=`border${rp[i]}Radius`,o=rv(t,a),s=rv(n,a);(void 0!==o||void 0!==s)&&(o||(o=0),s||(s=0),0===o||0===s||ry(o)===ry(s)?(e[a]=Math.max(ew(rg(o),rg(s),r),0),(el.test(s)||el.test(o))&&(e[a]+="%")):e[a]=s)}(t.rotate||n.rotate)&&(e.rotate=ew(t.rotate||0,n.rotate||0,r))}(a,i,this.latestValues,r,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rn.hasAnimatedSinceResize=!0,F.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(e,t,n){let r=O(e)?e:M(e);return r.start(t9("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{F.layout--},onComplete:()=>{F.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nM();let t=nP(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nP(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rR(t,n),nF(t,i),nE(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rU),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rH("z",e,r,this.animationValues);for(let t=0;t<rV.length;t++)rH(`rotate${rV[t]}`,e,r,this.animationValues),rH(`skew${rV[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rF;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rf(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rf(e?.pointerEvents)||""),this.hasProjected&&!nD(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,a=e.y.translate/t.y,o=n?.z||0;if((i||a||o)&&(r=`translate3d(${i}px, ${a}px, ${o}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:o,skewY:s}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),a&&(r+=`rotateY(${a}deg) `),o&&(r+=`skewX(${o}deg) `),s&&(r+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(r+=`scale(${s}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:a,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ra){if(void 0===i[e])continue;let{correct:n,applyTo:a,isCSSVariable:o}=ra[e],s="none"===t.transform?i[e]:n(i[e],r);if(a){let e=a.length;for(let n=0;n<e;n++)t[a[n]]=s}else o?this.options.visualElement.renderState.vars[e]=s:t[e]=s}return this.options.layoutId&&(t.pointerEvents=r===this?rf(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rY),this.root.sharedNodes.clear()}}}function rz(e){e.updateLayout()}function rW(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,a=t.source!==e.layout.source;"size"===i?nj(e=>{let r=a?t.measuredBox[e]:t.layoutBox[e],i=nP(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&nj(r=>{let i=a?t.measuredBox[r]:t.layoutBox[r],o=nP(n[r]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});let o=nS();nE(o,n,t.layoutBox);let s=nS();a?nE(s,e.applyTransform(r,!0),t.measuredBox):nE(s,n,t.layoutBox);let l=!rO(o),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:a}=r;if(i&&a){let o=nM();n_(o,t.layoutBox,i.layoutBox);let s=nM();n_(s,n,a.layoutBox),rL(o,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:s,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rK(e){d.value&&rI.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rX(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rq(e){e.clearSnapshot()}function rY(e){e.clearMeasurements()}function rG(e){e.isLayoutDirty=!1}function rQ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rZ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r3(e,t,n){e.translate=ew(t.translate,0,n),e.scale=ew(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r5(e,t,n,r){e.min=ew(t.min,n.min,r),e.max=ew(t.max,n.max,r)}function r4(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r9={duration:.45,ease:[.4,0,.1,1]},r7=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r6=r7("applewebkit/")&&!r7("chrome/")?Math.round:u;function r8(e){e.min=r6(e.min),e.max=r6(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rk(t)-rk(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=r$({attachResizeListener:(e,t)=>np(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},ia=r$({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function io(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function is(e){return!("touch"===e.pointerType||nf.x||nf.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&p.postRender(()=>i(t,ng(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=io(e,n),o=e=>{if(!is(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let a=e=>{is(e)&&(r(e),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,i)};return r.forEach(e=>{e.addEventListener("pointerenter",o,i)}),a}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=N(np(this.node.current,"focus",()=>this.onFocus()),np(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ih=(e,t)=>!!t&&(e===t||ih(e,t.parentElement)),id=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iy=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;ig(n,"down");let e=im(()=>{ig(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ig(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iv(e){return nm(e)&&!(nf.x||nf.y)}function ib(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&p.postRender(()=>i(t,ng(t)))}class iP extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=io(e,n),o=e=>{let r=e.currentTarget;if(!iv(e))return;ip.add(r);let a=t(r,e),o=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iv(e)&&"function"==typeof a&&a(e,{success:t})},s=e=>{o(e,r===window||r===document||n.useGlobalTarget||ih(r,e.target))},l=e=>{o(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",o,i),(0,tG.s)(e))&&(e.addEventListener("focus",e=>iy(e,i)),id.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ix=new WeakMap,iE=new WeakMap,iR=e=>{let t=ix.get(e.target);t&&t(e)},iT=e=>{e.forEach(iR)},i_={some:0,all:1};class iw extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:i_[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;iE.has(n)||iE.set(n,{});let r=iE.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iT,{root:e,...t})),r[i]}(t);return ix.set(e,n),r.observe(e),()=>{ix.delete(e),r.unobserve(e)}}(this.node.current,a,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iS=(0,n6.createContext)({strict:!1});var iA=n(32582);let iM=(0,n6.createContext)({});function ij(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iO(e){return!!(ij(e)||e.variants)}function iC(e){return Array.isArray(e)?e.join(" "):e}var iD=n(7044);let iL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ik={};for(let e in iL)ik[e]={isEnabled:t=>iL[e].some(e=>!!t[e])};let iN=Symbol.for("motionComponentSymbol");var iU=n(21279),iI=n(15124);function iV(e,{layout:t,layoutId:n}){return b.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ra[e]||"opacity"===e)}let iF=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iB={...q,transform:Math.round},iH={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:es,rotateX:es,rotateY:es,rotateZ:es,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:es,skewX:es,skewY:es,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:Y,originX:ed,originY:ed,originZ:eu,zIndex:iB,fillOpacity:Y,strokeOpacity:Y,numOctaves:iB},i$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iz=v.length;function iW(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,o=!1,s=!1;for(let e in t){let n=t[e];if(b.has(e)){o=!0;continue}if(z(e)){i[e]=n;continue}{let t=iF(n,iH[e]);e.startsWith("origin")?(s=!0,a[e]=t):r[e]=t}}if(!t.transform&&(o||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<iz;a++){let o=v[a],s=e[o];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===+!!o.startsWith("scale"):0===parseFloat(s))||n){let e=iF(s,iH[o]);if(!l){i=!1;let t=i$[o]||o;r+=`${t}(${e}) `}n&&(t[o]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}let iK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iX(e,t,n){for(let r in t)O(t[r])||iV(r,n)||(e[r]=t[r])}let iq={offset:"stroke-dashoffset",array:"stroke-dasharray"},iY={offset:"strokeDashoffset",array:"strokeDasharray"};function iG(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:a=1,pathOffset:o=0,...s},l,u,c){if(iW(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:d}=e;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==t&&(h.x=t),void 0!==n&&(h.y=n),void 0!==r&&(h.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let a=i?iq:iY;e[a.offset]=eu.transform(-r);let o=eu.transform(t),s=eu.transform(n);e[a.array]=`${o} ${s}`}(h,i,a,o,!1)}let iQ=()=>({...iK(),attrs:{}}),iZ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i3(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i5=n(72789);let i4=e=>(t,n)=>{let r=(0,n6.useContext)(iM),a=(0,n6.useContext)(iU.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,a){return{latestValues:function(e,t,n,r){let a={},s=r(e,{});for(let e in s)a[e]=rf(s[e]);let{initial:l,animate:u}=e,c=ij(e),h=iO(e);t&&h&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let d=!!n&&!1===n.initial,f=(d=d||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=o(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(n,r,a,e),renderState:t()}})(e,t,r,a);return n?s():(0,i5.M)(s)};function i9(e,t,n){let{style:r}=e,i={};for(let a in r)(O(r[a])||t.style&&O(t.style[a])||iV(a,e)||n?.getValue(a)?.liveStyle!==void 0)&&(i[a]=r[a]);return i}let i7={useVisualState:i4({scrapeMotionValuesFromProps:i9,createRenderState:iK})};function i6(e,t,n){let r=i9(e,t,n);for(let n in e)(O(e[n])||O(t[n]))&&(r[-1!==v.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i8={useVisualState:i4({scrapeMotionValuesFromProps:i6,createRenderState:iQ})},ae=e=>t=>t.test(e),at=[q,eu,el,es,eh,ec,{test:e=>"auto"===e,parse:e=>e}],an=e=>at.find(ae(e)),ar=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ai=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,aa=e=>/^0[^.\s]+$/u.test(e),ao=new Set(["brightness","contrast","saturate","opacity"]);function as(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Z)||[];if(!r)return e;let i=n.replace(r,""),a=+!!ao.has(t);return r!==n&&(a*=100),t+"("+a+i+")"}let al=/\b([a-z-]*)\(.*?\)/gu,au={...eR,getAnimatableNone:e=>{let t=e.match(al);return t?t.map(as).join(" "):e}},ac={...iH,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:au,WebkitFilter:au},ah=e=>ac[e];function ad(e,t){let n=ah(e);return n!==au&&(n=eR),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let af=new Set(["auto","none","0"]);class ap extends tk{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&K(r=r.trim())){let i=function e(t,n,r=1){H(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,a]=function(e){let t=ai.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let o=window.getComputedStyle(n).getPropertyValue(i);if(o){let e=o.trim();return ar(e)?parseFloat(e):e}return K(a)?e(a,n,r+1):a}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!P.has(n)||2!==e.length)return;let[r,i]=e,a=an(r),o=an(i);if(a!==o)if(t_(a)&&t_(o))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tA[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||aa(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!af.has(t)&&eb(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=ad(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tA[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,a=n[i];n[i]=tA[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let am=[...at,ep,eR],ag=e=>am.find(ae(e)),ay={current:null},av={current:!1},ab=new WeakMap,aP=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ax{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tk,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=_.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:s,renderState:l}=a;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!i,this.isControllingVariants=ij(t),this.isVariantNode=iO(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==s[e]&&O(t)&&t.set(s[e],!1)}}mount(e){this.current=e,ab.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),av.current||function(){if(av.current=!0,iD.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ay.current=e.matches;e.addListener(t),t()}else ay.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ay.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=b.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),a(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in ik){let t=ik[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nM()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<aP.length;t++){let n=aP[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],a=n[r];if(O(i))e.addValue(r,i);else if(O(a))e.addValue(r,M(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,M(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=M(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(ar(n)||aa(n))?n=parseFloat(n):!ag(n)&&eR.test(t)&&(n=ad(e,t)),this.setBaseTarget(e,O(n)?n.get():n)),O(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=o(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||O(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new R),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class aE extends ax{constructor(){super(...arguments),this.KeyframeResolver=ap}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;O(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function aR(e,{style:t,vars:n},r,i){for(let a in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(a,n[a])}class aT extends aE{constructor(){super(...arguments),this.type="html",this.renderInstance=aR}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tx(t):tR(e,t);{let n=window.getComputedStyle(e),r=(z(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nB(e,t)}build(e,t,n){iW(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i9(e,t,n)}}let a_=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class aw extends aE{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nM}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=ah(t);return e&&e.default||0}return t=a_.has(t)?t:D(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i6(e,t,n)}build(e,t,n){iG(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in aR(e,t,void 0,r),t.attrs)e.setAttribute(a_.has(n)?n:D(n),t.attrs[n])}mount(e){this.isSVGTag=iZ(e.tagName),super.mount(e)}}let aS=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tq={animation:{Feature:nc},exit:{Feature:nd},inView:{Feature:iw},tap:{Feature:iP},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n4},drag:{Feature:n3,ProjectionNode:ia,MeasureLayout:rs},layout:{ProjectionNode:ia,MeasureLayout:rs}},tY=(e,t)=>i3(e)?new aw(t):new aT(t,{allowProjection:e!==n6.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){function a(e,a){var o,s,l;let u,c={...(0,n6.useContext)(iA.Q),...e,layoutId:function({layoutId:e}){let t=(0,n6.useContext)(re.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=c,d=function(e){let{initial:t,animate:n}=function(e,t){if(ij(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n6.useContext)(iM));return(0,n6.useMemo)(()=>({initial:t,animate:n}),[iC(t),iC(n)])}(e),f=r(e,h);if(!h&&iD.B){s=0,l=0,(0,n6.useContext)(iS).strict;let e=function(e){let{drag:t,layout:n}=ik;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,d.visualElement=function(e,t,n,r,i){let{visualElement:a}=(0,n6.useContext)(iM),o=(0,n6.useContext)(iS),s=(0,n6.useContext)(iU.t),l=(0,n6.useContext)(iA.Q).reducedMotion,u=(0,n6.useRef)(null);r=r||o.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:a,props:n,presenceContext:s,blockInitialAnimation:!!s&&!1===s.initial,reducedMotionConfig:l}));let c=u.current,h=(0,n6.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:a,drag:o,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!o||s&&n$(s),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,h);let d=(0,n6.useRef)(!1);(0,n6.useInsertionEffect)(()=>{c&&d.current&&c.update(n,s)});let f=n[L],p=(0,n6.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iI.E)(()=>{c&&(d.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n7.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,n6.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(i,f,c,t,e.ProjectionNode)}return(0,n9.jsxs)(iM.Provider,{value:d,children:[u&&d.visualElement?(0,n9.jsx)(u,{visualElement:d.visualElement,...c}):null,n(i,e,(o=d.visualElement,(0,n6.useCallback)(e=>{e&&f.onMount&&f.onMount(e),o&&(e?o.mount(e):o.unmount()),a&&("function"==typeof a?a(e):n$(a)&&(a.current=e))},[o])),f,h,d.visualElement)]})}e&&function(e){for(let t in e)ik[t]={...ik[t],...e[t]}}(e),a.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let o=(0,n6.forwardRef)(a);return o[iN]=i,o}({...i3(e)?i8:i7,preloadedFeatures:tq,useRender:function(e=!1){return(t,n,r,{latestValues:i},a)=>{let o=(i3(t)?function(e,t,n,r){let i=(0,n6.useMemo)(()=>{let n=iQ();return iG(n,t,iZ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iX(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iX(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n6.useMemo)(()=>{let n=iK();return iW(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,a,t),s=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n6.Fragment?{...s,...o,ref:r}:{},{children:u}=n,c=(0,n6.useMemo)(()=>O(u)?u.get():u,[u]);return(0,n6.createElement)(t,{...l,children:c})}}(t),createVisualElement:tY,Component:e})}))},26415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,n){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(r),o=(n||{}).decode||e,s=0;s<a.length;s++){var l=a[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),h=l.substr(++u,l.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(h,o))}}return i},t.serialize=function(e,t,r){var a=r||{},o=a.encode||n;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,n=encodeURIComponent,r=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},26736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let r=n(2255);function i(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=n(57391),i=n(70642);function a(e,t){var n;let{url:a,tree:o}=t,s=(0,r.createHrefFromUrl)(a),l=o||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(n=(0,i.extractPathFromFlightRouterState)(l))?n:a.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let r=n(57391),i=n(86770),a=n(2030),o=n(25232),s=n(56928),l=n(59435),u=n(89752);function c(e,t){let{serverResponse:{flightData:n,canonicalUrl:c},navigatedAt:h}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof n)return(0,o.handleExternalUrl)(e,d,n,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of n){let{segmentPath:n,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...n],f,l,e.canonicalUrl);if(null===m)return e;if((0,a.isNavigatingToNewRootLayout)(f,m))return(0,o.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,r.createHrefFromUrl)(c):void 0;g&&(d.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(h,p,y,t),d.patchedTree=m,d.cache=y,p=y,f=m}return(0,l.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return o}});let r=n(40740)._(n(76715)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",o=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},30660:(e,t)=>{"use strict";function n(e){let t=5381;for(let n=0;n<e.length;n++)t=(t<<5)+t+e.charCodeAt(n)|0;return t>>>0}function r(e){return n(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{djb2Hash:function(){return n},hexHash:function(){return r}})},31658:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return f}});let r=n(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(n(78671)),a=n(6341),o=n(2015),s=n(30660),l=n(74722),u=n(12958),c=n(35499);function h(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let n="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(n=(0,s.djb2Hash)(t).toString(36).slice(0,6)),n}function d(e,t,n){let r=(0,l.normalizeAppPath)(e),s=(0,o.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),c=(0,a.interpolateDynamicPath)(r,t,s),{name:d,ext:f}=i.default.parse(n),p=h(i.default.posix.join(e,d)),m=p?`-${p}`:"";return(0,u.normalizePathSep)(i.default.join(c,`${d}${m}${f}`))}function f(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,n="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":n=h(e),!t.endsWith("/route")){let{dir:e,name:r,ext:a}=i.default.parse(t);t=i.default.posix.join(e,`${r}${n?`-${n}`:""}${a}`,"route")}return t}function p(e,t){let n=e.endsWith("/route"),r=n?e.slice(0,-6):e,i=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${i}`)+(n?"/route":"")}},32582:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});let r=(0,n(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},32708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let r=n(34400),i=n(41500),a=n(33123),o=n(83913);function s(e,t,n,s,l,u){let{segmentPath:c,seedData:h,tree:d,head:f}=s,p=t,m=n;for(let t=0;t<c.length;t+=2){let n=c[t],s=c[t+1],g=t===c.length-2,y=(0,a.createRouterCacheKey)(s),v=m.parallelRoutes.get(n);if(!v)continue;let b=p.parallelRoutes.get(n);b&&b!==v||(b=new Map(v),p.parallelRoutes.set(n,b));let P=v.get(y),x=b.get(y);if(g){if(h&&(!x||!x.lazyData||x===P)){let t=h[0],n=h[1],a=h[3];x={lazyData:null,rsc:u||t!==o.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:u&&P?new Map(P.parallelRoutes):new Map,navigatedAt:e},P&&u&&(0,r.invalidateCacheByRouterState)(x,P,d),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,x,P,d,h,f,l),b.set(y,x)}continue}x&&P&&(x===P&&(x={lazyData:x.lazyData,rsc:x.rsc,prefetchRsc:x.prefetchRsc,head:x.head,prefetchHead:x.prefetchHead,parallelRoutes:new Map(x.parallelRoutes),loading:x.loading},b.set(y,x)),p=x,m=P)}}function l(e,t,n,r,i){s(e,t,n,r,i,!0)}function u(e,t,n,r,i){s(e,t,n,r,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t,n){for(let i in n[1]){let a=n[1][i][0],o=(0,r.createRouterCacheKey)(a),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(o),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var i="",a=n+1;a<e.length;){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:i}),n=a;continue}if("("===r){var s=1,l="",a=n+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--s){a++;break}}else if("("===e[a]&&(s++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(s)throw TypeError("Unbalanced pattern at "+n);if(!l)throw TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:l}),n=a;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,a=void 0===r?"./":r,o="[^"+i(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",h=function(e){if(u<n.length&&n[u].type===e)return n[u++].value},d=function(e){var t=h(e);if(void 0!==t)return t;var r=n[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},f=function(){for(var e,t="";e=h("CHAR")||h("ESCAPED_CHAR");)t+=e;return t};u<n.length;){var p=h("CHAR"),m=h("NAME"),g=h("PATTERN");if(m||g){var y=p||"";-1===a.indexOf(y)&&(c+=y,y=""),c&&(s.push(c),c=""),s.push({name:m||l++,prefix:y,suffix:"",pattern:g||o,modifier:h("MODIFIER")||""});continue}var v=p||h("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(s.push(c),c=""),h("OPEN")){var y=f(),b=h("NAME")||"",P=h("PATTERN")||"",x=f();d("CLOSE"),s.push({name:b||(P?l++:""),pattern:b&&!P?o:P,prefix:y,suffix:x,modifier:h("MODIFIER")||""});continue}d("END")}return s}function n(e,t){void 0===t&&(t={});var n=a(t),r=t.encode,i=void 0===r?function(e){return e}:r,o=t.validate,s=void 0===o||o,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",n)});return function(t){for(var n="",r=0;r<e.length;r++){var a=e[r];if("string"==typeof a){n+=a;continue}var o=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(o)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===o.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var h=0;h<o.length;h++){var d=i(o[h],a);if(s&&!l[r].test(d))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');n+=a.prefix+d+a.suffix}continue}if("string"==typeof o||"number"==typeof o){var d=i(String(o),a);if(s&&!l[r].test(d))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');n+=a.prefix+d+a.suffix;continue}if(!u){var f=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+f)}}return n}}function r(e,t,n){void 0===n&&(n={});var r=n.decode,i=void 0===r?function(e){return e}:r;return function(n){var r=e.exec(n);if(!r)return!1;for(var a=r[0],o=r.index,s=Object.create(null),l=1;l<r.length;l++)!function(e){if(void 0!==r[e]){var n=t[e-1];"*"===n.modifier||"+"===n.modifier?s[n.name]=r[e].split(n.prefix+n.suffix).map(function(e){return i(e,n)}):s[n.name]=i(r[e],n)}}(l);return{path:a,index:o,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function o(e,t,n){void 0===n&&(n={});for(var r=n.strict,o=void 0!==r&&r,s=n.start,l=n.end,u=n.encode,c=void 0===u?function(e){return e}:u,h="["+i(n.endsWith||"")+"]|$",d="["+i(n.delimiter||"/#?")+"]",f=void 0===s||s?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)f+=i(c(m));else{var g=i(c(m.prefix)),y=i(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";f+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else f+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else f+="("+m.pattern+")"+m.modifier;else f+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)o||(f+=d+"?"),f+=n.endsWith?"(?="+h+")":"$";else{var b=e[e.length-1],P="string"==typeof b?d.indexOf(b[b.length-1])>-1:void 0===b;o||(f+="(?:"+d+"(?="+h+"))?"),P||(f+="(?="+d+"|"+h+")")}return new RegExp(f,a(n))}function s(t,n,r){if(t instanceof RegExp){if(!n)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)n.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,n,r).source}).join("|")+")",a(r)):o(e(t,r),n,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return n(e(t,r),r)},t.tokensToFunction=n,t.match=function(e,t){var n=[];return r(s(e,n,t),n,t)},t.regexpToFunction=r,t.tokensToRegexp=o,t.pathToRegexp=s})(),e.exports=t})()},35416:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return s}});let r=n(95796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=r.HTML_LIMITED_BOT_UA_RE.source;function o(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||o(e)}function l(e){return i.test(e)?"dom":o(e)?"html":void 0}},35429:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return M}});let r=n(11264),i=n(11448),a=n(91563),o=n(59154),s=n(6361),l=n(57391),u=n(25232),c=n(86770),h=n(2030),d=n(59435),f=n(41500),p=n(89752),m=n(68214),g=n(96493),y=n(22308),v=n(74007),b=n(36875),P=n(97860),x=n(5334),E=n(25942),R=n(26736),T=n(24642);n(50593);let{createFromFetch:_,createTemporaryReferenceSet:w,encodeReply:S}=n(19357);async function A(e,t,n){let o,l,{actionId:u,actionArgs:c}=n,h=w(),d=(0,T.extractInfoFromServerReferenceId)(u),f="use-cache"===d.type?(0,T.omitUnusedArgs)(c,d):c,p=await S(f,{temporaryReferences:h}),m=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:u,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[a.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":o=P.RedirectType.push;break;case"replace":o=P.RedirectType.replace;break;default:o=void 0}let x=!!m.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let E=y?(0,s.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,R=m.headers.get("content-type");if(null==R?void 0:R.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await _(Promise.resolve(m),{callServer:r.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:h});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:o,revalidatedParts:l,isPrerender:x}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:o,revalidatedParts:l,isPrerender:x}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===R?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:E,redirectType:o,revalidatedParts:l,isPrerender:x}}function M(e,t){let{resolve:n,reject:r}=t,i={},a=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return A(e,s,t).then(async m=>{let T,{actionResult:_,actionFlightData:w,redirectLocation:S,redirectType:A,isPrerender:M,revalidatedParts:j}=m;if(S&&(A===P.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=T=(0,l.createHrefFromUrl)(S,!1)),!w)return(n(_),S)?(0,u.handleExternalUrl)(e,i,S.href,e.pushRef.pendingPush):e;if("string"==typeof w)return n(_),(0,u.handleExternalUrl)(e,i,w,e.pushRef.pendingPush);let O=j.paths.length>0||j.tag||j.cookie;for(let r of w){let{tree:o,seedData:l,head:d,isRootRender:m}=r;if(!m)return console.log("SERVER ACTION APPLY FAILED"),n(_),e;let b=(0,c.applyRouterStatePatchToTree)([""],a,o,T||e.canonicalUrl);if(null===b)return n(_),(0,g.handleSegmentMismatch)(e,t,o);if((0,h.isNavigatingToNewRootLayout)(a,b))return n(_),(0,u.handleExternalUrl)(e,i,T||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],n=(0,p.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(v,n,void 0,o,l,d,void 0),i.cache=n,i.prefetchCache=new Map,O&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,a=b}return S&&T?(O||((0,x.createSeededPrefetchCacheEntry)({url:S,data:{flightData:w,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:M?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,R.hasBasePath)(T)?(0,E.removeBasePath)(T):T,A||P.RedirectType.push))):n(_),(0,d.handleMutable)(e,i)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41500:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,a,o,s,l,u){if(0===Object.keys(o[1]).length){n.head=l;return}for(let c in o[1]){let h,d=o[1][c],f=d[0],p=(0,r.createRouterCacheKey)(f),m=null!==s&&void 0!==s[2][c]?s[2][c]:null;if(a){let r=a.parallelRoutes.get(c);if(r){let a,o=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,s=new Map(r),h=s.get(p);a=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),navigatedAt:t}:o&&h?{lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes),loading:h.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),loading:null,navigatedAt:t},s.set(p,a),e(t,a,h,d,m||null,l,u),n.parallelRoutes.set(c,s);continue}}if(null!==m){let e=m[1],n=m[3];h={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else h={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=n.parallelRoutes.get(c);g?g.set(p,h):n.parallelRoutes.set(c,new Map([[p,h]])),e(t,h,void 0,d,m,l,u)}}}});let r=n(33123),i=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42785:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},44397:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t){return function e(t,n,i){if(0===Object.keys(n).length)return[t,i];if(n.children){let[a,o]=n.children,s=t.parallelRoutes.get("children");if(s){let t=(0,r.createRouterCacheKey)(a),n=s.get(t);if(n){let r=e(n,o,i+"/"+t);if(r)return r}}}for(let a in n){if("children"===a)continue;let[o,s]=n[a],l=t.parallelRoutes.get(a);if(!l)continue;let u=(0,r.createRouterCacheKey)(o),c=l.get(u);if(!c)continue;let h=e(c,s,i+"/"+u);if(h)return h}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return h},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return o},navigate:function(){return i},prefetch:function(){return r},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return s}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,i=n,a=n,o=n,s=n,l=n,u=n,c=n;var h=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,n)=>{"use strict";function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53038:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(43210);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=a(e,r)),t&&(i.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function i(e){return n.test(e)?e.replace(r,"\\$&"):e}},54674:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let r=n(84949),i=n(19169),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:a}=(0,i.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let r=n(41500),i=n(33898);function a(e,t,n,a,o){let{tree:s,seedData:l,head:u,isRootRender:c}=a;if(null===l)return!1;if(c){let i=l[1];n.loading=l[3],n.rsc=i,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,s,l,u,o)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,n,t,a,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let r=n(70642);function i(e){return void 0!==e}function a(e,t){var n,a;let o=null==(n=t.shouldScroll)||n,s=e.nextUrl;if(i(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?s=n:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,n)=>{"use strict";n.r(t),n.d(t,{_:()=>i});var r=0;function i(e){return"__private_"+r+++"_"+e}},61794:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(79289),i=n(26736);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},63690:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return b}});let r=n(59154),i=n(8830),a=n(43210),o=n(91992);n(50593);let s=n(19129),l=n(96127),u=n(89752),c=n(75076),h=n(73406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?f({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function f(e){let{actionQueue:t,action:n,setState:r}=e,i=t.state;t.pending=n;let a=n.payload,s=t.action(i,a);function l(e){n.discarded||(t.state=e,d(t,r),n.resolve(e))}(0,o.isThenable)(s)?s.then(l,e=>{d(t,r),n.reject(e)}):l(s)}function p(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let i={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let o={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=o,f({actionQueue:e,action:o,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),f({actionQueue:e,action:o,setState:n})):(null!==e.last&&(e.last.next=o),e.last=o)})(n,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function m(){return null}function g(){return null}function y(e,t,n,i){let a=new URL((0,l.addBasePath)(e),location.href);(0,h.setLinkForCurrentNavigation)(i);(0,s.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,u.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,s.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var a;(0,c.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:i,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[a,o]=n,[s,l]=t;return(0,i.matchSegment)(s,a)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),o[l]):!!Array.isArray(s)}}});let r=n(74007),i=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],i=t.parallelRoutes,o=new Map(i);for(let t in r){let n=r[t],s=n[0],l=(0,a.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let r=u.get(l);if(void 0!==r){let i=e(r,n),a=new Map(u);a.set(l,i),o.set(t,a)}}}let s=t.rsc,l=y(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o,navigatedAt:t.navigatedAt}}}});let r=n(83913),i=n(14077),a=n(33123),o=n(2030),s=n(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,n,o,s,u,d,f,p){return function e(t,n,o,s,u,d,f,p,m,g,y){let v=o[1],b=s[1],P=null!==d?d[2]:null;u||!0===s[4]&&(u=!0);let x=n.parallelRoutes,E=new Map(x),R={},T=null,_=!1,w={};for(let n in b){let o,s=b[n],h=v[n],d=x.get(n),S=null!==P?P[n]:null,A=s[0],M=g.concat([n,A]),j=(0,a.createRouterCacheKey)(A),O=void 0!==h?h[0]:void 0,C=void 0!==d?d.get(j):void 0;if(null!==(o=A===r.DEFAULT_SEGMENT_KEY?void 0!==h?{route:h,node:null,dynamicRequestTree:null,children:null}:c(t,h,s,C,u,void 0!==S?S:null,f,p,M,y):m&&0===Object.keys(s[1]).length?c(t,h,s,C,u,void 0!==S?S:null,f,p,M,y):void 0!==h&&void 0!==O&&(0,i.matchSegment)(A,O)&&void 0!==C&&void 0!==h?e(t,C,h,s,u,S,f,p,m,M,y):c(t,h,s,C,u,void 0!==S?S:null,f,p,M,y))){if(null===o.route)return l;null===T&&(T=new Map),T.set(n,o);let e=o.node;if(null!==e){let t=new Map(d);t.set(j,e),E.set(n,t)}let t=o.route;R[n]=t;let r=o.dynamicRequestTree;null!==r?(_=!0,w[n]=r):w[n]=t}else R[n]=s,w[n]=s}if(null===T)return null;let S={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:E,navigatedAt:t};return{route:h(s,R),node:S,dynamicRequestTree:_?h(s,w):null,children:T}}(e,t,n,o,!1,s,u,d,f,[],p)}function c(e,t,n,r,i,u,c,f,p,m){return!i&&(void 0===t||(0,o.isNavigatingToNewRootLayout)(t,n))?l:function e(t,n,r,i,o,l,u,c){let f,p,m,g,y=n[1],v=0===Object.keys(y).length;if(void 0!==r&&r.navigatedAt+s.DYNAMIC_STALETIME_MS>t)f=r.rsc,p=r.loading,m=r.head,g=r.navigatedAt;else if(null===i)return d(t,n,null,o,l,u,c);else if(f=i[1],p=i[3],m=v?o:null,g=t,i[4]||l&&v)return d(t,n,i,o,l,u,c);let b=null!==i?i[2]:null,P=new Map,x=void 0!==r?r.parallelRoutes:null,E=new Map(x),R={},T=!1;if(v)c.push(u);else for(let n in y){let r=y[n],i=null!==b?b[n]:null,s=null!==x?x.get(n):void 0,h=r[0],d=u.concat([n,h]),f=(0,a.createRouterCacheKey)(h),p=e(t,r,void 0!==s?s.get(f):void 0,i,o,l,d,c);P.set(n,p);let m=p.dynamicRequestTree;null!==m?(T=!0,R[n]=m):R[n]=r;let g=p.node;if(null!==g){let e=new Map;e.set(f,g),E.set(n,e)}}return{route:n,node:{lazyData:null,rsc:f,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:E,navigatedAt:g},dynamicRequestTree:T?h(n,R):null,children:P}}(e,n,r,u,c,f,p,m)}function h(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function d(e,t,n,r,i,o,s){let l=h(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,n,r,i,o,s,l){let u=n[1],c=null!==r?r[2]:null,h=new Map;for(let n in u){let r=u[n],d=null!==c?c[n]:null,f=r[0],p=s.concat([n,f]),m=(0,a.createRouterCacheKey)(f),g=e(t,r,void 0===d?null:d,i,o,p,l),y=new Map;y.set(m,g),h.set(n,y)}let d=0===h.size;d&&l.push(s);let f=null!==r?r[1]:null,p=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:h,prefetchRsc:void 0!==f?f:null,prefetchHead:d?i:[null,null],loading:void 0!==p?p:null,rsc:v(),head:d?v():null,navigatedAt:t}}(e,t,n,r,i,o,s),dynamicRequestTree:l,children:null}}function f(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:o,head:s}=t;o&&function(e,t,n,r,o){let s=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],a=s.children;if(null!==a){let e=a.get(n);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(r,t)){s=e;continue}}}return}!function e(t,n,r,o){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,n,r,o,s){let l=n[1],u=r[1],c=o[2],h=t.parallelRoutes;for(let t in l){let n=l[t],r=u[t],o=c[t],d=h.get(t),f=n[0],p=(0,a.createRouterCacheKey)(f),g=void 0!==d?d.get(p):void 0;void 0!==g&&(void 0!==r&&(0,i.matchSegment)(f,r[0])&&null!=o?e(g,n,r,o,s):m(n,g,null))}let d=t.rsc,f=o[1];null===d?t.rsc=f:y(d)&&d.resolve(f);let p=t.head;y(p)&&p.resolve(s)}(l,t.route,n,r,o),t.dynamicRequestTree=null);return}let u=n[1],c=r[2];for(let t in n){let n=u[t],r=c[t],a=s.get(t);if(void 0!==a){let t=a.route[0];if((0,i.matchSegment)(n[0],t)&&null!=r)return e(a,n,r,o)}}}(s,n,r,o)}(e,n,r,o,s)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)m(e.route,n,t);else for(let e of r.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,n){let r=e[1],i=t.parallelRoutes;for(let e in r){let t=r[e],o=i.get(e);if(void 0===o)continue;let s=t[0],l=(0,a.createRouterCacheKey)(s),u=o.get(l);void 0!==u&&m(t,u,n)}let o=t.rsc;y(o)&&(null===n?o.resolve(null):o.reject(n));let s=t.head;y(s)&&s.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=g,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70554:(e,t)=>{"use strict";function n(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return n}})},70642:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],a=Array.isArray(t),o=a?t[1]:t;!o||o.startsWith(i.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):a&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),i=n(83913),a=n(14077),o=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=o(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===i.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(i.PAGE_SEGMENT_KEY))return"";let a=[s(n)],o=null!=(t=e[1])?t:{},c=o.children?u(o.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let n=u(t);void 0!==n&&a.push(n)}return l(a)}function c(e,t){let n=function e(t,n){let[i,o]=t,[l,c]=n,h=s(i),d=s(l);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>h.startsWith(e)||d.startsWith(e)))return"";if(!(0,a.matchSegment)(i,l)){var f;return null!=(f=u(n))?f:""}for(let t in o)if(c[t]){let n=e(o[t],c[t]);if(null!==n)return s(l)+"/"+n}return null}(e,t);return null==n||"/"===n?n:l(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return a}});let r=n(74722),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function o(e){let t,n,a;for(let r of e.split("/"))if(n=i.find(e=>r.startsWith(e))){[t,a]=e.split(n,2);break}if(!t||!n||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),n){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=o.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},72789:(e,t,n)=>{"use strict";n.d(t,{M:()=>i});var r=n(43210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},73406:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return P},onNavigationIntent:function(){return x},pingVisibleLinks:function(){return R},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return h},unmountPrefetchableInstance:function(){return b}}),n(63690);let r=n(89752),i=n(59154),a=n(50593),o=n(43210),s=null,l={pending:!0},u={pending:!1};function c(e){(0,o.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),s=e})}function h(e){s===e&&(s=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,f=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;P(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==d.get(e)&&b(e),d.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,n,r,i,a){if(i){let i=g(t);if(null!==i){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:a};return m(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,n,r){let i=g(t);null!==i&&m(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function b(e){let t=d.get(e);if(void 0!==t){d.delete(e),f.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==p&&p.unobserve(e)}function P(e,t){let n=d.get(e);void 0!==n&&(n.isVisible=t,t?f.add(n):f.delete(n),E(n))}function x(e,t){let n=d.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,E(n))}function E(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function R(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of f){let o=r.prefetchTask;if(null!==o&&r.cacheVersion===n&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,a.cancelPrefetchTask)(o);let s=(0,a.createCacheKey)(r.prefetchHref,e),l=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(s,t,r.kind===i.PrefetchKind.FULL,l),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74479:(e,t,n)=>{"use strict";function r(e){return"object"==typeof e&&null!==e}n.d(t,{G:()=>r})},74722:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return o}});let r=n(85531),i=n(35499);function a(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,n,r)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&n===r.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},75076:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return o}});let r=n(5144),i=n(5334),a=new r.PromiseQueue(5),o=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},76759:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let r=n(42785),i=n(23736);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},77022:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let r=n(43210),i=n(51215),a="next-route-announcer";function o(e){let{tree:t}=e,[n,o]=(0,r.useState)(null);(0,r.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,r.useState)(""),u=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),n?(0,i.createPortal)(s,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78034:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let r=n(44827);function i(e){let{re:t,groups:n}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[e,t]of Object.entries(n)){let n=i[t.pos];void 0!==n&&(t.repeat?o[e]=n.split("/").map(e=>a(e)):o[e]=a(n))}return o}}},78866:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let r=n(59008),i=n(57391),a=n(86770),o=n(2030),s=n(25232),l=n(59435),u=n(41500),c=n(89752),h=n(96493),d=n(68214),f=n(22308);function p(e,t){let{origin:n}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,r.fetchServerResponse)(new URL(m,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return y.lazyData.then(async n=>{let{flightData:r,canonicalUrl:c}=n;if("string"==typeof r)return(0,s.handleExternalUrl)(e,p,r,e.pushRef.pendingPush);for(let n of(y.lazyData=null,r)){let{tree:r,seedData:l,head:d,isRootRender:P}=n;if(!P)return console.log("REFRESH FAILED"),e;let x=(0,a.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===x)return(0,h.handleSegmentMismatch)(e,t,r);if((0,o.isNavigatingToNewRootLayout)(g,x))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let E=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=E),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(b,y,void 0,r,l,d,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:x,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=x,g=x}return(0,l.handleMutable)(e,p)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},81836:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(43210);let i=r.forwardRef(function({title:e,titleId:t,...n},i){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},84949:(e,t)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85531:(e,t)=>{"use strict";function n(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(40740),i=n(60687),a=r._(n(43210)),o=n(30195),s=n(22142),l=n(59154),u=n(53038),c=n(79289),h=n(96127);n(50148);let d=n(73406),f=n(61794),p=n(63690);function m(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function g(e){let t,n,r,[o,g]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:b,as:P,children:x,prefetch:E=null,passHref:R,replace:T,shallow:_,scroll:w,onClick:S,onMouseEnter:A,onTouchStart:M,legacyBehavior:j=!1,onNavigate:O,ref:C,unstable_dynamicOnHover:D,...L}=e;t=x,j&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let k=a.default.useContext(s.AppRouterContext),N=!1!==E,U=null===E?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:V}=a.default.useMemo(()=>{let e=m(b);return{href:e,as:P?m(P):e}},[b,P]);j&&(n=a.default.Children.only(t));let F=j?n&&"object"==typeof n&&n.ref:C,B=a.default.useCallback(e=>(null!==k&&(v.current=(0,d.mountLinkInstance)(e,I,k,U,N,g)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(e)}),[N,I,k,U,g]),H={ref:(0,u.useMergedRef)(B,F),onClick(e){j||"function"!=typeof S||S(e),j&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),k&&(e.defaultPrevented||function(e,t,n,r,i,o,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==o||o,r.current)})}}(e,I,V,v,T,w,O))},onMouseEnter(e){j||"function"!=typeof A||A(e),j&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),k&&N&&(0,d.onNavigationIntent)(e.currentTarget,!0===D)},onTouchStart:function(e){j||"function"!=typeof M||M(e),j&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),k&&N&&(0,d.onNavigationIntent)(e.currentTarget,!0===D)}};return(0,c.isAbsoluteUrl)(V)?H.href=V:j&&!R&&("a"!==n.type||"href"in n.props)||(H.href=(0,h.addBasePath)(V)),r=j?a.default.cloneElement(n,H):(0,i.jsx)("a",{...L,...H,children:t}),(0,i.jsx)(y.Provider,{value:o,children:r})}n(32708);let y=(0,a.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86044:(e,t,n)=>{"use strict";n.d(t,{xQ:()=>a});var r=n(43210),i=n(21279);function a(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:o,register:s}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return s(l)},[e]);let u=(0,r.useCallback)(()=>e&&o&&o(l),[l,o,e]);return!n&&o?[!1,u]:[!0]}},86770:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,l){let u,[c,h,d,f,p]=n;if(1===t.length){let e=s(n,r);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,a.matchSegment)(m,c))return null;if(2===t.length)u=s(h[g],r);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),h[g],r,l)))return null;let y=[t[0],{...h,[g]:u},d,f];return p&&(y[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let r=n(83913),i=n(74007),a=n(14077),o=n(22308);function s(e,t){let[n,i]=e,[o,l]=t;if(o===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(n,o)){let t={};for(let e in i)void 0!==l[e]?t[e]=s(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88212:(e,t,n)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=n(26415);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},88920:(e,t,n)=>{"use strict";n.d(t,{N:()=>v});var r=n(60687),i=n(43210),a=n(12157),o=n(72789),s=n(15124),l=n(21279),u=n(18171),c=n(32582);class h extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:e,isPresent:t,anchorX:n}){let a=(0,i.useId)(),o=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:u,right:c}=s.current;if(t||!o.current||!e||!r)return;let h="left"===n?`left: ${u}`:`right: ${c}`;o.current.dataset.motionPopId=a;let d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${a}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${h}px !important;
            top: ${i}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[t]),(0,r.jsx)(h,{isPresent:t,childRef:o,sizeRef:s,children:i.cloneElement(e,{ref:o})})}let f=({children:e,initial:t,isPresent:n,onExitComplete:a,custom:s,presenceAffectsLayout:u,mode:c,anchorX:h})=>{let f=(0,o.M)(p),m=(0,i.useId)(),g=!0,y=(0,i.useMemo)(()=>(g=!1,{id:m,initial:t,isPresent:n,custom:s,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;a&&a()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[n,f,a]);return u&&g&&(y={...y}),(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[n]),i.useEffect(()=>{n||f.size||!a||a()},[n]),"popLayout"===c&&(e=(0,r.jsx)(d,{isPresent:n,anchorX:h,children:e})),(0,r.jsx)(l.t.Provider,{value:y,children:e})};function p(){return new Map}var m=n(86044);let g=e=>e.key||"";function y(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:n=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:h=!1,anchorX:d="left"})=>{let[p,v]=(0,m.xQ)(h),b=(0,i.useMemo)(()=>y(e),[e]),P=h&&!p?[]:b.map(g),x=(0,i.useRef)(!0),E=(0,i.useRef)(b),R=(0,o.M)(()=>new Map),[T,_]=(0,i.useState)(b),[w,S]=(0,i.useState)(b);(0,s.E)(()=>{x.current=!1,E.current=b;for(let e=0;e<w.length;e++){let t=g(w[e]);P.includes(t)?R.delete(t):!0!==R.get(t)&&R.set(t,!1)}},[w,P.length,P.join("-")]);let A=[];if(b!==T){let e=[...b];for(let t=0;t<w.length;t++){let n=w[t],r=g(n);P.includes(r)||(e.splice(t,0,n),A.push(n))}return"wait"===c&&A.length&&(e=A),S(y(e)),_(b),null}let{forceRender:M}=(0,i.useContext)(a.L);return(0,r.jsx)(r.Fragment,{children:w.map(e=>{let i=g(e),a=(!h||!!p)&&(b===w||P.includes(i));return(0,r.jsx)(f,{isPresent:a,initial:(!x.current||!!n)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,onExitComplete:a?void 0:()=>{if(!R.has(i))return;R.set(i,!0);let e=!0;R.forEach(t=>{t||(e=!1)}),e&&(M?.(),S(E.current),h&&v?.(),l&&l())},anchorX:d,children:e},i)})})}},89752:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return M},createPrefetchURL:function(){return S},default:function(){return D},isExternalURL:function(){return w}});let r=n(40740),i=n(60687),a=r._(n(43210)),o=n(22142),s=n(59154),l=n(57391),u=n(10449),c=n(19129),h=r._(n(35656)),d=n(35416),f=n(96127),p=n(77022),m=n(67086),g=n(44397),y=n(89330),v=n(25942),b=n(26736),P=n(70642),x=n(12776),E=n(63690),R=n(36875),T=n(97860);n(73406);let _={};function w(e){return e.origin!==window.location.origin}function S(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return w(t)?null:t}function A(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(i,"",r)):window.history.replaceState(i,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function j(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function O(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,i=null!==r?r:n;return(0,a.useDeferredValue)(n,i)}function C(e){let t,{actionQueue:n,assetPrefix:r,globalError:l}=e,d=(0,c.useActionQueue)(n),{canonicalUrl:f}=d,{searchParams:x,pathname:w}=(0,a.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[f]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(_.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let n=(0,R.getURLFromRedirectError)(t);(0,R.getRedirectTypeFromError)(t)===T.RedirectType.push?E.publicAppRouterInstance.push(n,{}):E.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:S}=d;if(S.mpaNavigation){if(_.pendingMpaPath!==f){let e=window.location;S.pendingPush?e.assign(f):e.replace(f),_.pendingMpaPath=f}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=j(t),i&&n(i)),e(t,r,i)},window.history.replaceState=function(e,r,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=j(e),i&&n(i)),t(e,r,i)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,E.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:M,tree:C,nextUrl:D,focusAndScrollRef:L}=d,k=(0,a.useMemo)(()=>(0,g.findHeadInCache)(M,C[1]),[M,C]),U=(0,a.useMemo)(()=>(0,P.getSelectedParams)(C),[C]),I=(0,a.useMemo)(()=>({parentTree:C,parentCacheNode:M,parentSegmentPath:null,url:f}),[C,M,f]),V=(0,a.useMemo)(()=>({tree:C,focusAndScrollRef:L,nextUrl:D}),[C,L,D]);if(null!==k){let[e,n]=k;t=(0,i.jsx)(O,{headCacheNode:e},n)}else t=null;let F=(0,i.jsxs)(m.RedirectBoundary,{children:[t,M.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:C})]});return F=(0,i.jsx)(h.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:F}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(A,{appRouterState:d}),(0,i.jsx)(N,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:U,children:(0,i.jsx)(u.PathnameContext.Provider,{value:w,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:x,children:(0,i.jsx)(o.GlobalLayoutRouterContext.Provider,{value:V,children:(0,i.jsx)(o.AppRouterContext.Provider,{value:E.publicAppRouterInstance,children:(0,i.jsx)(o.LayoutRouterContext.Provider,{value:I,children:F})})})})})})]})}function D(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,x.useNavFailureHandler)(),(0,i.jsx)(h.ErrorBoundary,{errorComponent:h.default,children:(0,i.jsx)(C,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let L=new Set,k=new Set;function N(){let[,e]=a.default.useState(0),t=L.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return k.add(n),t!==L.size&&n(),()=>{k.delete(n)}},[t,e]),[...L].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&k.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let r=n(98834),i=n(54674);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let r=n(25232);function i(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let o=a.length<=2,[s,l]=a,u=(0,i.createRouterCacheKey)(l),c=n.parallelRoutes.get(s),h=t.parallelRoutes.get(s);h&&h!==c||(h=new Map(c),t.parallelRoutes.set(s,h));let d=null==c?void 0:c.get(u),f=h.get(u);if(o){f&&f.lazyData&&f!==d||h.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!f||!d){f||h.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},h.set(u,f)),e(f,d,(0,r.getNextFlightSegmentPath)(a))}}});let r=n(74007),i=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:i,hash:a}=(0,r.parsePath)(e);return""+t+n+i+a}}};