{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credentials<PERSON>rovider from 'next-auth/providers/credentials'\nimport GoogleProvider from 'next-auth/providers/google'\nimport GitHubProvider from 'next-auth/providers/github'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    // Credentials provider for email/password login\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.password) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: `${user.firstName} ${user.lastName}`,\n          role: user.role,\n          image: user.imageUrl,\n        }\n      }\n    }),\n\n    // Google OAuth provider\n    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [\n      GoogleProvider({\n        clientId: process.env.GOOGLE_CLIENT_ID,\n        clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n        profile(profile) {\n          return {\n            id: profile.sub,\n            name: profile.name,\n            email: profile.email,\n            image: profile.picture,\n            role: 'USER', // Default role for OAuth users\n          }\n        },\n      })\n    ] : []),\n\n    // GitHub OAuth provider\n    ...(process.env.GITHUB_ID && process.env.GITHUB_SECRET ? [\n      GitHubProvider({\n        clientId: process.env.GITHUB_ID,\n        clientSecret: process.env.GITHUB_SECRET,\n        profile(profile) {\n          return {\n            id: profile.id.toString(),\n            name: profile.name || profile.login,\n            email: profile.email || '',\n            image: profile.avatar_url,\n            role: 'USER', // Default role for OAuth users\n          }\n        },\n      })\n    ] : []),\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signOut: '/auth/signout',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n\n// Helper function to get server session\nexport async function getServerSession() {\n  const { getServerSession } = await import('next-auth/next')\n  return getServerSession(authOptions)\n}\n\n// Helper function to require authentication\nexport async function requireAuth() {\n  const session = await getServerSession()\n  if (!session) {\n    throw new Error('Authentication required')\n  }\n  return session\n}\n\n// Helper function to require admin role\nexport async function requireAdmin() {\n  const session = await requireAuth()\n  if (session.user.role !== 'ADMIN') {\n    throw new Error('Admin access required')\n  }\n  return session\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,gDAAgD;QAChD,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC1C,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,QAAQ;gBACtB;YACF;QACF;QAEA,wBAAwB;WACpB,QAAQ,GAAG,CAAC,gBAAgB,IAAI,QAAQ,GAAG,CAAC,oBAAoB,GAAG;YACrE,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;gBACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;gBACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;gBAC9C,SAAQ,OAAO;oBACb,OAAO;wBACL,IAAI,QAAQ,GAAG;wBACf,MAAM,QAAQ,IAAI;wBAClB,OAAO,QAAQ,KAAK;wBACpB,OAAO,QAAQ,OAAO;wBACtB,MAAM;oBACR;gBACF;YACF;SACD,GAAG,EAAE;QAEN,wBAAwB;WACpB,QAAQ,GAAG,CAAC,SAAS,IAAI,QAAQ,GAAG,CAAC,aAAa,GAAG;YACvD,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;gBACb,UAAU,QAAQ,GAAG,CAAC,SAAS;gBAC/B,cAAc,QAAQ,GAAG,CAAC,aAAa;gBACvC,SAAQ,OAAO;oBACb,OAAO;wBACL,IAAI,QAAQ,EAAE,CAAC,QAAQ;wBACvB,MAAM,QAAQ,IAAI,IAAI,QAAQ,KAAK;wBACnC,OAAO,QAAQ,KAAK,IAAI;wBACxB,OAAO,QAAQ,UAAU;wBACzB,MAAM;oBACR;gBACF;YACF;SACD,GAAG,EAAE;KACP;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,eAAe;IACpB,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,OAAO,iBAAiB;AAC1B;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QACjC,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}