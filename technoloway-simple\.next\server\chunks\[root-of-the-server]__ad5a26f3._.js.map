{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/lib/api-utils.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { ZodError, ZodSchema } from 'zod'\nimport { Prisma } from '@prisma/client'\n\n// API Response types\nexport interface ApiResponse<T = any> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n}\n\nexport interface PaginatedResponse<T = any> extends ApiResponse<T> {\n  pagination?: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\n// Error handling\nexport class ApiError extends Error {\n  constructor(\n    public message: string,\n    public statusCode: number = 500,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\n// Success response helper\nexport function successResponse<T>(\n  data: T,\n  message?: string,\n  statusCode: number = 200\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json(\n    {\n      success: true,\n      data,\n      message,\n    },\n    { status: statusCode }\n  )\n}\n\n// Error response helper\nexport function errorResponse(\n  error: string | Error,\n  statusCode: number = 500,\n  code?: string\n): NextResponse<ApiResponse> {\n  const message = error instanceof Error ? error.message : error\n  \n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n      code,\n    },\n    { status: statusCode }\n  )\n}\n\n// Paginated response helper\nexport function paginatedResponse<T>(\n  data: T[],\n  page: number,\n  limit: number,\n  total: number,\n  message?: string\n): NextResponse<PaginatedResponse<T[]>> {\n  const totalPages = Math.ceil(total / limit)\n  \n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination: {\n      page,\n      limit,\n      total,\n      totalPages,\n    },\n  })\n}\n\n// Validation middleware\nexport function validateRequest<T>(schema: ZodSchema<T>) {\n  return async (request: NextRequest): Promise<T> => {\n    try {\n      const body = await request.json()\n      return schema.parse(body)\n    } catch (error) {\n      if (error instanceof ZodError) {\n        const errorMessages = error.errors.map(err => \n          `${err.path.join('.')}: ${err.message}`\n        ).join(', ')\n        throw new ApiError(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR')\n      }\n      throw new ApiError('Invalid request body', 400, 'INVALID_BODY')\n    }\n  }\n}\n\n// Query parameter helpers\nexport function getQueryParams(request: NextRequest) {\n  const { searchParams } = new URL(request.url)\n  \n  return {\n    page: parseInt(searchParams.get('page') || '1'),\n    limit: Math.min(parseInt(searchParams.get('limit') || '10'), 100), // Max 100 items per page\n    search: searchParams.get('search') || undefined,\n    sortBy: searchParams.get('sortBy') || undefined,\n    sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',\n    filter: searchParams.get('filter') || undefined,\n  }\n}\n\n// Pagination helpers\nexport function getPaginationParams(page: number, limit: number) {\n  const skip = (page - 1) * limit\n  return { skip, take: limit }\n}\n\n// Error handler wrapper for API routes\nexport function withErrorHandler(\n  handler: (request: NextRequest, context?: any) => Promise<NextResponse>\n) {\n  return async (request: NextRequest, context?: any): Promise<NextResponse> => {\n    try {\n      return await handler(request, context)\n    } catch (error) {\n      console.error('API Error:', error)\n      \n      if (error instanceof ApiError) {\n        return errorResponse(error.message, error.statusCode, error.code)\n      }\n      \n      if (error instanceof ZodError) {\n        const errorMessages = error.errors.map(err => \n          `${err.path.join('.')}: ${err.message}`\n        ).join(', ')\n        return errorResponse(`Validation error: ${errorMessages}`, 400, 'VALIDATION_ERROR')\n      }\n      \n      if (error instanceof Prisma.PrismaClientKnownRequestError) {\n        switch (error.code) {\n          case 'P2002':\n            return errorResponse('A record with this data already exists', 409, 'DUPLICATE_RECORD')\n          case 'P2025':\n            return errorResponse('Record not found', 404, 'NOT_FOUND')\n          case 'P2003':\n            return errorResponse('Foreign key constraint failed', 400, 'FOREIGN_KEY_ERROR')\n          default:\n            return errorResponse('Database error occurred', 500, 'DATABASE_ERROR')\n        }\n      }\n      \n      return errorResponse('Internal server error', 500, 'INTERNAL_ERROR')\n    }\n  }\n}\n\n// Method validation helper\nexport function validateMethod(request: NextRequest, allowedMethods: string[]) {\n  if (!allowedMethods.includes(request.method)) {\n    throw new ApiError(`Method ${request.method} not allowed`, 405, 'METHOD_NOT_ALLOWED')\n  }\n}\n\n// Authentication helper (placeholder - implement based on your auth strategy)\nexport async function requireAuth(request: NextRequest) {\n  // TODO: Implement authentication logic\n  // This could use NextAuth, JWT tokens, or your preferred auth method\n  const authHeader = request.headers.get('authorization')\n\n  if (!authHeader) {\n    throw new ApiError('Authentication required', 401, 'UNAUTHORIZED')\n  }\n\n  // For now, return a mock admin user - replace with actual auth logic\n  return {\n    id: 'admin-user-id',\n    email: '<EMAIL>',\n    role: 'ADMIN' as const,\n  }\n}\n\n// Admin authorization helper\nexport async function requireAdmin(request: NextRequest) {\n  const user = await requireAuth(request)\n  \n  if (user.role !== 'ADMIN') {\n    throw new ApiError('Admin access required', 403, 'FORBIDDEN')\n  }\n  \n  return user\n}\n\n// Slug generation helper\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim()\n}\n\n// File upload helpers\nexport function validateFileType(file: File, allowedTypes: string[]): boolean {\n  return allowedTypes.includes(file.type)\n}\n\nexport function validateFileSize(file: File, maxSizeInBytes: number): boolean {\n  return file.size <= maxSizeInBytes\n}\n\n// Search helpers\nexport function buildSearchQuery(searchTerm: string, fields: string[]) {\n  if (!searchTerm) return {}\n  \n  return {\n    OR: fields.map(field => ({\n      [field]: {\n        contains: searchTerm,\n        mode: 'insensitive' as const,\n      },\n    })),\n  }\n}\n\n// Sort helpers\nexport function buildSortQuery(sortBy?: string, sortOrder: 'asc' | 'desc' = 'desc') {\n  if (!sortBy) return { createdAt: sortOrder }\n  \n  return { [sortBy]: sortOrder }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAoBO,MAAM,iBAAiB;;;;IAC5B,YACE,AAAO,OAAe,EACtB,AAAO,aAAqB,GAAG,EAC/B,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAJC,UAAA,cACA,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,SAAS,gBACd,IAAO,EACP,OAAgB,EAChB,aAAqB,GAAG;IAExB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT;QACA;IACF,GACA;QAAE,QAAQ;IAAW;AAEzB;AAGO,SAAS,cACd,KAAqB,EACrB,aAAqB,GAAG,EACxB,IAAa;IAEb,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAEzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;QACP;IACF,GACA;QAAE,QAAQ;IAAW;AAEzB;AAGO,SAAS,kBACd,IAAS,EACT,IAAY,EACZ,KAAa,EACb,KAAa,EACb,OAAgB;IAEhB,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA,YAAY;YACV;YACA;YACA;YACA;QACF;IACF;AACF;AAGO,SAAS,gBAAmB,MAAoB;IACrD,OAAO,OAAO;QACZ,IAAI;YACF,MAAM,OAAO,MAAM,QAAQ,IAAI;YAC/B,OAAO,OAAO,KAAK,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,sJAAA,CAAA,WAAQ,EAAE;gBAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MACrC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EACvC,IAAI,CAAC;gBACP,MAAM,IAAI,SAAS,CAAC,kBAAkB,EAAE,eAAe,EAAE,KAAK;YAChE;YACA,MAAM,IAAI,SAAS,wBAAwB,KAAK;QAClD;IACF;AACF;AAGO,SAAS,eAAe,OAAoB;IACjD,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAE5C,OAAO;QACL,MAAM,SAAS,aAAa,GAAG,CAAC,WAAW;QAC3C,OAAO,KAAK,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,YAAY,OAAO;QAC7D,QAAQ,aAAa,GAAG,CAAC,aAAa;QACtC,QAAQ,aAAa,GAAG,CAAC,aAAa;QACtC,WAAW,AAAC,aAAa,GAAG,CAAC,gBAAmC;QAChE,QAAQ,aAAa,GAAG,CAAC,aAAa;IACxC;AACF;AAGO,SAAS,oBAAoB,IAAY,EAAE,KAAa;IAC7D,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAC1B,OAAO;QAAE;QAAM,MAAM;IAAM;AAC7B;AAGO,SAAS,iBACd,OAAuE;IAEvE,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAE5B,IAAI,iBAAiB,UAAU;gBAC7B,OAAO,cAAc,MAAM,OAAO,EAAE,MAAM,UAAU,EAAE,MAAM,IAAI;YAClE;YAEA,IAAI,iBAAiB,sJAAA,CAAA,WAAQ,EAAE;gBAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MACrC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EACvC,IAAI,CAAC;gBACP,OAAO,cAAc,CAAC,kBAAkB,EAAE,eAAe,EAAE,KAAK;YAClE;YAEA,IAAI,iBAAiB,6HAAA,CAAA,SAAM,CAAC,6BAA6B,EAAE;gBACzD,OAAQ,MAAM,IAAI;oBAChB,KAAK;wBACH,OAAO,cAAc,0CAA0C,KAAK;oBACtE,KAAK;wBACH,OAAO,cAAc,oBAAoB,KAAK;oBAChD,KAAK;wBACH,OAAO,cAAc,iCAAiC,KAAK;oBAC7D;wBACE,OAAO,cAAc,2BAA2B,KAAK;gBACzD;YACF;YAEA,OAAO,cAAc,yBAAyB,KAAK;QACrD;IACF;AACF;AAGO,SAAS,eAAe,OAAoB,EAAE,cAAwB;IAC3E,IAAI,CAAC,eAAe,QAAQ,CAAC,QAAQ,MAAM,GAAG;QAC5C,MAAM,IAAI,SAAS,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,YAAY,CAAC,EAAE,KAAK;IAClE;AACF;AAGO,eAAe,YAAY,OAAoB;IACpD,uCAAuC;IACvC,qEAAqE;IACrE,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,SAAS,2BAA2B,KAAK;IACrD;IAEA,qEAAqE;IACrE,OAAO;QACL,IAAI;QACJ,OAAO;QACP,MAAM;IACR;AACF;AAGO,eAAe,aAAa,OAAoB;IACrD,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,KAAK,IAAI,KAAK,SAAS;QACzB,MAAM,IAAI,SAAS,yBAAyB,KAAK;IACnD;IAEA,OAAO;AACT;AAGO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAGO,SAAS,iBAAiB,IAAU,EAAE,YAAsB;IACjE,OAAO,aAAa,QAAQ,CAAC,KAAK,IAAI;AACxC;AAEO,SAAS,iBAAiB,IAAU,EAAE,cAAsB;IACjE,OAAO,KAAK,IAAI,IAAI;AACtB;AAGO,SAAS,iBAAiB,UAAkB,EAAE,MAAgB;IACnE,IAAI,CAAC,YAAY,OAAO,CAAC;IAEzB,OAAO;QACL,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;gBACvB,CAAC,MAAM,EAAE;oBACP,UAAU;oBACV,MAAM;gBACR;YACF,CAAC;IACH;AACF;AAGO,SAAS,eAAe,MAAe,EAAE,YAA4B,MAAM;IAChF,IAAI,CAAC,QAAQ,OAAO;QAAE,WAAW;IAAU;IAE3C,OAAO;QAAE,CAAC,OAAO,EAAE;IAAU;AAC/B", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// User schemas\nexport const createUserSchema = z.object({\n  email: z.string().email(),\n  firstName: z.string().optional(),\n  lastName: z.string().optional(),\n  imageUrl: z.string().url().optional(),\n  role: z.enum(['ADMIN', 'USER', 'CLIENT']).default('USER'),\n})\n\nexport const updateUserSchema = createUserSchema.partial()\n\n// Service schemas\nexport const createServiceSchema = z.object({\n  categoryId: z.string(),\n  name: z.string().min(1).max(255),\n  description: z.string().min(1),\n  iconClass: z.string().max(100).optional(),\n  price: z.number().positive(),\n  discountRate: z.number().int().min(0).max(100).optional(),\n  totalDiscount: z.number().optional(),\n  manager: z.string().max(50).optional(),\n  isActive: z.boolean().default(true),\n  displayOrder: z.number().int().default(0),\n})\n\nexport const updateServiceSchema = createServiceSchema.partial()\n\n// Project schemas\nexport const createProjectSchema = z.object({\n  orderId: z.string(),\n  clientId: z.string().optional(),\n  name: z.string().min(1),\n  description: z.string().min(1),\n  goals: z.string().optional(),\n  manager: z.string().max(10).optional(),\n  startDate: z.date().optional(),\n  completionDate: z.date().optional(),\n  estimatedCost: z.number().positive().optional(),\n  estimatedTime: z.string().max(10).optional(),\n  estimatedEffort: z.string().max(10).optional(),\n  status: z.enum(['PLANNING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED']).default('PLANNING'),\n  isFeatured: z.boolean().default(false),\n  displayOrder: z.number().int().default(0),\n  imageUrl: z.string().url().optional(),\n  projectUrl: z.string().url().optional(),\n  githubUrl: z.string().url().optional(),\n})\n\nexport const updateProjectSchema = createProjectSchema.partial()\n\n// Client schemas\nexport const createClientSchema = z.object({\n  userId: z.string().optional(),\n  companyName: z.string().min(1).max(200),\n  contactName: z.string().min(1).max(100),\n  contactPosition: z.string().max(100).optional(),\n  contactEmail: z.string().email().max(100),\n  contactPhone: z.string().max(20).optional(),\n  contactFax: z.string().max(20).optional(),\n  companyWebsite: z.string().max(100).optional(),\n  address: z.string().min(1).max(200),\n  city: z.string().min(1).max(100),\n  state: z.string().min(1).max(50),\n  zipCode: z.string().min(1).max(20),\n  country: z.string().min(1).max(100),\n  logoUrl: z.string().max(500).optional(),\n  notes: z.string().optional(),\n})\n\nexport const updateClientSchema = createClientSchema.partial()\n\n// Blog post schemas\nexport const createBlogPostSchema = z.object({\n  authorId: z.string(),\n  title: z.string().min(1).max(255),\n  slug: z.string().min(1).max(255),\n  content: z.string().min(1),\n  excerpt: z.string().optional(),\n  featuredImageUrl: z.string().max(500).optional(),\n  isPublished: z.boolean().default(false),\n  publishedAt: z.date().optional(),\n  categories: z.string().optional(),\n  tags: z.string().optional(),\n})\n\nexport const updateBlogPostSchema = createBlogPostSchema.partial()\n\n// Team member schemas\nexport const createTeamMemberSchema = z.object({\n  firstName: z.string().min(1),\n  lastName: z.string().min(1),\n  position: z.string().min(1),\n  department: z.string().optional(),\n  phone: z.string().min(1).max(12),\n  email: z.string().email().max(255).optional(),\n  salary: z.number().positive().optional(),\n  payrollMethod: z.string().max(10).optional(),\n  resumeUrl: z.string().max(500).optional(),\n  notes: z.string().optional(),\n  bio: z.string().optional(),\n  photoUrl: z.string().max(500).optional(),\n  linkedInUrl: z.string().max(500).optional(),\n  twitterUrl: z.string().max(500).optional(),\n  githubUrl: z.string().max(500).optional(),\n  displayOrder: z.number().int().default(0),\n  isActive: z.boolean().default(true),\n})\n\nexport const updateTeamMemberSchema = createTeamMemberSchema.partial()\n\n// Technology schemas\nexport const createTechnologySchema = z.object({\n  name: z.string().min(1),\n  description: z.string().optional(),\n  iconUrl: z.string().url().optional(),\n  displayOrder: z.number().int().default(0),\n  isActive: z.boolean().default(true),\n})\n\nexport const updateTechnologySchema = createTechnologySchema.partial()\n\n// Testimonial schemas\nexport const createTestimonialSchema = z.object({\n  clientName: z.string().min(1).max(100),\n  clientTitle: z.string().min(1).max(100),\n  clientCompany: z.string().min(1).max(100),\n  clientPhotoUrl: z.string().max(500).optional(),\n  content: z.string().min(1),\n  rating: z.number().int().min(1).max(5).default(5),\n  isFeatured: z.boolean().default(false),\n  displayOrder: z.number().int().default(0),\n})\n\nexport const updateTestimonialSchema = createTestimonialSchema.partial()\n\n// Contact form schemas\nexport const createContactFormSchema = z.object({\n  userId: z.string().optional(),\n  name: z.string().min(1),\n  email: z.string().email(),\n  phone: z.string().optional(),\n  company: z.string().optional(),\n  subject: z.string().min(1),\n  message: z.string().min(1),\n  status: z.enum(['NEW', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']).default('NEW'),\n})\n\nexport const updateContactFormSchema = createContactFormSchema.partial()\n\n// Category schemas\nexport const createCategorySchema = z.object({\n  name: z.string().min(1),\n  description: z.string().optional(),\n  parentId: z.string().optional(),\n  isActive: z.boolean().default(true),\n  displayOrder: z.number().int().default(0),\n})\n\nexport const updateCategorySchema = createCategorySchema.partial()\n\n// Order schemas\nexport const createOrderSchema = z.object({\n  clientId: z.string(),\n  orderNumber: z.string().min(1),\n  description: z.string().optional(),\n  totalAmount: z.number().positive(),\n  status: z.enum(['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PENDING'),\n  orderDate: z.date().default(() => new Date()),\n})\n\nexport const updateOrderSchema = createOrderSchema.partial()\n\n// Invoice schemas\nexport const createInvoiceSchema = z.object({\n  clientId: z.string(),\n  projectId: z.string().optional(),\n  orderId: z.string().optional(),\n  contractId: z.string().optional(),\n  invoiceNumber: z.string().min(1),\n  description: z.string().optional(),\n  subtotal: z.number().positive(),\n  taxAmount: z.number().default(0),\n  totalAmount: z.number().positive(),\n  status: z.enum(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED']).default('DRAFT'),\n  issueDate: z.date().default(() => new Date()),\n  dueDate: z.date(),\n  paidAt: z.date().optional(),\n})\n\nexport const updateInvoiceSchema = createInvoiceSchema.partial()\n\n// Job listing schemas\nexport const createJobListingSchema = z.object({\n  title: z.string().min(1),\n  description: z.string().min(1),\n  requirements: z.string().min(1),\n  location: z.string().min(1),\n  employmentType: z.string().min(1),\n  salaryMin: z.number().positive().optional(),\n  salaryMax: z.number().positive().optional(),\n  salaryCurrency: z.string().default('USD'),\n  isRemote: z.boolean().default(false),\n  isActive: z.boolean().default(true),\n  expiresAt: z.date().optional(),\n})\n\nexport const updateJobListingSchema = createJobListingSchema.partial()\n\n// Export all schemas as a single object for easier imports\nexport const schemas = {\n  user: { create: createUserSchema, update: updateUserSchema },\n  service: { create: createServiceSchema, update: updateServiceSchema },\n  project: { create: createProjectSchema, update: updateProjectSchema },\n  client: { create: createClientSchema, update: updateClientSchema },\n  blogPost: { create: createBlogPostSchema, update: updateBlogPostSchema },\n  teamMember: { create: createTeamMemberSchema, update: updateTeamMemberSchema },\n  technology: { create: createTechnologySchema, update: updateTechnologySchema },\n  testimonial: { create: createTestimonialSchema, update: updateTestimonialSchema },\n  contactForm: { create: createContactFormSchema, update: updateContactFormSchema },\n  category: { create: createCategorySchema, update: updateCategorySchema },\n  order: { create: createOrderSchema, update: updateOrderSchema },\n  invoice: { create: createInvoiceSchema, update: updateInvoiceSchema },\n  jobListing: { create: createJobListingSchema, update: updateJobListingSchema },\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AAGO,MAAM,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;IACvB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACnC,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAQ;KAAS,EAAE,OAAO,CAAC;AACpD;AAEO,MAAM,mBAAmB,iBAAiB,OAAO;AAGjD,MAAM,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM;IACpB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC5B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC5B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACvC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ;IACvD,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IACpC,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;AACzC;AAEO,MAAM,sBAAsB,oBAAoB,OAAO;AAGvD,MAAM,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM;IACjB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC5B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IACpC,WAAW,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC5B,gBAAgB,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IACjC,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7C,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IAC1C,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IAC5C,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAe;QAAa;QAAW;KAAY,EAAE,OAAO,CAAC;IACzF,YAAY,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACnC,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACrC,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;AACtC;AAEO,MAAM,sBAAsB,oBAAoB,OAAO;AAGvD,MAAM,qBAAqB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACnC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACnC,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IAC7C,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC;IACrC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IACzC,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IACvC,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IAC5C,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC/B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC5B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC7B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC/B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC/B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACrC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAEO,MAAM,qBAAqB,mBAAmB,OAAO;AAGrD,MAAM,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC7B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IAC9C,aAAa,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACjC,aAAa,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;IAC9B,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC3B;AAEO,MAAM,uBAAuB,qBAAqB,OAAO;AAGzD,MAAM,yBAAyB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC1B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC7B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,QAAQ;IAC3C,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtC,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IAC1C,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACvC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,KAAK,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACtC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACzC,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACxC,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACvC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,yBAAyB,uBAAuB,OAAO;AAG7D,MAAM,yBAAyB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IAClC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEO,MAAM,yBAAyB,uBAAuB,OAAO;AAG7D,MAAM,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAClC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACnC,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACrC,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IAC5C,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAC/C,YAAY,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;AACzC;AAEO,MAAM,0BAA0B,wBAAwB,OAAO;AAG/D,MAAM,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;IACvB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACxB,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAe;QAAY;KAAS,EAAE,OAAO,CAAC;AACvE;AAEO,MAAM,0BAA0B,wBAAwB,OAAO;AAG/D,MAAM,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;AACzC;AAEO,MAAM,uBAAuB,qBAAqB,OAAO;AAGzD,MAAM,oBAAoB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC5B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAa;QAAe;QAAa;KAAY,EAAE,OAAO,CAAC;IAC1F,WAAW,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,OAAO,CAAC,IAAM,IAAI;AACxC;AAEO,MAAM,oBAAoB,kBAAkB,OAAO;AAGnD,MAAM,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC9B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAQ;QAAQ;QAAW;KAAY,EAAE,OAAO,CAAC;IAC1E,WAAW,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,OAAO,CAAC,IAAM,IAAI;IACtC,SAAS,mLAAA,CAAA,IAAC,CAAC,IAAI;IACf,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;AAC3B;AAEO,MAAM,sBAAsB,oBAAoB,OAAO;AAGvD,MAAM,yBAAyB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACtB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC5B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC7B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACzB,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC/B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IACnC,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,WAAW,mLAAA,CAAA,IAAC,CAAC,IAAI,GAAG,QAAQ;AAC9B;AAEO,MAAM,yBAAyB,uBAAuB,OAAO;AAG7D,MAAM,UAAU;IACrB,MAAM;QAAE,QAAQ;QAAkB,QAAQ;IAAiB;IAC3D,SAAS;QAAE,QAAQ;QAAqB,QAAQ;IAAoB;IACpE,SAAS;QAAE,QAAQ;QAAqB,QAAQ;IAAoB;IACpE,QAAQ;QAAE,QAAQ;QAAoB,QAAQ;IAAmB;IACjE,UAAU;QAAE,QAAQ;QAAsB,QAAQ;IAAqB;IACvE,YAAY;QAAE,QAAQ;QAAwB,QAAQ;IAAuB;IAC7E,YAAY;QAAE,QAAQ;QAAwB,QAAQ;IAAuB;IAC7E,aAAa;QAAE,QAAQ;QAAyB,QAAQ;IAAwB;IAChF,aAAa;QAAE,QAAQ;QAAyB,QAAQ;IAAwB;IAChF,UAAU;QAAE,QAAQ;QAAsB,QAAQ;IAAqB;IACvE,OAAO;QAAE,QAAQ;QAAmB,QAAQ;IAAkB;IAC9D,SAAS;QAAE,QAAQ;QAAqB,QAAQ;IAAoB;IACpE,YAAY;QAAE,QAAQ;QAAwB,QAAQ;IAAuB;AAC/E", "debugId": null}}, {"offset": {"line": 561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/api/services/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { \n  with<PERSON>rror<PERSON><PERSON><PERSON>, \n  successResponse, \n  paginatedResponse,\n  getQueryParams,\n  getPaginationParams,\n  buildSearchQuery,\n  buildSortQuery,\n  validateRequest,\n  validateMethod,\n  requireAdmin\n} from '@/lib/api-utils'\nimport { createServiceSchema, updateServiceSchema } from '@/lib/validations'\n\n// GET /api/services - List all services with pagination and search\nexport const GET = withErrorHandler(async (request: NextRequest) => {\n  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)\n  const { skip, take } = getPaginationParams(page, limit)\n\n  // Build where clause\n  const where: any = {}\n  \n  // Add search functionality\n  if (search) {\n    Object.assign(where, buildSearchQuery(search, ['name', 'description']))\n  }\n  \n  // Add filter for active/inactive services\n  if (filter === 'active') {\n    where.isActive = true\n  } else if (filter === 'inactive') {\n    where.isActive = false\n  }\n\n  // Get total count for pagination\n  const total = await prisma.service.count({ where })\n\n  // Get services with pagination\n  const services = await prisma.service.findMany({\n    where,\n    include: {\n      category: {\n        select: {\n          id: true,\n          name: true,\n        },\n      },\n      _count: {\n        select: {\n          projects: true,\n          orderDetails: true,\n        },\n      },\n    },\n    orderBy: buildSortQuery(sortBy, sortOrder),\n    skip,\n    take,\n  })\n\n  return paginatedResponse(services, page, limit, total)\n})\n\n// POST /api/services - Create a new service\nexport const POST = withErrorHandler(async (request: NextRequest) => {\n  await requireAdmin(request)\n  validateMethod(request, ['POST'])\n  \n  const validate = validateRequest(createServiceSchema)\n  const data = await validate(request)\n\n  // Check if category exists\n  const category = await prisma.category.findUnique({\n    where: { id: data.categoryId },\n  })\n\n  if (!category) {\n    throw new Error('Category not found')\n  }\n\n  const service = await prisma.service.create({\n    data,\n    include: {\n      category: {\n        select: {\n          id: true,\n          name: true,\n        },\n      },\n    },\n  })\n\n  return successResponse(service, 'Service created successfully', 201)\n})\n\n// PUT /api/services - Bulk update services (admin only)\nexport const PUT = withErrorHandler(async (request: NextRequest) => {\n  await requireAdmin(request)\n  validateMethod(request, ['PUT'])\n  \n  const body = await request.json()\n  const { ids, data } = body\n\n  if (!Array.isArray(ids) || ids.length === 0) {\n    throw new Error('Invalid service IDs provided')\n  }\n\n  const validate = validateRequest(updateServiceSchema)\n  const updateData = await validate({ json: () => data } as NextRequest)\n\n  const updatedServices = await prisma.service.updateMany({\n    where: {\n      id: {\n        in: ids,\n      },\n    },\n    data: updateData,\n  })\n\n  return successResponse(\n    { count: updatedServices.count },\n    `${updatedServices.count} services updated successfully`\n  )\n})\n\n// DELETE /api/services - Bulk delete services (admin only)\nexport const DELETE = withErrorHandler(async (request: NextRequest) => {\n  await requireAdmin(request)\n  validateMethod(request, ['DELETE'])\n  \n  const body = await request.json()\n  const { ids } = body\n\n  if (!Array.isArray(ids) || ids.length === 0) {\n    throw new Error('Invalid service IDs provided')\n  }\n\n  // Check if any services are being used in projects or orders\n  const servicesInUse = await prisma.service.findMany({\n    where: {\n      id: { in: ids },\n      OR: [\n        { projects: { some: {} } },\n        { orderDetails: { some: {} } },\n      ],\n    },\n    select: { id: true, name: true },\n  })\n\n  if (servicesInUse.length > 0) {\n    const serviceNames = servicesInUse.map(s => s.name).join(', ')\n    throw new Error(\n      `Cannot delete services that are in use: ${serviceNames}. Please remove them from projects and orders first.`\n    )\n  }\n\n  const deletedServices = await prisma.service.deleteMany({\n    where: {\n      id: {\n        in: ids,\n      },\n    },\n  })\n\n  return successResponse(\n    { count: deletedServices.count },\n    `${deletedServices.count} services deleted successfully`\n  )\n})\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AAYA;;;;AAGO,MAAM,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;IAC1E,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;IAEjD,qBAAqB;IACrB,MAAM,QAAa,CAAC;IAEpB,2BAA2B;IAC3B,IAAI,QAAQ;QACV,OAAO,MAAM,CAAC,OAAO,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAAC;YAAQ;SAAc;IACvE;IAEA,0CAA0C;IAC1C,IAAI,WAAW,UAAU;QACvB,MAAM,QAAQ,GAAG;IACnB,OAAO,IAAI,WAAW,YAAY;QAChC,MAAM,QAAQ,GAAG;IACnB;IAEA,iCAAiC;IACjC,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE;IAAM;IAEjD,+BAA+B;IAC/B,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC7C;QACA,SAAS;YACP,UAAU;gBACR,QAAQ;oBACN,IAAI;oBACJ,MAAM;gBACR;YACF;YACA,QAAQ;gBACN,QAAQ;oBACN,UAAU;oBACV,cAAc;gBAChB;YACF;QACF;QACA,SAAS,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;QAChC;QACA;IACF;IAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,MAAM,OAAO;AAClD;AAGO,MAAM,OAAO,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;IAC1C,MAAM,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IACnB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;QAAC;KAAO;IAEhC,MAAM,WAAW,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,2HAAA,CAAA,sBAAmB;IACpD,MAAM,OAAO,MAAM,SAAS;IAE5B,2BAA2B;IAC3B,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAChD,OAAO;YAAE,IAAI,KAAK,UAAU;QAAC;IAC/B;IAEA,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1C;QACA,SAAS;YACP,UAAU;gBACR,QAAQ;oBACN,IAAI;oBACJ,MAAM;gBACR;YACF;QACF;IACF;IAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,gCAAgC;AAClE;AAGO,MAAM,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;IACzC,MAAM,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IACnB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;QAAC;KAAM;IAE/B,MAAM,OAAO,MAAM,QAAQ,IAAI;IAC/B,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAEtB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,KAAK,GAAG;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,2HAAA,CAAA,sBAAmB;IACpD,MAAM,aAAa,MAAM,SAAS;QAAE,MAAM,IAAM;IAAK;IAErD,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QACtD,OAAO;YACL,IAAI;gBACF,IAAI;YACN;QACF;QACA,MAAM;IACR;IAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EACnB;QAAE,OAAO,gBAAgB,KAAK;IAAC,GAC/B,GAAG,gBAAgB,KAAK,CAAC,8BAA8B,CAAC;AAE5D;AAGO,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;IAC5C,MAAM,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IACnB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;QAAC;KAAS;IAElC,MAAM,OAAO,MAAM,QAAQ,IAAI;IAC/B,MAAM,EAAE,GAAG,EAAE,GAAG;IAEhB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,KAAK,GAAG;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,6DAA6D;IAC7D,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;QAClD,OAAO;YACL,IAAI;gBAAE,IAAI;YAAI;YACd,IAAI;gBACF;oBAAE,UAAU;wBAAE,MAAM,CAAC;oBAAE;gBAAE;gBACzB;oBAAE,cAAc;wBAAE,MAAM,CAAC;oBAAE;gBAAE;aAC9B;QACH;QACA,QAAQ;YAAE,IAAI;YAAM,MAAM;QAAK;IACjC;IAEA,IAAI,cAAc,MAAM,GAAG,GAAG;QAC5B,MAAM,eAAe,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;QACzD,MAAM,IAAI,MACR,CAAC,wCAAwC,EAAE,aAAa,oDAAoD,CAAC;IAEjH;IAEA,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;QACtD,OAAO;YACL,IAAI;gBACF,IAAI;YACN;QACF;IACF;IAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EACnB;QAAE,OAAO,gBAAgB,KAAK;IAAC,GAC/B,GAAG,gBAAgB,KAAK,CAAC,8BAA8B,CAAC;AAE5D", "debugId": null}}]}