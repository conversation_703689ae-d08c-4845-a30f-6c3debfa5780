module.exports = {

"[project]/node_modules/next-auth/next/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_698fb6f0._.js",
  "server/chunks/[root-of-the-server]__5de2d0a4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/next-auth/next/index.js [app-route] (ecmascript)");
    });
});
}}),

};