{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { signIn, getSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport Link from 'next/link'\n\nconst signInSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n})\n\ntype SignInForm = z.infer<typeof signInSchema>\n\nexport default function SignInPage() {\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const router = useRouter()\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<SignInForm>({\n    resolver: zodResolver(signInSchema),\n  })\n\n  const onSubmit = async (data: SignInForm) => {\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const result = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      })\n\n      if (result?.error) {\n        setError('Invalid email or password')\n      } else {\n        // Check user role and redirect accordingly\n        const session = await getSession()\n        if (session?.user?.role === 'ADMIN') {\n          router.push('/admin')\n        } else {\n          router.push('/')\n        }\n      }\n    } catch (error) {\n      setError('An error occurred. Please try again.')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Sign in to Technoloway\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Access your admin dashboard\n          </p>\n        </div>\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit(onSubmit)}>\n          <div className=\"rounded-md shadow-sm -space-y-px\">\n            <div>\n              <label htmlFor=\"email\" className=\"sr-only\">\n                Email address\n              </label>\n              <input\n                {...register('email')}\n                type=\"email\"\n                autoComplete=\"email\"\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Email address\"\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n              )}\n            </div>\n            <div>\n              <label htmlFor=\"password\" className=\"sr-only\">\n                Password\n              </label>\n              <input\n                {...register('password')}\n                type=\"password\"\n                autoComplete=\"current-password\"\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Password\"\n              />\n              {errors.password && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n              )}\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'Signing in...' : 'Sign in'}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <Link\n              href=\"/\"\n              className=\"font-medium text-blue-600 hover:text-blue-500\"\n            >\n              ← Back to website\n            </Link>\n          </div>\n        </form>\n\n        <div className=\"mt-6 p-4 bg-blue-50 rounded-md\">\n          <h3 className=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials:</h3>\n          <p className=\"text-sm text-blue-700\">\n            Email: <EMAIL><br />\n            Password: admin123\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AARA;;;;;;;;;AAUA,MAAM,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAc;QACtB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX,OAAO;gBACL,2CAA2C;gBAC3C,MAAM,UAAU,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;gBAC/B,IAAI,SAAS,MAAM,SAAS,SAAS;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;;;;;;;8BAIxD,8OAAC;oBAAK,WAAU;oBAAiB,UAAU,aAAa;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAU;;;;;;sDAG3C,8OAAC;4CACE,GAAG,SAAS,QAAQ;4CACrB,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAGlE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAU;;;;;;sDAG9C,8OAAC;4CACE,GAAG,SAAS,WAAW;4CACxB,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;wBAKtE,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;sCAI3C,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,kBAAkB;;;;;;;;;;;sCAInC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;;gCAAwB;8CACP,8OAAC;;;;;gCAAK;;;;;;;;;;;;;;;;;;;;;;;;AAO9C", "debugId": null}}]}