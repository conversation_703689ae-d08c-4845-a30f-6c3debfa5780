"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2315],{3317:(e,s,l)=>{l.d(s,{w:()=>r});var t=l(5155),a=l(6874),i=l.n(a);function r(){return(0,t.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,t.jsxs)("div",{className:"container py-16",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"T"})}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"Technoloway"})]}),(0,t.jsx)("p",{className:"text-gray-300 mb-6 max-w-md",children:"Transforming ideas into digital reality. We build exceptional software solutions that drive business growth and innovation."}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-gray-300",children:"<EMAIL>"}),(0,t.jsx)("p",{className:"text-gray-300",children:"+1 (555) 123-4567"}),(0,t.jsx)("p",{className:"text-gray-300",children:"San Francisco, CA"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Services"}),(0,t.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/#services",className:"hover:text-white transition-colors",children:"Web Development"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/#services",className:"hover:text-white transition-colors",children:"Mobile Apps"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/#services",className:"hover:text-white transition-colors",children:"Cloud Solutions"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/#services",className:"hover:text-white transition-colors",children:"API Development"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Company"}),(0,t.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/team",className:"hover:text-white transition-colors",children:"About"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/team",className:"hover:text-white transition-colors",children:"Team"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/portfolio",className:"hover:text-white transition-colors",children:"Portfolio"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/blog",className:"hover:text-white transition-colors",children:"Blog"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/#contact",className:"hover:text-white transition-colors",children:"Contact"})})]})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-700 mt-12 pt-8 text-center",children:(0,t.jsxs)("p",{className:"text-gray-400",children:["\xa9 ",new Date().getFullYear()," Technoloway. All rights reserved."]})})]})})}},7911:(e,s,l)=>{l.d(s,{Y:()=>m});var t=l(5155),a=l(2115),i=l(6874),r=l.n(i),n=l(5695),c=l(760),o=l(6408),d=l(9598),h=l(4500);let x=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Projects",href:"/projects"},{name:"Technologies",href:"/technologies"},{name:"Contact",href:"/contact"}];function m(){let[e,s]=(0,a.useState)(!1),l=(0,n.usePathname)(),i=e=>"/"===e?"/"===l:l.startsWith(e);return(0,t.jsxs)("header",{className:"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200",children:[(0,t.jsxs)("nav",{className:"container flex items-center justify-between py-4","aria-label":"Global",children:[(0,t.jsx)("div",{className:"flex lg:flex-1",children:(0,t.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,t.jsx)("span",{className:"sr-only",children:"Technoloway"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"T"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Technoloway"})]})]})}),(0,t.jsx)("div",{className:"flex lg:hidden",children:(0,t.jsxs)("button",{type:"button",className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700",onClick:()=>s(!0),children:[(0,t.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,t.jsx)(d.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,t.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:x.map(e=>(0,t.jsx)(r(),{href:e.href,className:"text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ".concat(i(e.href)?"text-blue-600":"text-gray-900"),children:e.name},e.name))}),(0,t.jsx)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end",children:(0,t.jsx)(r(),{href:"/contact",className:"btn-primary",children:"Get Started"})})]}),(0,t.jsx)(c.N,{children:e&&(0,t.jsxs)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"lg:hidden",children:[(0,t.jsx)("div",{className:"fixed inset-0 z-50"}),(0,t.jsxs)(o.P.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"spring",damping:25,stiffness:200},className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(r(),{href:"/",className:"-m-1.5 p-1.5",children:[(0,t.jsx)("span",{className:"sr-only",children:"Technoloway"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"T"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Technoloway"})]})]}),(0,t.jsxs)("button",{type:"button",className:"-m-2.5 rounded-md p-2.5 text-gray-700",onClick:()=>s(!1),children:[(0,t.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,t.jsx)(h.A,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,t.jsx)("div",{className:"mt-6 flow-root",children:(0,t.jsxs)("div",{className:"-my-6 divide-y divide-gray-500/10",children:[(0,t.jsx)("div",{className:"space-y-2 py-6",children:x.map(e=>(0,t.jsx)(r(),{href:e.href,className:"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ".concat(i(e.href)?"text-blue-600":"text-gray-900"),onClick:()=>s(!1),children:e.name},e.name))}),(0,t.jsx)("div",{className:"py-6",children:(0,t.jsx)(r(),{href:"/contact",className:"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50",onClick:()=>s(!1),children:"Get Started"})})]})})]})]})})]})}}}]);