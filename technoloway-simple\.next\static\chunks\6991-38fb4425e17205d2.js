"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6991],{760:(e,t,r)=>{r.d(t,{N:()=>b});var n=r(5155),i=r(2115),o=r(869),l=r(2885),a=r(7494),s=r(845),u=r(7351),d=r(1508);class c extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,u.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(e){let{children:t,isPresent:r,anchorX:o}=e,l=(0,i.useId)(),a=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,i.useContext)(d.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:i,right:d}=s.current;if(r||!a.current||!e||!t)return;a.current.dataset.motionPopId=l;let c=document.createElement("style");return u&&(c.nonce=u),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(d),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[r]),(0,n.jsx)(c,{isPresent:r,childRef:a,sizeRef:s,children:i.cloneElement(t,{ref:a})})}let p=e=>{let{children:t,initial:r,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:d,mode:c,anchorX:p}=e,h=(0,l.M)(m),g=(0,i.useId)(),v=!0,b=(0,i.useMemo)(()=>(v=!1,{id:g,initial:r,isPresent:o,custom:u,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;a&&a()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[o,h,a]);return d&&v&&(b={...b}),(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[o]),i.useEffect(()=>{o||h.size||!a||a()},[o]),"popLayout"===c&&(t=(0,n.jsx)(f,{isPresent:o,anchorX:p,children:t})),(0,n.jsx)(s.t.Provider,{value:b,children:t})};function m(){return new Map}var h=r(2082);let g=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:r,initial:s=!0,onExitComplete:u,presenceAffectsLayout:d=!0,mode:c="sync",propagate:f=!1,anchorX:m="left"}=e,[b,y]=(0,h.xQ)(f),_=(0,i.useMemo)(()=>v(t),[t]),w=f&&!b?[]:_.map(g),x=(0,i.useRef)(!0),j=(0,i.useRef)(_),E=(0,l.M)(()=>new Map),[C,P]=(0,i.useState)(_),[S,O]=(0,i.useState)(_);(0,a.E)(()=>{x.current=!1,j.current=_;for(let e=0;e<S.length;e++){let t=g(S[e]);w.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[S,w.length,w.join("-")]);let M=[];if(_!==C){let e=[..._];for(let t=0;t<S.length;t++){let r=S[t],n=g(r);w.includes(n)||(e.splice(t,0,r),M.push(r))}return"wait"===c&&M.length&&(e=M),O(v(e)),P(_),null}let{forceRender:R}=(0,i.useContext)(o.L);return(0,n.jsx)(n.Fragment,{children:S.map(e=>{let t=g(e),i=(!f||!!b)&&(_===S||w.includes(t));return(0,n.jsx)(p,{isPresent:i,initial:(!x.current||!!s)&&void 0,custom:r,presenceAffectsLayout:d,mode:c,onExitComplete:i?void 0:()=>{if(!E.has(t))return;E.set(t,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(null==R||R(),O(j.current),f&&(null==y||y()),u&&u())},anchorX:m,children:e},t)})})}},901:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext(null)},1193:(e,t)=>{function r(e){var t;let{config:r,src:n,width:i,quality:o}=e,l=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+l+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},1469:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return a}});let n=r(8229),i=r(8883),o=r(3063),l=n._(r(1193));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=o.Image},2464:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext({})},3063:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return _}});let n=r(8229),i=r(6966),o=r(5155),l=i._(r(2115)),a=n._(r(7650)),s=n._(r(5564)),u=r(8883),d=r(5840),c=r(6752);r(3230);let f=r(901),p=n._(r(1193)),m=r(6654),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,n,i,o,l){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function v(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}let b=(0,l.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:i,height:a,width:s,decoding:u,className:d,style:c,fetchPriority:f,placeholder:p,loading:h,unoptimized:b,fill:y,onLoadRef:_,onLoadingCompleteRef:w,setBlurComplete:x,setShowAltText:j,sizesInput:E,onLoad:C,onError:P,...S}=e,O=(0,l.useCallback)(e=>{e&&(P&&(e.src=e.src),e.complete&&g(e,p,_,w,x,b,E))},[r,p,_,w,x,P,b,E]),M=(0,m.useMergedRef)(t,O);return(0,o.jsx)("img",{...S,...v(f),loading:h,width:s,height:a,decoding:u,"data-nimg":y?"fill":"1",className:d,style:c,sizes:i,srcSet:n,src:r,ref:M,onLoad:e=>{g(e.currentTarget,p,_,w,x,b,E)},onError:e=>{j(!0),"empty"!==p&&x(!0),P&&P(e)}})});function y(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...v(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,n),null):(0,o.jsx)(s.default,{children:(0,o.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let _=(0,l.forwardRef)((e,t)=>{let r=(0,l.useContext)(f.RouterContext),n=(0,l.useContext)(c.ImageConfigContext),i=(0,l.useMemo)(()=>{var e;let t=h||n||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:o}},[n]),{onLoad:a,onLoadingComplete:s}=e,m=(0,l.useRef)(a);(0,l.useEffect)(()=>{m.current=a},[a]);let g=(0,l.useRef)(s);(0,l.useEffect)(()=>{g.current=s},[s]);let[v,_]=(0,l.useState)(!1),[w,x]=(0,l.useState)(!1),{props:j,meta:E}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:i,blurComplete:v,showAltText:w});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(b,{...j,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:_,setShowAltText:x,sizesInput:e.sizes,ref:t}),E.priority?(0,o.jsx)(y,{isAppRouter:!r,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4500:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(2115);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},5029:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(2115),i=n.useLayoutEffect,o=n.useEffect;function l(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let i=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(i,e))}}return i(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),i(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),o(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:l}=e,a=n?40*n:t,s=i?40*i:r,u=a&&s?"viewBox='0 0 "+a+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},5564:(e,t,r)=>{var n=r(9509);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return f}});let i=r(8229),o=r(6966),l=r(5155),a=o._(r(2115)),s=i._(r(5029)),u=r(2464),d=r(2830),c=r(7544);function f(e){void 0===e&&(e=!1);let t=[(0,l.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,l.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(3230);let m=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let o=!0,l=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){l=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(i.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!l)&&r.has(e)?o=!1:(r.add(e),n[t]=r)}}}return o}}()).reverse().map((e,t)=>{let i=e.key||t;if(n.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:i})})}let g=function(e){let{children:t}=e,r=(0,a.useContext)(u.AmpStateContext),n=(0,a.useContext)(d.HeadManagerContext);return(0,l.jsx)(s.default,{reduceComponentsToState:h,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6752:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return o}});let n=r(8229)._(r(2115)),i=r(5840),o=n.default.createContext(i.imageConfigDefault)},6766:(e,t,r)=>{r.d(t,{default:()=>i.a});var n=r(1469),i=r.n(n)},7544:(e,t)=>{function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},8883:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(3230);let n=r(5100),i=r(5840),o=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let u,d,c,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:v,quality:b,width:y,height:_,fill:w=!1,style:x,overrideSrc:j,onLoad:E,onLoadingComplete:C,placeholder:P="empty",blurDataURL:S,fetchPriority:O,decoding:M="async",layout:R,objectFit:z,objectPosition:k,lazyBoundary:I,lazyRoot:A,...D}=e,{imgConf:L,showAltText:N,blurComplete:T,defaultLoader:F}=t,U=L||i.imageConfigDefault;if("allSizes"in U)u=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),n=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);u={...U,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=D.loader||F;delete D.loader,delete D.srcSet;let W="__next_img_default"in B;if(W){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:r,...n}=t;return e(n)}}if(R){"fill"===R&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!p&&(p=t)}let G="",q=a(y),X=a(_);if((s=f)&&"object"==typeof s&&(l(s)||void 0!==s.src)){let e=l(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,S=S||e.blurDataURL,G=e.src,!w)if(q||X){if(q&&!X){let t=q/e.width;X=Math.round(e.height*t)}else if(!q&&X){let t=X/e.height;q=Math.round(e.width*t)}}else q=e.width,X=e.height}let V=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:G)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,V=!1),u.unoptimized&&(m=!0),W&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=a(b),$=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:k}:{},N?{}:{color:"transparent"},x),J=T||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:q,heightInt:X,blurWidth:d,blurHeight:c,blurDataURL:S||"",objectFit:$.objectFit})+'")':'url("'+P+'")',Q=o.includes($.objectFit)?"fill"===$.objectFit?"100% 100%":"cover":$.objectFit,Y=J?{backgroundSize:Q,backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Z=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:o,sizes:l,loader:a}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:s,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,l),d=s.length-1;return{sizes:l||"w"!==u?l:"100vw",srcSet:s.map((e,n)=>a({config:t,src:r,quality:o,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:a({config:t,src:r,quality:o,width:s[d]})}}({config:u,src:f,unoptimized:m,width:q,quality:H,sizes:p,loader:B});return{props:{...D,loading:V?"lazy":g,fetchPriority:O,width:q,height:X,decoding:M,className:v,style:{...$,...Y},sizes:Z.sizes,srcSet:Z.srcSet,src:j||Z.src},meta:{unoptimized:m,priority:h,placeholder:P,fill:w}}}},9598:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(2115);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...o}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))})}}]);