"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7329],{760:(e,t,n)=>{n.d(t,{N:()=>b});var r=n(5155),o=n(2115),u=n(869),i=n(2885),l=n(7494),a=n(845),s=n(7351),c=n(1508);class f extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,s.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:n,anchorX:u}=e,i=(0,o.useId)(),l=(0,o.useRef)(null),a=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:s}=(0,o.useContext)(c.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:o,right:c}=a.current;if(n||!l.current||!e||!t)return;l.current.dataset.motionPopId=i;let f=document.createElement("style");return s&&(f.nonce=s),document.head.appendChild(f),f.sheet&&f.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===u?"left: ".concat(o):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.contains(f)&&document.head.removeChild(f)}},[n]),(0,r.jsx)(f,{isPresent:n,childRef:l,sizeRef:a,children:o.cloneElement(t,{ref:l})})}let p=e=>{let{children:t,initial:n,isPresent:u,onExitComplete:l,custom:s,presenceAffectsLayout:c,mode:f,anchorX:p}=e,m=(0,i.M)(h),g=(0,o.useId)(),y=!0,b=(0,o.useMemo)(()=>(y=!1,{id:g,initial:n,isPresent:u,custom:s,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;l&&l()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[u,m,l]);return c&&y&&(b={...b}),(0,o.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[u]),o.useEffect(()=>{u||m.size||!l||l()},[u]),"popLayout"===f&&(t=(0,r.jsx)(d,{isPresent:u,anchorX:p,children:t})),(0,r.jsx)(a.t.Provider,{value:b,children:t})};function h(){return new Map}var m=n(2082);let g=e=>e.key||"";function y(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:n,initial:a=!0,onExitComplete:s,presenceAffectsLayout:c=!0,mode:f="sync",propagate:d=!1,anchorX:h="left"}=e,[b,P]=(0,m.xQ)(d),v=(0,o.useMemo)(()=>y(t),[t]),E=d&&!b?[]:v.map(g),j=(0,o.useRef)(!0),x=(0,o.useRef)(v),_=(0,i.M)(()=>new Map),[w,O]=(0,o.useState)(v),[C,M]=(0,o.useState)(v);(0,l.E)(()=>{j.current=!1,x.current=v;for(let e=0;e<C.length;e++){let t=g(C[e]);E.includes(t)?_.delete(t):!0!==_.get(t)&&_.set(t,!1)}},[C,E.length,E.join("-")]);let L=[];if(v!==w){let e=[...v];for(let t=0;t<C.length;t++){let n=C[t],r=g(n);E.includes(r)||(e.splice(t,0,n),L.push(n))}return"wait"===f&&L.length&&(e=L),M(y(e)),O(v),null}let{forceRender:R}=(0,o.useContext)(u.L);return(0,r.jsx)(r.Fragment,{children:C.map(e=>{let t=g(e),o=(!d||!!b)&&(v===C||E.includes(t));return(0,r.jsx)(p,{isPresent:o,initial:(!j.current||!!a)&&void 0,custom:n,presenceAffectsLayout:c,mode:f,onExitComplete:o?void 0:()=>{if(!_.has(t))return;_.set(t,!0);let e=!0;_.forEach(t=>{t||(e=!1)}),e&&(null==R||R(),M(x.current),d&&(null==P||P()),s&&s())},anchorX:h,children:e},t)})})}},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return u}});let r=n(9991),o=n(7102);function u(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return u},formatWithValidation:function(){return l},urlObjectKeys:function(){return i}});let r=n(6966)._(n(8859)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:n}=e,u=e.protocol||"",i=e.pathname||"",l=e.hash||"",a=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:n&&(s=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(s+=":"+e.port)),a&&"object"==typeof a&&(a=String(r.urlQueryToSearchParams(a)));let c=e.search||a&&"?"+a||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==s?(s="//"+(s||""),i&&"/"!==i[0]&&(i="/"+i)):s||(s=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+u+s+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return u(e)}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},4500:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(2115);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...u}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},u),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},5695:(e,t,n)=>{var r=n(8999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}})},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(2115);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=u(e,r)),t&&(o.current=u(t,r))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return b}});let r=n(6966),o=n(5155),u=r._(n(2115)),i=n(2757),l=n(5227),a=n(9818),s=n(6654),c=n(9991),f=n(5929);n(3230);let d=n(4930),p=n(2664),h=n(6634);function m(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function g(e){let t,n,r,[i,g]=(0,u.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,u.useRef)(null),{href:P,as:v,children:E,prefetch:j=null,passHref:x,replace:_,shallow:w,scroll:O,onClick:C,onMouseEnter:M,onTouchStart:L,legacyBehavior:R=!1,onNavigate:k,ref:S,unstable_dynamicOnHover:T,...N}=e;t=E,R&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let A=u.default.useContext(l.AppRouterContext),I=!1!==j,U=null===j?a.PrefetchKind.AUTO:a.PrefetchKind.FULL,{href:D,as:F}=u.default.useMemo(()=>{let e=m(P);return{href:e,as:v?m(v):e}},[P,v]);R&&(n=u.default.Children.only(t));let B=R?n&&"object"==typeof n&&n.ref:S,K=u.default.useCallback(e=>(null!==A&&(b.current=(0,d.mountLinkInstance)(e,D,A,U,I,g)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[I,D,A,U,g]),z={ref:(0,s.useMergedRef)(K,B),onClick(e){R||"function"!=typeof C||C(e),R&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),A&&(e.defaultPrevented||function(e,t,n,r,o,i,l){let{nodeName:a}=e.currentTarget;if(!("A"===a.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),u.default.startTransition(()=>{if(l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==i||i,r.current)})}}(e,D,F,b,_,O,k))},onMouseEnter(e){R||"function"!=typeof M||M(e),R&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),A&&I&&(0,d.onNavigationIntent)(e.currentTarget,!0===T)},onTouchStart:function(e){R||"function"!=typeof L||L(e),R&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),A&&I&&(0,d.onNavigationIntent)(e.currentTarget,!0===T)}};return(0,c.isAbsoluteUrl)(F)?z.href=F:R&&!x&&("a"!==n.type||"href"in n.props)||(z.href=(0,f.addBasePath)(F)),r=R?u.default.cloneElement(n,z):(0,o.jsx)("a",{...N,...z,children:t}),(0,o.jsx)(y.Provider,{value:i,children:r})}n(3180);let y=(0,u.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,u.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function u(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return u},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},9598:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(2115);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...u}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},u),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))})},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return a},getLocationOrigin:function(){return i},getURL:function(){return l},isAbsoluteUrl:function(){return u},isResSent:function(){return s},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return P}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),u=0;u<r;u++)o[u]=arguments[u];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,u=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=i();return e.substring(t.length)}function a(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&s(n))return r;if(!r)throw Object.defineProperty(Error('"'+a(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function P(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);