(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6203],{184:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(2115);let a=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},1151:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(2115);let a=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},2589:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(2115);let a=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},3742:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(2115);let a=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})},4589:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(5155),a=t(2115),l=t(6408),i=t(6865),n=t(3742),d=t(2589);let o=a.forwardRef(function(e,s){let{title:t,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},l),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))});var c=t(1151),m=t(184),x=t(5052);let u=[{id:1,fileName:"team_members.csv",fileSize:2048,uploadDate:"2024-01-22T10:30:00Z",status:"Completed",recordsProcessed:8,recordsSuccess:8,recordsError:0,dataType:"Team Members",uploadedBy:"Admin User",errors:[]},{id:2,fileName:"projects_data.json",fileSize:15360,uploadDate:"2024-01-20T14:15:00Z",status:"Completed",recordsProcessed:25,recordsSuccess:23,recordsError:2,dataType:"Projects",uploadedBy:"Admin User",errors:["Row 5: Invalid date format for project start date","Row 12: Missing required field: client_id"]},{id:3,fileName:"client_contacts.xlsx",fileSize:8192,uploadDate:"2024-01-18T09:45:00Z",status:"Failed",recordsProcessed:0,recordsSuccess:0,recordsError:15,dataType:"Clients",uploadedBy:"Admin User",errors:["Invalid file format: Expected CSV or JSON","File contains unsupported characters"]},{id:4,fileName:"testimonials.csv",fileSize:4096,uploadDate:"2024-01-15T16:20:00Z",status:"Processing",recordsProcessed:5,recordsSuccess:5,recordsError:0,dataType:"Testimonials",uploadedBy:"Admin User",errors:[]}],h=[{value:"team-members",label:"Team Members",description:"Upload team member profiles and information"},{value:"projects",label:"Projects",description:"Import project data and details"},{value:"clients",label:"Clients",description:"Add client information and contacts"},{value:"testimonials",label:"Testimonials",description:"Import customer testimonials and reviews"},{value:"blog-posts",label:"Blog Posts",description:"Bulk upload blog content"},{value:"services",label:"Services",description:"Import service offerings and pricing"},{value:"technologies",label:"Technologies",description:"Upload technology stack information"}];function p(){var e;let[s,t]=(0,a.useState)(""),[p,f]=(0,a.useState)(!1),[g,b]=(0,a.useState)([]),[j,v]=(0,a.useState)({}),[y,N]=(0,a.useState)(null),w=(0,a.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?f(!0):"dragleave"===e.type&&f(!1)},[]),k=(0,a.useCallback)(e=>{if(e.preventDefault(),e.stopPropagation(),f(!1),e.dataTransfer.files&&e.dataTransfer.files[0]){let s=Array.from(e.dataTransfer.files);b(e=>[...e,...s]),s.forEach(e=>{E(e.name)})}},[]),E=e=>{let s=0,t=setInterval(()=>{(s+=30*Math.random())>=100&&(s=100,clearInterval(t)),v(t=>({...t,[e]:s}))},500)},S=e=>{b(s=>s.filter((s,t)=>t!==e))},C=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},A=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),D=e=>{switch(e){case"Completed":return"bg-green-100 text-green-800";case"Processing":return"bg-blue-100 text-blue-800";case"Failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},L=e=>{switch(e){case"Completed":return i.A;case"Processing":return n.A;case"Failed":return d.A;default:return o}};return(0,r.jsx)("div",{className:"py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Data Upload"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Import data from CSV, JSON, or Excel files to populate your database"})]}),(0,r.jsx)("div",{className:"mb-8 bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Upload New Data"}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Data Type"}),(0,r.jsxs)("select",{value:s,onChange:e=>t(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:[(0,r.jsx)("option",{value:"",children:"Choose data type..."}),h.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:null==(e=h.find(e=>e.value===s))?void 0:e.description})]}),(0,r.jsx)("div",{className:"relative border-2 border-dashed rounded-lg p-6 transition-colors ".concat(p?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"),onDragEnter:w,onDragLeave:w,onDragOver:w,onDrop:k,children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[(0,r.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Drop files here or click to upload"}),(0,r.jsx)("input",{id:"file-upload",name:"file-upload",type:"file",className:"sr-only",multiple:!0,accept:".csv,.json,.xlsx,.xls",onChange:e=>{if(e.target.files){let s=Array.from(e.target.files);b(e=>[...e,...s]),s.forEach(e=>{E(e.name)})}}})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Supports CSV, JSON, and Excel files up to 10MB"})]})]})}),g.length>0&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Uploaded Files"}),(0,r.jsx)("div",{className:"space-y-3",children:g.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(o,{className:"h-8 w-8 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:C(e.size)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[void 0!==j[e.name]&&(0,r.jsxs)("div",{className:"w-32",children:[(0,r.jsx)("div",{className:"bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(j[e.name],"%")}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[Math.round(j[e.name]),"%"]})]}),(0,r.jsx)("button",{onClick:()=>S(s),className:"text-gray-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})})]})]},s))}),(0,r.jsx)("div",{className:"mt-4 flex justify-end",children:(0,r.jsx)("button",{disabled:!s,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Process Upload"})})]})]})}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Upload History"}),(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:u.map((e,s)=>{let t=L(e.status);return(0,r.jsx)(l.P.li,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*s},className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center ".concat(D(e.status)),children:(0,r.jsx)(t,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.fileName}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(D(e.status)),children:e.status})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mb-2",children:[(0,r.jsx)("span",{children:e.dataType}),(0,r.jsx)("span",{children:C(e.fileSize)}),(0,r.jsxs)("span",{children:["Uploaded: ",A(e.uploadDate)]}),(0,r.jsxs)("span",{children:["By: ",e.uploadedBy]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,r.jsxs)("span",{className:"text-green-600",children:["✓ ",e.recordsSuccess," successful"]}),e.recordsError>0&&(0,r.jsxs)("span",{className:"text-red-600",children:["✗ ",e.recordsError," errors"]}),(0,r.jsxs)("span",{className:"text-gray-500",children:["Total: ",e.recordsProcessed," records"]})]}),e.errors.length>0&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("details",{className:"text-sm",children:[(0,r.jsxs)("summary",{className:"cursor-pointer text-red-600 hover:text-red-800",children:["View ",e.errors.length," error(s)"]}),(0,r.jsx)("ul",{className:"mt-1 ml-4 list-disc text-red-600",children:e.errors.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>N(e),className:"text-gray-400 hover:text-blue-600 transition-colors",title:"View Details",children:(0,r.jsx)(m.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-green-600 transition-colors",title:"Download Report",children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})})]})]})},e.id)})})})]})}),y&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:()=>N(null)}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full",children:[(0,r.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsx)("div",{className:"sm:flex sm:items-start",children:(0,r.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,r.jsxs)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:["Upload Details: ",y.fileName]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,r.jsx)("span",{className:"mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(D(y.status)),children:y.status})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Data Type"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:y.dataType})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"File Size"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:C(y.fileSize)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Upload Date"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:A(y.uploadDate)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Processing Summary"}),(0,r.jsx)("div",{className:"mt-1 bg-gray-50 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:y.recordsProcessed}),(0,r.jsx)("div",{className:"text-gray-500",children:"Total Records"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-semibold text-green-600",children:y.recordsSuccess}),(0,r.jsx)("div",{className:"text-gray-500",children:"Successful"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-lg font-semibold text-red-600",children:y.recordsError}),(0,r.jsx)("div",{className:"text-gray-500",children:"Errors"})]})]})})]}),y.errors.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Errors"}),(0,r.jsx)("div",{className:"mt-1 bg-red-50 rounded-lg p-3",children:(0,r.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:y.errors.map((e,s)=>(0,r.jsxs)("li",{children:["• ",e]},s))})})]})]})]})})}),(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm",children:"Download Report"}),(0,r.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm",onClick:()=>N(null),children:"Close"})]})]})]})})]})})}},5052:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(2115);let a=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))})},6170:(e,s,t)=>{Promise.resolve().then(t.bind(t,4589))},6865:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(2115);let a=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})}},e=>{var s=s=>e(e.s=s);e.O(0,[6408,8441,1684,7358],()=>s(6170)),_N_E=e.O()}]);