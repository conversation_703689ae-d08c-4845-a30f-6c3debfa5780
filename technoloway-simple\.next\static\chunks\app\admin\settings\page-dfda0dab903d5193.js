(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7122],{1327:(e,t,s)=>{Promise.resolve().then(s.bind(s,6613))},4170:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var n=s(2115);let r=n.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?n.createElement("title",{id:r},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},5246:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var n=s(2115);let r=n.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?n.createElement("title",{id:r},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))})},5895:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var n=s(2115);let r=n.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?n.createElement("title",{id:r},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))})},6613:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var n=s(5155),r=s(2115),a=s(6408),i=s(5246),o=s(4170),l=s(8828),c=s(8246);let d=r.forwardRef(function(e,t){let{title:s,titleId:n,...a}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},a),s?r.createElement("title",{id:n},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.53 16.122a3 3 0 0 0-5.78 1.128 2.25 2.25 0 0 1-2.4 2.245 4.5 4.5 0 0 0 8.4-2.245c0-.399-.078-.78-.22-1.128Zm0 0a15.998 15.998 0 0 0 3.388-1.62m-5.043-.025a15.994 15.994 0 0 1 1.622-3.395m3.42 3.42a15.995 15.995 0 0 0 4.764-4.648l3.876-5.814a1.151 1.151 0 0 0-1.597-1.597L14.146 6.32a15.996 15.996 0 0 0-4.649 4.763m3.42 3.42a6.776 6.776 0 0 0-3.42-3.42"}))});var u=s(5895);let m=[{id:"general",name:"General",icon:i.A,description:"Basic site settings and configuration"},{id:"profile",name:"Profile",icon:o.A,description:"Your personal account settings"},{id:"notifications",name:"Notifications",icon:l.A,description:"Email and push notification preferences"},{id:"security",name:"Security",icon:c.A,description:"Password and security settings"},{id:"appearance",name:"Appearance",icon:d,description:"Theme and display preferences"},{id:"integrations",name:"Integrations",icon:u.A,description:"Third-party services and APIs"}];function x(){var e,t;let[s,i]=(0,r.useState)("general"),[o,l]=(0,r.useState)({siteName:"Technoloway",siteDescription:"Leading software development company",siteUrl:"https://technoloway.com",contactEmail:"<EMAIL>",firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+****************",timezone:"America/New_York",emailNotifications:!0,pushNotifications:!0,marketingEmails:!1,weeklyReports:!0,twoFactorAuth:!1,sessionTimeout:30,theme:"light",language:"en",googleAnalytics:"",mailchimpApi:"",slackWebhook:""}),c=(e,t)=>{l(s=>({...s,[e]:t}))},d=()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Site Name"}),(0,n.jsx)("input",{type:"text",value:o.siteName,onChange:e=>c("siteName",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Site Description"}),(0,n.jsx)("textarea",{value:o.siteDescription,onChange:e=>c("siteDescription",e.target.value),rows:3,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Site URL"}),(0,n.jsx)("input",{type:"url",value:o.siteUrl,onChange:e=>c("siteUrl",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Contact Email"}),(0,n.jsx)("input",{type:"email",value:o.contactEmail,onChange:e=>c("contactEmail",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]})]}),u=()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"First Name"}),(0,n.jsx)("input",{type:"text",value:o.firstName,onChange:e=>c("firstName",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Last Name"}),(0,n.jsx)("input",{type:"text",value:o.lastName,onChange:e=>c("lastName",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,n.jsx)("input",{type:"email",value:o.email,onChange:e=>c("email",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Phone"}),(0,n.jsx)("input",{type:"tel",value:o.phone,onChange:e=>c("phone",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Timezone"}),(0,n.jsxs)("select",{value:o.timezone,onChange:e=>c("timezone",e.target.value),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,n.jsx)("option",{value:"America/New_York",children:"Eastern Time"}),(0,n.jsx)("option",{value:"America/Chicago",children:"Central Time"}),(0,n.jsx)("option",{value:"America/Denver",children:"Mountain Time"}),(0,n.jsx)("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]})]}),x=()=>(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Email Notifications"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Receive notifications via email"})]}),(0,n.jsx)("button",{type:"button",onClick:()=>c("emailNotifications",!o.emailNotifications),className:"".concat(o.emailNotifications?"bg-blue-600":"bg-gray-200"," relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),children:(0,n.jsx)("span",{className:"".concat(o.emailNotifications?"translate-x-5":"translate-x-0"," pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out")})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Push Notifications"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Receive push notifications in browser"})]}),(0,n.jsx)("button",{type:"button",onClick:()=>c("pushNotifications",!o.pushNotifications),className:"".concat(o.pushNotifications?"bg-blue-600":"bg-gray-200"," relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),children:(0,n.jsx)("span",{className:"".concat(o.pushNotifications?"translate-x-5":"translate-x-0"," pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out")})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Marketing Emails"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Receive marketing and promotional emails"})]}),(0,n.jsx)("button",{type:"button",onClick:()=>c("marketingEmails",!o.marketingEmails),className:"".concat(o.marketingEmails?"bg-blue-600":"bg-gray-200"," relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),children:(0,n.jsx)("span",{className:"".concat(o.marketingEmails?"translate-x-5":"translate-x-0"," pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out")})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Weekly Reports"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Receive weekly analytics reports"})]}),(0,n.jsx)("button",{type:"button",onClick:()=>c("weeklyReports",!o.weeklyReports),className:"".concat(o.weeklyReports?"bg-blue-600":"bg-gray-200"," relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),children:(0,n.jsx)("span",{className:"".concat(o.weeklyReports?"translate-x-5":"translate-x-0"," pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out")})})]})]})}),f=()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Two-Factor Authentication"}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Add an extra layer of security to your account"})]}),(0,n.jsx)("button",{type:"button",onClick:()=>c("twoFactorAuth",!o.twoFactorAuth),className:"".concat(o.twoFactorAuth?"bg-blue-600":"bg-gray-200"," relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"),children:(0,n.jsx)("span",{className:"".concat(o.twoFactorAuth?"translate-x-5":"translate-x-0"," pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out")})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Session Timeout (minutes)"}),(0,n.jsxs)("select",{value:o.sessionTimeout,onChange:e=>c("sessionTimeout",parseInt(e.target.value)),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,n.jsx)("option",{value:15,children:"15 minutes"}),(0,n.jsx)("option",{value:30,children:"30 minutes"}),(0,n.jsx)("option",{value:60,children:"1 hour"}),(0,n.jsx)("option",{value:120,children:"2 hours"}),(0,n.jsx)("option",{value:480,children:"8 hours"})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("button",{className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Change Password"}),(0,n.jsx)("button",{className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Download Account Data"})]})]});return(0,n.jsx)("div",{className:"py-6",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Settings"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your account settings and preferences"})]}),(0,n.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-x-5",children:[(0,n.jsx)("aside",{className:"py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3",children:(0,n.jsx)("nav",{className:"space-y-1",children:m.map(e=>{let t=e.icon;return(0,n.jsxs)("button",{onClick:()=>i(e.id),className:"".concat(s===e.id?"bg-blue-50 border-blue-500 text-blue-700":"border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900"," group border-l-4 px-3 py-2 flex items-center text-sm font-medium w-full text-left"),children:[(0,n.jsx)(t,{className:"".concat(s===e.id?"text-blue-500":"text-gray-400 group-hover:text-gray-500"," flex-shrink-0 -ml-1 mr-3 h-6 w-6")}),(0,n.jsx)("span",{className:"truncate",children:e.name})]},e.id)})})}),(0,n.jsx)("div",{className:"space-y-6 sm:px-6 lg:px-0 lg:col-span-9",children:(0,n.jsx)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-white shadow rounded-lg",children:(0,n.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:null==(e=m.find(e=>e.id===s))?void 0:e.name}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:null==(t=m.find(e=>e.id===s))?void 0:t.description})]}),(()=>{switch(s){case"general":return d();case"profile":return u();case"notifications":return x();case"security":return f();default:return(0,n.jsx)("div",{children:"Settings section not implemented yet."})}})(),(0,n.jsx)("div",{className:"mt-6 flex justify-end",children:(0,n.jsx)("button",{className:"ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Save Changes"})})]})},s)})]})]})})}},8246:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var n=s(2115);let r=n.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?n.createElement("title",{id:r},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},8828:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var n=s(2115);let r=n.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?n.createElement("title",{id:r},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,8441,1684,7358],()=>t(1327)),_N_E=e.O()}]);