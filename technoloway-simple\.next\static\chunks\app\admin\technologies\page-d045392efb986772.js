(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7753],{184:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},1151:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},1316:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},4393:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 6h.008v.008H6V6Z"}))})},4759:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(5155),a=s(2115),i=s(6408),n=s(7765),l=s(8046),c=s(7305),d=s(184),o=s(1316),m=s(1151),x=s(4393);let u=[{id:1,name:"React",description:"A JavaScript library for building user interfaces with component-based architecture.",category:"Frontend",type:"Framework",proficiencyLevel:"Expert",yearsOfExperience:5,projectsUsed:45,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg",website:"https://reactjs.org",documentation:"https://reactjs.org/docs",isActive:!0,isFeatured:!0,tags:["JavaScript","UI","SPA","Component-based"],createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-20T14:30:00Z"},{id:2,name:"Next.js",description:"The React framework for production with server-side rendering and static site generation.",category:"Frontend",type:"Framework",proficiencyLevel:"Expert",yearsOfExperience:4,projectsUsed:32,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg",website:"https://nextjs.org",documentation:"https://nextjs.org/docs",isActive:!0,isFeatured:!0,tags:["React","SSR","SSG","Full-stack"],createdAt:"2024-01-12T11:00:00Z",updatedAt:"2024-01-18T16:45:00Z"},{id:3,name:"Node.js",description:"JavaScript runtime built on Chrome's V8 JavaScript engine for server-side development.",category:"Backend",type:"Runtime",proficiencyLevel:"Expert",yearsOfExperience:6,projectsUsed:38,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg",website:"https://nodejs.org",documentation:"https://nodejs.org/docs",isActive:!0,isFeatured:!0,tags:["JavaScript","Server","API","Microservices"],createdAt:"2024-01-10T09:00:00Z",updatedAt:"2024-01-15T13:20:00Z"},{id:4,name:"TypeScript",description:"Typed superset of JavaScript that compiles to plain JavaScript for better development experience.",category:"Language",type:"Programming Language",proficiencyLevel:"Expert",yearsOfExperience:4,projectsUsed:42,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg",website:"https://www.typescriptlang.org",documentation:"https://www.typescriptlang.org/docs",isActive:!0,isFeatured:!0,tags:["JavaScript","Type Safety","Development"],createdAt:"2024-01-08T14:00:00Z",updatedAt:"2024-01-12T10:15:00Z"},{id:5,name:"Python",description:"High-level programming language known for its simplicity and versatility in various domains.",category:"Language",type:"Programming Language",proficiencyLevel:"Advanced",yearsOfExperience:7,projectsUsed:28,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg",website:"https://www.python.org",documentation:"https://docs.python.org",isActive:!0,isFeatured:!1,tags:["AI/ML","Data Science","Backend","Automation"],createdAt:"2024-01-05T16:00:00Z",updatedAt:"2024-01-10T12:30:00Z"},{id:6,name:"PostgreSQL",description:"Advanced open-source relational database with strong standards compliance and extensibility.",category:"Database",type:"Database",proficiencyLevel:"Advanced",yearsOfExperience:5,projectsUsed:35,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg",website:"https://www.postgresql.org",documentation:"https://www.postgresql.org/docs",isActive:!0,isFeatured:!1,tags:["SQL","ACID","Scalable","Open Source"],createdAt:"2024-01-03T12:00:00Z",updatedAt:"2024-01-08T15:45:00Z"},{id:7,name:"AWS",description:"Amazon Web Services cloud computing platform offering a wide range of infrastructure services.",category:"Cloud",type:"Platform",proficiencyLevel:"Advanced",yearsOfExperience:4,projectsUsed:25,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg",website:"https://aws.amazon.com",documentation:"https://docs.aws.amazon.com",isActive:!0,isFeatured:!0,tags:["Cloud","Infrastructure","Scalable","DevOps"],createdAt:"2024-01-01T10:00:00Z",updatedAt:"2024-01-05T11:20:00Z"},{id:8,name:"Docker",description:"Platform for developing, shipping, and running applications using containerization technology.",category:"DevOps",type:"Tool",proficiencyLevel:"Advanced",yearsOfExperience:3,projectsUsed:30,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg",website:"https://www.docker.com",documentation:"https://docs.docker.com",isActive:!0,isFeatured:!1,tags:["Containerization","DevOps","Deployment"],createdAt:"2023-12-28T14:00:00Z",updatedAt:"2024-01-03T09:15:00Z"}],h=["All","Frontend","Backend","Language","Database","Cloud","DevOps"],g=["All","Beginner","Intermediate","Advanced","Expert"],p=e=>{switch(e){case"Expert":return"bg-green-100 text-green-800";case"Advanced":return"bg-blue-100 text-blue-800";case"Intermediate":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};function f(){let[e,t]=(0,a.useState)(u),[s,f]=(0,a.useState)(""),[v,j]=(0,a.useState)("All"),[y,b]=(0,a.useState)("All"),[w,N]=(0,a.useState)(null),[k,A]=(0,a.useState)(!1),L=e.filter(e=>{let t=e.name.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(s.toLowerCase())),r="All"===v||e.category===v,a="All"===y||e.proficiencyLevel===y;return t&&r&&a}),E=e=>{t(t=>t.map(t=>t.id===e?{...t,isFeatured:!t.isFeatured}:t))},C=e=>{confirm("Are you sure you want to delete this technology?")&&t(t=>t.filter(t=>t.id!==e))};return(0,r.jsx)("div",{className:"py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:[(0,r.jsxs)("div",{className:"md:flex md:items-center md:justify-between mb-8",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate",children:"Technologies"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your technology stack, skills, and expertise levels"})]}),(0,r.jsx)("div",{className:"mt-4 flex md:mt-0 md:ml-4",children:(0,r.jsxs)("button",{onClick:()=>A(!0),className:"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(n.A,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Technology"]})})]}),(0,r.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:e.length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Technologies"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"In Stack"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:e.filter(e=>"Expert"===e.proficiencyLevel).length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Expert Level"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Mastered"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:e.filter(e=>e.isFeatured).length})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Featured"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Highlighted"})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold",children:e.reduce((e,t)=>e+t.projectsUsed,0)})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Projects Used"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:"Total"})]})})]})})})]}),(0,r.jsx)("div",{className:"mb-6 bg-white shadow rounded-lg",children:(0,r.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(l.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search technologies...",value:s,onChange:e=>f(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("select",{value:v,onChange:e=>j(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:h.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})}),(0,r.jsx)("div",{children:(0,r.jsx)("select",{value:y,onChange:e=>b(e.target.value),className:"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:g.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})})]})})}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:L.map((e,t)=>(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},className:"bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("img",{src:e.logo,alt:e.name,className:"w-10 h-10 object-contain"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.category})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-1",children:e.isFeatured&&(0,r.jsx)(c.A,{className:"w-5 h-5 text-yellow-400 fill-current"})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Proficiency"}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(p(e.proficiencyLevel)),children:e.proficiencyLevel})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Experience"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.yearsOfExperience," years"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Projects"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.projectsUsed})]})]}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800",children:e},e)),e.tags.length>3&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800",children:["+",e.tags.length-3]})]})}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>N(e),className:"text-gray-400 hover:text-blue-600 transition-colors",title:"View Details",children:(0,r.jsx)(d.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-blue-600 transition-colors",title:"Edit",children:(0,r.jsx)(o.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>E(e.id),className:"text-gray-400 hover:text-yellow-600 transition-colors ".concat(e.isFeatured?"text-yellow-600":""),title:e.isFeatured?"Remove from Featured":"Add to Featured",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>C(e.id),className:"text-gray-400 hover:text-red-600 transition-colors",title:"Delete",children:(0,r.jsx)(m.A,{className:"h-5 w-5"})})]})]})},e.id))}),0===L.length&&(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No technologies found"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Try adjusting your search or filter criteria."})]})}),w&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:()=>N(null)}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full",children:[(0,r.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsx)("div",{className:"sm:flex sm:items-start",children:(0,r.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,r.jsx)("img",{src:w.logo,alt:w.name,className:"w-12 h-12 object-contain"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:w.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[w.category," • ",w.type]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:w.description})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Proficiency Level"}),(0,r.jsx)("span",{className:"mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(p(w.proficiencyLevel)),children:w.proficiencyLevel})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Experience"}),(0,r.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[w.yearsOfExperience," years"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Projects Used"}),(0,r.jsxs)("p",{className:"mt-1 text-sm text-gray-900",children:[w.projectsUsed," projects"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Tags"}),(0,r.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:w.tags.map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800",children:e},e))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Website"}),(0,r.jsx)("a",{href:w.website,target:"_blank",rel:"noopener noreferrer",className:"mt-1 text-sm text-blue-600 hover:text-blue-800",children:w.website})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Documentation"}),(0,r.jsx)("a",{href:w.documentation,target:"_blank",rel:"noopener noreferrer",className:"mt-1 text-sm text-blue-600 hover:text-blue-800",children:"View Docs"})]})]})]})]})})}),(0,r.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm",children:"Edit Technology"}),(0,r.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm",onClick:()=>N(null),children:"Close"})]})]})]})})]})})}},6424:(e,t,s)=>{Promise.resolve().then(s.bind(s,4759))},7305:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})},7765:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},8046:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,8441,1684,7358],()=>t(6424)),_N_E=e.O()}]);