(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{3738:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var a=r(5155),l=r(2115),t=r(2108),d=r(5695),n=r(2177),i=r(8778),o=r(1153),c=r(6874),m=r.n(c);let u=o.z.object({email:o.z.string().email("Invalid email address"),password:o.z.string().min(6,"Password must be at least 6 characters")});function x(){let[e,s]=(0,l.useState)(!1),[r,o]=(0,l.useState)(null),c=(0,d.useRouter)(),{register:x,handleSubmit:p,formState:{errors:h}}=(0,n.mN)({resolver:(0,i.u)(u)}),b=async e=>{s(!0),o(null);try{let s=await (0,t.signIn)("credentials",{email:e.email,password:e.password,redirect:!1});if(null==s?void 0:s.error)o("Invalid email or password");else{var r;let e=await (0,t.getSession)();(null==e||null==(r=e.user)?void 0:r.role)==="ADMIN"?c.push("/admin"):c.push("/")}}catch(e){o("An error occurred. Please try again.")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to Technoloway"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Access your admin dashboard"})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:p(b),children:[(0,a.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,a.jsx)("input",{...x("email"),type:"email",autoComplete:"email",className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Email address"}),h.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.email.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,a.jsx)("input",{...x("password"),type:"password",autoComplete:"current-password",className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Password"}),h.password&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.password.message})]})]}),r&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,a.jsx)("div",{className:"text-sm text-red-700",children:r})}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:e,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:e?"Signing in...":"Sign in"})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(m(),{href:"/",className:"font-medium text-blue-600 hover:text-blue-500",children:"← Back to website"})})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-md",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"Demo Credentials:"}),(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:["Email: <EMAIL>",(0,a.jsx)("br",{}),"Password: admin123"]})]})]})})}},9461:(e,s,r)=>{Promise.resolve().then(r.bind(r,3738))}},e=>{var s=s=>e(e.s=s);e.O(0,[7244,666,2108,8441,1684,7358],()=>s(9461)),_N_E=e.O()}]);