(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8419],{1721:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(5155),i=s(5695),n=s(6874),r=s.n(n),l=s(6408),c=s(2461),o=s(4219),d=s(2771),m=s(8960),h=s(2227),x=s(1890),p=s(9337),g=s(9675),u=s(7911),f=s(3317);let j={"ecommerce-platform":{id:"ecommerce-platform",title:"EcoCommerce Platform",description:"A comprehensive e-commerce platform for sustainable products with advanced filtering, payment integration, and inventory management. The platform was designed to help environmentally conscious consumers find and purchase sustainable products while providing businesses with powerful tools to manage their operations.",shortDescription:"Sustainable e-commerce platform",image:"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=800&fit=crop",gallery:["https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1556742111-a301076d9d18?w=800&h=600&fit=crop"],category:"E-commerce",technologies:["Next.js","TypeScript","Stripe","PostgreSQL","Tailwind CSS","Prisma","AWS S3","Vercel"],client:"GreenTech Solutions",duration:"6 months",teamSize:5,budget:"$45,000",status:"Completed",completedAt:"2024-01-15",liveUrl:"https://ecocommerce-demo.vercel.app",githubUrl:"https://github.com/technoloway/ecommerce-platform",features:["Product catalog with advanced filtering and search","Secure payment processing with Stripe integration","Comprehensive inventory management system","Customer reviews and ratings system","Admin dashboard with analytics","Mobile-responsive design","SEO optimization for better visibility","Multi-language support","Email notification system","Order tracking and management"],results:["200% increase in online sales within 3 months","85% improvement in user engagement metrics","50% reduction in cart abandonment rate","99.9% uptime achieved with robust infrastructure","4.8/5 average customer satisfaction rating","300% increase in mobile conversions"],challenges:[{title:"Complex Product Filtering",description:"Implementing advanced filtering for thousands of products with multiple attributes while maintaining fast search performance.",solution:"Used Elasticsearch for full-text search and implemented efficient database indexing strategies."},{title:"Payment Security",description:"Ensuring PCI compliance and secure payment processing for international transactions.",solution:"Integrated Stripe with additional security layers and implemented fraud detection mechanisms."},{title:"Scalability Requirements",description:"Building a platform that could handle traffic spikes during sales events and seasonal peaks.",solution:"Implemented auto-scaling infrastructure on AWS with CDN optimization and caching strategies."}],testimonial:{content:"Technoloway transformed our vision into reality. The e-commerce platform they built exceeded our expectations and has been instrumental in our 200% growth this year.",author:"Sarah Mitchell",role:"CTO, GreenTech Solutions",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"}},"healthcare-app":{id:"healthcare-app",title:"HealthTracker Mobile App",description:"A comprehensive health tracking mobile application with real-time monitoring, doctor consultations, and personalized health insights. The app connects patients with healthcare providers and provides tools for managing chronic conditions and maintaining overall wellness.",shortDescription:"Health monitoring mobile app",image:"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=1200&h=800&fit=crop",gallery:["https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop","https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=800&h=600&fit=crop"],category:"Healthcare",technologies:["React Native","Node.js","MongoDB","Firebase","Socket.io","Apple HealthKit","Google Fit","WebRTC"],client:"MedTech Innovations",duration:"8 months",teamSize:6,budget:"$65,000",status:"Completed",completedAt:"2024-02-20",liveUrl:"https://apps.apple.com/app/healthtracker",features:["Real-time health monitoring and data sync","Video consultations with healthcare providers","Medication reminders and tracking","Comprehensive health data analytics","Emergency contact system with location sharing","Integration with wearable devices","Secure HIPAA-compliant data storage","Personalized health insights and recommendations","Appointment scheduling and management","Health goal setting and progress tracking"],results:["150% increase in patient engagement","40% reduction in missed appointments","95% user satisfaction rate","500K+ app downloads in first 6 months","30% improvement in medication adherence","25% reduction in emergency room visits"],testimonial:{content:"The HealthTracker app has revolutionized how we connect with our patients. The engagement metrics and health outcomes have improved dramatically.",author:"Dr. Michael Chen",role:"Chief Medical Officer, MedTech Innovations",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"}}};function y(){let e=j[(0,i.useParams)().slug];return e?(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(u.Y,{}),(0,a.jsxs)("main",{className:"pt-20",children:[(0,a.jsx)("section",{className:"py-8 bg-gray-50",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(r(),{href:"/",className:"hover:text-blue-600",children:"Home"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(r(),{href:"/projects",className:"hover:text-blue-600",children:"Projects"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:e.title})]})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6},children:[(0,a.jsxs)(r(),{href:"/projects",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-6 group",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2 transition-transform group-hover:-translate-x-1"}),"Back to Projects"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((e=>{switch(e){case"Completed":return"bg-green-100 text-green-800";case"In Progress":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}})(e.status)),children:e.status}),(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:e.category})]}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:e.title}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:e.shortDescription}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Client"}),(0,a.jsx)("div",{className:"font-semibold text-gray-900",children:e.client})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Duration"}),(0,a.jsx)("div",{className:"font-semibold text-gray-900",children:e.duration})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Budget"}),(0,a.jsx)("div",{className:"font-semibold text-gray-900",children:e.budget})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Completed"}),(0,a.jsx)("div",{className:"font-semibold text-gray-900",children:new Date(e.completedAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.liveUrl&&(0,a.jsxs)("a",{href:e.liveUrl,target:"_blank",rel:"noopener noreferrer",className:"btn-primary inline-flex items-center",children:["View Live Project",(0,a.jsx)(x.A,{className:"ml-2 w-4 h-4"})]}),(0,a.jsx)(r(),{href:"/contact",className:"btn-secondary",children:"Start Similar Project"})]})]}),(0,a.jsx)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"relative",children:(0,a.jsx)("img",{src:e.image,alt:e.title,className:"w-full h-96 object-cover rounded-2xl shadow-lg"})})]})})}),(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Project Overview"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed mb-8",children:e.description}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Technologies Used"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.technologies.map(e=>(0,a.jsx)("span",{className:"px-3 py-1 bg-white rounded-full text-sm font-medium text-gray-700 border border-gray-200",children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Team Size"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-6 h-6 text-blue-600 mr-2"}),(0,a.jsxs)("span",{className:"text-lg font-medium text-gray-900",children:[e.teamSize," developers"]})]})]})]})]})})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Key Features"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Here are the main features and capabilities we implemented for this project."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.features.map((e,t)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"flex items-start p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)(p.A,{className:"w-6 h-6 text-green-500 mr-3 flex-shrink-0 mt-0.5"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},t))})]})}),(0,a.jsx)("section",{className:"py-16 bg-blue-50",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Project Results"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"The measurable impact and success metrics achieved after project completion."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.results.map((e,t)=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-white p-6 rounded-lg shadow-sm border border-blue-100",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(g.A,{className:"w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5"}),(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:e})]})},t))})]})}),e.testimonial&&(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Client Testimonial"}),(0,a.jsxs)("blockquote",{className:"text-2xl font-medium text-gray-700 mb-8 leading-relaxed",children:['"',e.testimonial.content,'"']}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("img",{src:e.testimonial.avatar,alt:e.testimonial.author,className:"w-12 h-12 rounded-full mr-4"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900",children:e.testimonial.author}),(0,a.jsx)("div",{className:"text-gray-600",children:e.testimonial.role})]})]})]})})}),(0,a.jsx)("section",{className:"py-16 bg-gradient-to-r from-blue-600 to-purple-600",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Ready to Start Your Own Project?"}),(0,a.jsx)("p",{className:"text-lg text-blue-100 max-w-3xl mx-auto mb-8",children:"Let's discuss your requirements and create a custom solution that drives your business forward."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(r(),{href:"/contact",className:"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:"Start Your Project"}),(0,a.jsx)(r(),{href:"/projects",className:"inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors",children:"View More Projects"})]})]})})})]}),(0,a.jsx)(f.w,{})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(u.Y,{}),(0,a.jsx)("main",{className:"pt-20",children:(0,a.jsxs)("div",{className:"container py-24 text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Project Not Found"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"The project you're looking for doesn't exist."}),(0,a.jsx)(r(),{href:"/projects",className:"btn-primary",children:"Back to Projects"})]})}),(0,a.jsx)(f.w,{})]})}},9016:(e,t,s)=>{Promise.resolve().then(s.bind(s,1721))}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,7244,3597,2315,8441,1684,7358],()=>t(9016)),_N_E=e.O()}]);