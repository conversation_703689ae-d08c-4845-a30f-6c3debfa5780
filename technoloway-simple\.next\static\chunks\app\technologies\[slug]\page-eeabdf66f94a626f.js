(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6155],{1052:(e,t,s)=>{Promise.resolve().then(s.bind(s,3209))},3209:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(5155),i=s(5695),r=s(6874),n=s.n(r),c=s(6408),l=s(2461),o=s(9675),d=s(2771),x=s(1890),m=s(9337),h=s(7305),p=s(8030),g=s(7911),u=s(3317);let y={react:{id:"react",name:"React",description:"React is a free and open-source front-end JavaScript library for building user interfaces based on UI components. It is maintained by Meta and a community of individual developers and companies. React can be used as a base in the development of single-page, mobile, or server-rendered applications.",category:"Frontend",type:"Framework",proficiencyLevel:"Expert",yearsOfExperience:5,projectsUsed:45,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg",website:"https://reactjs.org",documentation:"https://reactjs.org/docs",github:"https://github.com/facebook/react",isActive:!0,isFeatured:!0,tags:["JavaScript","UI","SPA","Component-based"],useCases:["Single Page Applications (SPAs)","Interactive User Interfaces","Component Libraries","Progressive Web Apps","Mobile Apps (React Native)","Desktop Apps (Electron)"],advantages:["Virtual DOM for optimal performance","Large ecosystem and community support","Reusable component architecture","Strong developer tools and debugging","Backed by Meta (Facebook)","Excellent documentation and learning resources"],keyFeatures:["Component-Based Architecture","Virtual DOM","JSX Syntax","Unidirectional Data Flow","React Hooks","Server-Side Rendering Support"],relatedTechnologies:["Next.js","TypeScript","Redux","React Router"],projects:[{name:"EcoCommerce Platform",description:"E-commerce platform built with React and Next.js",url:"/projects/ecommerce-platform"},{name:"HealthTracker Dashboard",description:"Healthcare analytics dashboard using React",url:"/projects/healthcare-app"}],learningResources:[{title:"Official React Documentation",url:"https://reactjs.org/docs",type:"Documentation"},{title:"React Tutorial for Beginners",url:"https://reactjs.org/tutorial",type:"Tutorial"},{title:"React Patterns",url:"https://reactpatterns.com",type:"Best Practices"}]},nextjs:{id:"nextjs",name:"Next.js",description:"Next.js is a React framework that gives you building blocks to create web applications. By framework, we mean Next.js handles the tooling and configuration needed for React, and provides additional structure, features, and optimizations for your application.",category:"Frontend",type:"Framework",proficiencyLevel:"Expert",yearsOfExperience:4,projectsUsed:32,logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg",website:"https://nextjs.org",documentation:"https://nextjs.org/docs",github:"https://github.com/vercel/next.js",isActive:!0,isFeatured:!0,tags:["React","SSR","SSG","Full-stack"],useCases:["E-commerce Websites","Corporate Websites","Blogs and Content Sites","Web Applications","Landing Pages","API Development"],advantages:["Built-in SEO optimization","Automatic code splitting","API routes for backend logic","Excellent performance out of the box","Zero-config deployment","Built-in CSS and Sass support"],keyFeatures:["Server-Side Rendering (SSR)","Static Site Generation (SSG)","API Routes","Automatic Code Splitting","Built-in CSS Support","Image Optimization"],relatedTechnologies:["React","TypeScript","Vercel","Tailwind CSS"],projects:[{name:"Corporate Website",description:"Modern corporate website with Next.js",url:"/projects/corporate-website"}]}};function j(){let e=y[(0,i.useParams)().slug];return e?(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(g.Y,{}),(0,a.jsxs)("main",{className:"pt-20",children:[(0,a.jsx)("section",{className:"py-8 bg-gray-50",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(n(),{href:"/",className:"hover:text-blue-600",children:"Home"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(n(),{href:"/technologies",className:"hover:text-blue-600",children:"Technologies"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:e.name})]})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6},children:[(0,a.jsxs)(n(),{href:"/technologies",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-6 group",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2 transition-transform group-hover:-translate-x-1"}),"Back to Technologies"]}),(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mr-4",children:(0,a.jsx)("img",{src:e.logo,alt:e.name,className:"w-10 h-10 object-contain"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 mt-2",children:[e.category," • ",e.type]})]})]}),(0,a.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed mb-8",children:e.description}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Proficiency"}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"Expert":return"bg-green-100 text-green-800";case"Advanced":return"bg-blue-100 text-blue-800";case"Intermediate":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(e.proficiencyLevel)),children:e.proficiencyLevel})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Experience"}),(0,a.jsxs)("div",{className:"font-semibold text-gray-900",children:[e.yearsOfExperience," years"]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"btn-primary inline-flex items-center",children:["Official Website",(0,a.jsx)(x.A,{className:"ml-2 w-4 h-4"})]}),(0,a.jsxs)("a",{href:e.documentation,target:"_blank",rel:"noopener noreferrer",className:"btn-secondary inline-flex items-center",children:["Documentation",(0,a.jsx)(x.A,{className:"ml-2 w-4 h-4"})]})]})]}),(0,a.jsx)(c.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"relative",children:(0,a.jsx)("div",{className:"aspect-square bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 flex items-center justify-center",children:(0,a.jsx)("img",{src:e.logo,alt:e.name,className:"w-32 h-32 object-contain"})})})]})})}),(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Key Features"}),(0,a.jsx)("div",{className:"space-y-4",children:e.keyFeatures.map((e,t)=>(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:-10},whileInView:{opacity:1,x:0},transition:{duration:.4,delay:.1*t},viewport:{once:!0},className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-green-500 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},e))})]}),(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Advantages"}),(0,a.jsx)("div",{className:"space-y-4",children:e.advantages.map((e,t)=>(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:-10},whileInView:{opacity:1,x:0},transition:{duration:.4,delay:.1*t},viewport:{once:!0},className:"flex items-start",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-blue-500 mr-3 flex-shrink-0 mt-0.5"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},e))})]})]})})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Use Cases"}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:["Here are the common scenarios where ",e.name," excels and provides the best value."]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.useCases.map((e,t)=>(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-gray-50 p-6 rounded-lg",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsx)(p.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e})]},t))})]})}),e.relatedTechnologies&&(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Related Technologies"}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:["Technologies that work well with ",e.name," in our development stack."]})]}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:e.relatedTechnologies.map((e,t)=>(0,a.jsx)(c.P.span,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},viewport:{once:!0},className:"px-6 py-3 bg-white rounded-full text-gray-700 font-medium border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all",children:e},e))})]})}),e.projects&&e.projects.length>0&&(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,a.jsxs)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:["Projects Using ",e.name]}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:["See how we've successfully implemented ",e.name," in real-world projects."]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:e.projects.map((e,t)=>(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-gray-50 p-6 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,a.jsxs)(n(),{href:e.url,className:"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium",children:["View Project",(0,a.jsx)(x.A,{className:"ml-1 h-4 w-4"})]})]},t))})]})}),(0,a.jsx)("section",{className:"py-16 bg-gradient-to-r from-blue-600 to-purple-600",children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center",children:[(0,a.jsxs)("h2",{className:"text-3xl font-bold text-white mb-4",children:["Ready to Build with ",e.name,"?"]}),(0,a.jsxs)("p",{className:"text-lg text-blue-100 max-w-3xl mx-auto mb-8",children:["Let's discuss how ",e.name," can help bring your project to life. Our experts are ready to guide you through the development process."]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(n(),{href:"/contact",className:"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:"Start Your Project"}),(0,a.jsx)(n(),{href:"/technologies",className:"inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors",children:"Explore More Technologies"})]})]})})})]}),(0,a.jsx)(u.w,{})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(g.Y,{}),(0,a.jsx)("main",{className:"pt-20",children:(0,a.jsxs)("div",{className:"container py-24 text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Technology Not Found"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"The technology you're looking for doesn't exist."}),(0,a.jsx)(n(),{href:"/technologies",className:"btn-primary",children:"Back to Technologies"})]})}),(0,a.jsx)(u.w,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6408,7244,8615,2315,8441,1684,7358],()=>t(1052)),_N_E=e.O()}]);