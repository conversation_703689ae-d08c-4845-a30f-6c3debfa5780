# Deployment Guide

This guide covers deploying your Technoloway application to production with various hosting providers.

## Pre-Deployment Checklist

### 1. Environment Configuration
- [ ] Generate secure secrets for production
- [ ] Set up production database (PostgreSQL)
- [ ] Configure email service (SMTP)
- [ ] Set up analytics (Google Analytics)
- [ ] Configure OAuth providers (optional)
- [ ] Set up monitoring and error tracking

### 2. Security Review
- [ ] Update all default passwords
- [ ] Review and secure API endpoints
- [ ] Enable HTTPS/SSL certificates
- [ ] Configure CORS policies
- [ ] Set up rate limiting
- [ ] Review file upload security

### 3. Performance Optimization
- [ ] Optimize images and assets
- [ ] Configure CDN (if needed)
- [ ] Set up database connection pooling
- [ ] Enable caching strategies
- [ ] Optimize bundle size

## Deployment Options

### Option 1: Vercel (Recommended)

Vercel is the recommended platform for Next.js applications.

#### Setup Steps:

1. **Install Vercel CLI**:
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy**:
   ```bash
   vercel --prod
   ```

#### Environment Variables:
Set these in your Vercel dashboard:

```env
DATABASE_URL=your_production_database_url
NEXTAUTH_SECRET=your_secure_secret
NEXTAUTH_URL=https://yourdomain.com
SMTP_HOST=smtp.gmail.com
SMTP_USER=your_email
SMTP_PASS=your_app_password
NEXT_PUBLIC_GA_ID=your_ga_id
```

#### Custom Domain:
1. Go to Vercel dashboard
2. Select your project
3. Go to Settings > Domains
4. Add your custom domain
5. Configure DNS records

### Option 2: Railway

Railway provides both hosting and managed PostgreSQL.

#### Setup Steps:

1. **Connect GitHub Repository**:
   - Go to [Railway](https://railway.app)
   - Connect your GitHub account
   - Import your repository

2. **Add PostgreSQL Database**:
   - Click "New" > "Database" > "PostgreSQL"
   - Railway will provide a DATABASE_URL

3. **Configure Environment Variables**:
   - Go to your service settings
   - Add all required environment variables

4. **Deploy**:
   - Railway automatically deploys on git push

### Option 3: AWS (Advanced)

For enterprise deployments with full control.

#### Architecture:
- **Compute**: AWS ECS or Lambda
- **Database**: AWS RDS (PostgreSQL)
- **Storage**: AWS S3 for file uploads
- **CDN**: AWS CloudFront
- **Load Balancer**: AWS ALB

#### Setup Steps:

1. **Create RDS PostgreSQL Instance**
2. **Set up ECS Cluster**
3. **Configure Application Load Balancer**
4. **Set up S3 bucket for file uploads**
5. **Configure CloudFront distribution**
6. **Set up CI/CD with AWS CodePipeline**

### Option 4: Docker Deployment

For self-hosted or cloud deployments.

#### Dockerfile:
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose:
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/technoloway
      - NEXTAUTH_SECRET=your-secret
    depends_on:
      - db

  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=technoloway
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

## Database Setup

### Production Database Options:

1. **Supabase** (Recommended for small-medium projects)
   - Free tier available
   - Managed PostgreSQL
   - Built-in auth and real-time features

2. **Railway PostgreSQL**
   - Simple setup
   - Good for development and small production

3. **AWS RDS**
   - Enterprise-grade
   - High availability options
   - Automated backups

4. **Google Cloud SQL**
   - Managed PostgreSQL
   - Good performance
   - Integration with other Google services

### Database Migration:

```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# Seed database (optional)
npx prisma db seed
```

## Environment Variables for Production

### Required Variables:
```env
# Database
DATABASE_URL=postgresql://user:pass@host:port/db

# Authentication
NEXTAUTH_SECRET=secure-random-string-min-32-chars
NEXTAUTH_URL=https://yourdomain.com

# Application
APP_NAME=Technoloway
APP_URL=https://yourdomain.com
ADMIN_EMAIL=<EMAIL>

# Security
JWT_SECRET=another-secure-random-string

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### Optional Variables:
```env
# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# OAuth (if using)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Monitoring
SENTRY_DSN=your-sentry-dsn
```

## SSL/HTTPS Configuration

### Vercel:
- Automatic SSL certificates
- Custom domains supported

### Railway:
- Automatic SSL certificates
- Custom domains in paid plans

### Self-hosted:
- Use Let's Encrypt with Certbot
- Configure reverse proxy (Nginx)

## Performance Optimization

### 1. Database Optimization:
```sql
-- Add indexes for frequently queried fields
CREATE INDEX idx_services_active ON services(is_active);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_blog_posts_published ON blog_posts(is_published, published_at);
```

### 2. Caching Strategy:
- Enable Next.js static generation
- Use Redis for session storage
- Implement API response caching

### 3. CDN Configuration:
- Configure CloudFront or Vercel Edge Network
- Optimize image delivery
- Cache static assets

## Monitoring and Maintenance

### 1. Health Checks:
```bash
# API health check
curl https://yourdomain.com/api/health

# Database connectivity
curl https://yourdomain.com/api/health/db
```

### 2. Monitoring Setup:
- **Uptime Monitoring**: UptimeRobot, Pingdom
- **Error Tracking**: Sentry
- **Performance**: New Relic, DataDog
- **Analytics**: Google Analytics

### 3. Backup Strategy:
- Automated database backups
- File upload backups
- Configuration backups

## Security Considerations

### 1. Environment Security:
- Use environment variables for secrets
- Never commit secrets to git
- Rotate secrets regularly

### 2. Database Security:
- Use SSL connections
- Restrict database access
- Regular security updates

### 3. Application Security:
- Keep dependencies updated
- Regular security audits
- Implement rate limiting

## Troubleshooting

### Common Issues:

1. **Database Connection Errors**:
   - Check DATABASE_URL format
   - Verify database server is running
   - Check firewall settings

2. **Build Failures**:
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check for TypeScript errors

3. **Authentication Issues**:
   - Verify NEXTAUTH_SECRET is set
   - Check OAuth provider configuration
   - Verify callback URLs

### Logs and Debugging:
```bash
# View application logs
vercel logs your-project-name

# Check build logs
vercel inspect your-deployment-url
```

## Post-Deployment Tasks

1. **Test all functionality**:
   - User registration/login
   - Contact forms
   - File uploads
   - Admin dashboard

2. **Set up monitoring**:
   - Configure alerts
   - Set up error tracking
   - Monitor performance metrics

3. **SEO Optimization**:
   - Submit sitemap to Google
   - Configure Google Search Console
   - Set up analytics tracking

4. **Backup Verification**:
   - Test database backups
   - Verify file backup systems
   - Document recovery procedures

## Support and Maintenance

### Regular Tasks:
- [ ] Monitor application performance
- [ ] Review error logs
- [ ] Update dependencies
- [ ] Security patches
- [ ] Database maintenance
- [ ] Backup verification

### Monthly Tasks:
- [ ] Security audit
- [ ] Performance review
- [ ] Cost optimization
- [ ] User feedback review
- [ ] Feature planning

For additional support, refer to the platform-specific documentation and community resources.
