// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  ADMIN
  USER
  CLIENT
}

enum ProjectStatus {
  PLANNING
  IN_PROGRESS
  COMPLETED
  ON_HOLD
  CANCELLED
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ContactStatus {
  NEW
  IN_PROGRESS
  RESOLVED
  CLOSED
}

// Base model for common fields
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String?
  lastName  String?
  imageUrl  String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  blogPosts    BlogPost[]
  contactForms ContactForm[]
  messages     Message[]

  @@map("users")
}

// Categories for services and other content
model Category {
  id           String   @id @default(cuid())
  name         String
  description  String?
  parentId     String?
  isActive     Boolean  @default(true)
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  parent   Category?  @relation("CategoryParent", fields: [parentId], references: [id])
  children Category[] @relation("CategoryParent")
  services Service[]

  @@map("categories")
}

// Services offered by the company
model Service {
  id            String   @id @default(cuid())
  categoryId    String
  name          String
  description   String
  iconClass     String?
  price         Decimal
  discountRate  Int?
  totalDiscount Decimal?
  manager       String?
  isActive      Boolean  @default(true)
  displayOrder  Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  category       Category        @relation(fields: [categoryId], references: [id])
  projects       Project[]
  serviceOptions ServiceOption[]
  orderDetails   OrderDetail[]

  @@map("services")
}

// Service options and features
model ServiceOption {
  id          String   @id @default(cuid())
  serviceId   String
  name        String
  description String?
  price       Decimal?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  service      Service                @relation(fields: [serviceId], references: [id])
  features     ServiceOptionFeature[]
  orderDetails OrderDetail[]

  @@map("service_options")
}

model ServiceOptionFeature {
  id              String   @id @default(cuid())
  serviceOptionId String
  name            String
  description     String?
  isIncluded      Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  serviceOption ServiceOption @relation(fields: [serviceOptionId], references: [id])

  @@map("service_option_features")
}

// Technologies used in projects
model Technology {
  id           String   @id @default(cuid())
  name         String
  description  String?
  iconUrl      String?
  displayOrder Int      @default(0)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  projects Project[]

  @@map("technologies")
}

// Team members
model TeamMember {
  id            String   @id @default(cuid())
  firstName     String
  lastName      String
  position      String
  department    String?
  phone         String
  email         String?
  salary        Decimal?
  payrollMethod String?
  resumeUrl     String?
  notes         String?
  bio           String?
  photoUrl      String?
  linkedInUrl   String?
  twitterUrl    String?
  githubUrl     String?
  displayOrder  Int      @default(0)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  tasks          ProjectTask[]
  payrollRecords PayrollRecord[]

  @@map("team_members")
}

// Clients
model Client {
  id              String   @id @default(cuid())
  userId          String? // Link to User if client has account
  companyName     String
  contactName     String
  contactPosition String?
  contactEmail    String
  contactPhone    String?
  contactFax      String?
  companyWebsite  String?
  address         String
  city            String
  state           String
  zipCode         String
  country         String
  logoUrl         String?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  projects  Project[]
  invoices  Invoice[]
  orders    Order[]
  contracts Contract[]
  feedbacks Feedback[]

  @@map("clients")
}

// Orders
model Order {
  id          String      @id @default(cuid())
  clientId    String
  orderNumber String      @unique
  description String?
  totalAmount Decimal
  status      OrderStatus @default(PENDING)
  orderDate   DateTime    @default(now())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  client       Client        @relation(fields: [clientId], references: [id])
  orderDetails OrderDetail[]
  projects     Project[]
  invoices     Invoice[]
  contracts    Contract[]

  @@map("orders")
}

model OrderDetail {
  id              String   @id @default(cuid())
  orderId         String
  serviceId       String?
  serviceOptionId String?
  quantity        Int      @default(1)
  unitPrice       Decimal
  totalPrice      Decimal
  description     String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  order         Order          @relation(fields: [orderId], references: [id])
  service       Service?       @relation(fields: [serviceId], references: [id])
  serviceOption ServiceOption? @relation(fields: [serviceOptionId], references: [id])

  @@map("order_details")
}

// Projects
model Project {
  id              String        @id @default(cuid())
  orderId         String
  clientId        String?
  name            String
  description     String
  goals           String?
  manager         String?
  startDate       DateTime?
  completionDate  DateTime?
  estimatedCost   Decimal?
  estimatedTime   String?
  estimatedEffort String?
  status          ProjectStatus @default(PLANNING)
  isFeatured      Boolean       @default(false)
  displayOrder    Int           @default(0)
  imageUrl        String?
  projectUrl      String?
  githubUrl       String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  order        Order             @relation(fields: [orderId], references: [id])
  client       Client?           @relation(fields: [clientId], references: [id])
  services     Service[]
  technologies Technology[]
  documents    ProjectDocument[]
  messages     Message[]
  invoices     Invoice[]
  contracts    Contract[]
  tasks        ProjectTask[]
  feedbacks    Feedback[]

  @@map("projects")
}

// Project documents
model ProjectDocument {
  id          String   @id @default(cuid())
  projectId   String
  fileName    String
  filePath    String
  fileSize    Int?
  mimeType    String?
  description String?
  uploadedBy  String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id])

  @@map("project_documents")
}

// Project tasks
model ProjectTask {
  id           String    @id @default(cuid())
  projectId    String
  teamMemberId String
  taskId       Int // Original TaskID from ASP.NET
  description  String?
  startDate    DateTime?
  endDate      DateTime?
  workHours    Int?
  payRate      Decimal?
  status       String?
  notes        String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  project    Project    @relation(fields: [projectId], references: [id])
  teamMember TeamMember @relation(fields: [teamMemberId], references: [id])

  @@map("project_tasks")
}

// Messages for project communication
model Message {
  id         String    @id @default(cuid())
  projectId  String
  senderId   String?
  senderName String
  senderRole String // Admin, Client
  content    String
  isRead     Boolean   @default(false)
  readAt     DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id])
  sender  User?   @relation(fields: [senderId], references: [id])

  @@map("messages")
}

// Contracts
model Contract {
  id          String    @id @default(cuid())
  clientId    String
  projectId   String?
  orderId     String?
  title       String
  content     String
  startDate   DateTime
  endDate     DateTime?
  totalAmount Decimal
  status      String    @default("DRAFT")
  signedAt    DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  client   Client    @relation(fields: [clientId], references: [id])
  project  Project?  @relation(fields: [projectId], references: [id])
  order    Order?    @relation(fields: [orderId], references: [id])
  invoices Invoice[]

  @@map("contracts")
}

// Invoices
model Invoice {
  id            String        @id @default(cuid())
  clientId      String
  projectId     String?
  orderId       String?
  contractId    String?
  invoiceNumber String        @unique
  description   String?
  subtotal      Decimal
  taxAmount     Decimal       @default(0)
  totalAmount   Decimal
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime      @default(now())
  dueDate       DateTime
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  client   Client        @relation(fields: [clientId], references: [id])
  project  Project?      @relation(fields: [projectId], references: [id])
  order    Order?        @relation(fields: [orderId], references: [id])
  contract Contract?     @relation(fields: [contractId], references: [id])
  items    InvoiceItem[]
  payments Payment[]

  @@map("invoices")
}

model InvoiceItem {
  id          String   @id @default(cuid())
  invoiceId   String
  description String
  quantity    Int      @default(1)
  unitPrice   Decimal
  totalPrice  Decimal
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  invoice Invoice @relation(fields: [invoiceId], references: [id])

  @@map("invoice_items")
}

// Payments
model Payment {
  id            String        @id @default(cuid())
  invoiceId     String
  amount        Decimal
  paymentMethod String
  status        PaymentStatus @default(PENDING)
  transactionId String?
  notes         String?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  invoice Invoice @relation(fields: [invoiceId], references: [id])

  @@map("payments")
}

// Blog posts
model BlogPost {
  id               String    @id @default(cuid())
  authorId         String
  title            String
  slug             String    @unique
  content          String
  excerpt          String?
  featuredImageUrl String?
  isPublished      Boolean   @default(false)
  publishedAt      DateTime?
  categories       String? // Comma-separated categories
  tags             String? // Comma-separated tags
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relations
  author User @relation(fields: [authorId], references: [id])

  @@map("blog_posts")
}

// Testimonials
model Testimonial {
  id             String   @id @default(cuid())
  clientName     String
  clientTitle    String
  clientCompany  String
  clientPhotoUrl String?
  content        String
  rating         Int      @default(5)
  isFeatured     Boolean  @default(false)
  displayOrder   Int      @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("testimonials")
}

// Contact forms
model ContactForm {
  id        String        @id @default(cuid())
  userId    String?
  name      String
  email     String
  phone     String?
  company   String?
  subject   String
  message   String
  status    ContactStatus @default(NEW)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@map("contact_forms")
}

// Job listings
model JobListing {
  id             String    @id @default(cuid())
  title          String
  description    String
  requirements   String
  location       String
  employmentType String // Full-time, Part-time, Contract, etc.
  salaryMin      Decimal?
  salaryMax      Decimal?
  salaryCurrency String    @default("USD")
  isRemote       Boolean   @default(false)
  isActive       Boolean   @default(true)
  expiresAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  applications JobApplication[]

  @@map("job_listings")
}

model JobApplication {
  id             String   @id @default(cuid())
  jobListingId   String
  applicantName  String
  applicantEmail String
  applicantPhone String?
  resumeUrl      String?
  coverLetter    String?
  status         String   @default("PENDING")
  appliedAt      DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  jobListing JobListing @relation(fields: [jobListingId], references: [id])

  @@map("job_applications")
}

// Site settings
model SiteSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  icon        String?
  category    String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("site_settings")
}

// Feedback
model Feedback {
  id        String   @id @default(cuid())
  clientId  String?
  projectId String?
  rating    Int
  comment   String
  isPublic  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  client  Client?  @relation(fields: [clientId], references: [id])
  project Project? @relation(fields: [projectId], references: [id])

  @@map("feedbacks")
}

// Legal pages (Privacy Policy, Terms of Service, etc.)
model LegalPage {
  id           String   @id @default(cuid())
  title        String
  slug         String   @unique
  content      String
  isActive     Boolean  @default(true)
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  sections LegalPageSection[]

  @@map("legal_pages")
}

model LegalPageSection {
  id           String   @id @default(cuid())
  legalPageId  String
  title        String
  content      String
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  legalPage LegalPage @relation(fields: [legalPageId], references: [id])

  @@map("legal_page_sections")
}

// About pages
model AboutPage {
  id        String   @id @default(cuid())
  title     String
  content   String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  sections AboutPageSection[]

  @@map("about_pages")
}

model AboutPageSection {
  id           String   @id @default(cuid())
  aboutPageId  String
  title        String
  content      String
  imageUrl     String?
  displayOrder Int      @default(0)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  aboutPage AboutPage @relation(fields: [aboutPageId], references: [id])

  @@map("about_page_sections")
}

// Hero sections for homepage
model HeroSection {
  id          String   @id @default(cuid())
  pageName    String   @default("Home")
  title       String
  subtitle    String?
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  slides HeroSlide[]

  @@map("hero_sections")
}

model HeroSlide {
  id            String   @id @default(cuid())
  heroSectionId String
  title         String
  subtitle      String?
  description   String?
  imageUrl      String?
  buttonText    String?
  buttonUrl     String?
  displayOrder  Int      @default(0)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  heroSection HeroSection @relation(fields: [heroSectionId], references: [id])

  @@map("hero_slides")
}

// Payroll records
model PayrollRecord {
  id           String   @id @default(cuid())
  teamMemberId String
  payPeriod    String
  baseSalary   Decimal
  overtime     Decimal  @default(0)
  bonuses      Decimal  @default(0)
  deductions   Decimal  @default(0)
  netPay       Decimal
  payDate      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  teamMember TeamMember @relation(fields: [teamMemberId], references: [id])

  @@map("payroll_records")
}

// Data upload logs
model DataUploadLog {
  id          String   @id @default(cuid())
  fileName    String
  entityType  String
  operation   String // INSERT, UPDATE, UPSERT, TRUNCATE
  recordCount Int
  status      String // SUCCESS, FAILED, PARTIAL
  errorLog    String?
  uploadedBy  String?
  createdAt   DateTime @default(now())

  @@map("data_upload_logs")
}

// Chatbot system
model ChatbotIntent {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  priority    Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  keywords  ChatbotKeyword[]
  responses ChatbotResponse[]

  @@map("chatbot_intents")
}

model ChatbotKeyword {
  id        String   @id @default(cuid())
  intentId  String
  keyword   String
  synonyms  String? // Comma-separated synonyms
  weight    Int      @default(1)
  matchType String   @default("contains") // exact, contains, starts_with, ends_with
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  intent ChatbotIntent @relation(fields: [intentId], references: [id])

  @@map("chatbot_keywords")
}

model ChatbotResponse {
  id           String   @id @default(cuid())
  intentId     String
  message      String
  responseType String   @default("text") // text, card, quick_reply
  isActive     Boolean  @default(true)
  priority     Int      @default(1)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  intent       ChatbotIntent        @relation(fields: [intentId], references: [id])
  quickActions ChatbotQuickAction[]

  @@map("chatbot_responses")
}

model ChatbotQuickAction {
  id         String   @id @default(cuid())
  responseId String
  label      String
  action     String // url, intent, postback
  value      String
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  response ChatbotResponse @relation(fields: [responseId], references: [id])

  @@map("chatbot_quick_actions")
}
