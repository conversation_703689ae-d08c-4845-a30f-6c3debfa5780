
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  emailVerified: 'emailVerified',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  imageUrl: 'imageUrl',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  parentId: 'parentId',
  isActive: 'isActive',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceScalarFieldEnum = {
  id: 'id',
  categoryId: 'categoryId',
  name: 'name',
  description: 'description',
  iconClass: 'iconClass',
  price: 'price',
  discountRate: 'discountRate',
  totalDiscount: 'totalDiscount',
  manager: 'manager',
  isActive: 'isActive',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceOptionScalarFieldEnum = {
  id: 'id',
  serviceId: 'serviceId',
  name: 'name',
  description: 'description',
  price: 'price',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceOptionFeatureScalarFieldEnum = {
  id: 'id',
  serviceOptionId: 'serviceOptionId',
  name: 'name',
  description: 'description',
  isIncluded: 'isIncluded',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TechnologyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  iconUrl: 'iconUrl',
  displayOrder: 'displayOrder',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TeamMemberScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  position: 'position',
  department: 'department',
  phone: 'phone',
  email: 'email',
  salary: 'salary',
  payrollMethod: 'payrollMethod',
  resumeUrl: 'resumeUrl',
  notes: 'notes',
  bio: 'bio',
  photoUrl: 'photoUrl',
  linkedInUrl: 'linkedInUrl',
  twitterUrl: 'twitterUrl',
  githubUrl: 'githubUrl',
  displayOrder: 'displayOrder',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ClientScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyName: 'companyName',
  contactName: 'contactName',
  contactPosition: 'contactPosition',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  contactFax: 'contactFax',
  companyWebsite: 'companyWebsite',
  address: 'address',
  city: 'city',
  state: 'state',
  zipCode: 'zipCode',
  country: 'country',
  logoUrl: 'logoUrl',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  orderNumber: 'orderNumber',
  description: 'description',
  totalAmount: 'totalAmount',
  status: 'status',
  orderDate: 'orderDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderDetailScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  serviceId: 'serviceId',
  serviceOptionId: 'serviceOptionId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  clientId: 'clientId',
  name: 'name',
  description: 'description',
  goals: 'goals',
  manager: 'manager',
  startDate: 'startDate',
  completionDate: 'completionDate',
  estimatedCost: 'estimatedCost',
  estimatedTime: 'estimatedTime',
  estimatedEffort: 'estimatedEffort',
  status: 'status',
  isFeatured: 'isFeatured',
  displayOrder: 'displayOrder',
  imageUrl: 'imageUrl',
  projectUrl: 'projectUrl',
  githubUrl: 'githubUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectDocumentScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  description: 'description',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectTaskScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  teamMemberId: 'teamMemberId',
  taskId: 'taskId',
  description: 'description',
  startDate: 'startDate',
  endDate: 'endDate',
  workHours: 'workHours',
  payRate: 'payRate',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  senderId: 'senderId',
  senderName: 'senderName',
  senderRole: 'senderRole',
  content: 'content',
  isRead: 'isRead',
  readAt: 'readAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ContractScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  projectId: 'projectId',
  orderId: 'orderId',
  title: 'title',
  content: 'content',
  startDate: 'startDate',
  endDate: 'endDate',
  totalAmount: 'totalAmount',
  status: 'status',
  signedAt: 'signedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  projectId: 'projectId',
  orderId: 'orderId',
  contractId: 'contractId',
  invoiceNumber: 'invoiceNumber',
  description: 'description',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  status: 'status',
  issueDate: 'issueDate',
  dueDate: 'dueDate',
  paidAt: 'paidAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceItemScalarFieldEnum = {
  id: 'id',
  invoiceId: 'invoiceId',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  invoiceId: 'invoiceId',
  amount: 'amount',
  paymentMethod: 'paymentMethod',
  status: 'status',
  transactionId: 'transactionId',
  notes: 'notes',
  paidAt: 'paidAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BlogPostScalarFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  title: 'title',
  slug: 'slug',
  content: 'content',
  excerpt: 'excerpt',
  featuredImageUrl: 'featuredImageUrl',
  isPublished: 'isPublished',
  publishedAt: 'publishedAt',
  categories: 'categories',
  tags: 'tags',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TestimonialScalarFieldEnum = {
  id: 'id',
  clientName: 'clientName',
  clientTitle: 'clientTitle',
  clientCompany: 'clientCompany',
  clientPhotoUrl: 'clientPhotoUrl',
  content: 'content',
  rating: 'rating',
  isFeatured: 'isFeatured',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ContactFormScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  email: 'email',
  phone: 'phone',
  company: 'company',
  subject: 'subject',
  message: 'message',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobListingScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  requirements: 'requirements',
  location: 'location',
  employmentType: 'employmentType',
  salaryMin: 'salaryMin',
  salaryMax: 'salaryMax',
  salaryCurrency: 'salaryCurrency',
  isRemote: 'isRemote',
  isActive: 'isActive',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobApplicationScalarFieldEnum = {
  id: 'id',
  jobListingId: 'jobListingId',
  applicantName: 'applicantName',
  applicantEmail: 'applicantEmail',
  applicantPhone: 'applicantPhone',
  resumeUrl: 'resumeUrl',
  coverLetter: 'coverLetter',
  status: 'status',
  appliedAt: 'appliedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SiteSettingScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  icon: 'icon',
  category: 'category',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeedbackScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  projectId: 'projectId',
  rating: 'rating',
  comment: 'comment',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LegalPageScalarFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  isActive: 'isActive',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LegalPageSectionScalarFieldEnum = {
  id: 'id',
  legalPageId: 'legalPageId',
  title: 'title',
  content: 'content',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AboutPageScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AboutPageSectionScalarFieldEnum = {
  id: 'id',
  aboutPageId: 'aboutPageId',
  title: 'title',
  content: 'content',
  imageUrl: 'imageUrl',
  displayOrder: 'displayOrder',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HeroSectionScalarFieldEnum = {
  id: 'id',
  pageName: 'pageName',
  title: 'title',
  subtitle: 'subtitle',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HeroSlideScalarFieldEnum = {
  id: 'id',
  heroSectionId: 'heroSectionId',
  title: 'title',
  subtitle: 'subtitle',
  description: 'description',
  imageUrl: 'imageUrl',
  buttonText: 'buttonText',
  buttonUrl: 'buttonUrl',
  displayOrder: 'displayOrder',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PayrollRecordScalarFieldEnum = {
  id: 'id',
  teamMemberId: 'teamMemberId',
  payPeriod: 'payPeriod',
  baseSalary: 'baseSalary',
  overtime: 'overtime',
  bonuses: 'bonuses',
  deductions: 'deductions',
  netPay: 'netPay',
  payDate: 'payDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DataUploadLogScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  entityType: 'entityType',
  operation: 'operation',
  recordCount: 'recordCount',
  status: 'status',
  errorLog: 'errorLog',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt'
};

exports.Prisma.ChatbotIntentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  priority: 'priority',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatbotKeywordScalarFieldEnum = {
  id: 'id',
  intentId: 'intentId',
  keyword: 'keyword',
  synonyms: 'synonyms',
  weight: 'weight',
  matchType: 'matchType',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatbotResponseScalarFieldEnum = {
  id: 'id',
  intentId: 'intentId',
  message: 'message',
  responseType: 'responseType',
  isActive: 'isActive',
  priority: 'priority',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChatbotQuickActionScalarFieldEnum = {
  id: 'id',
  responseId: 'responseId',
  label: 'label',
  action: 'action',
  value: 'value',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  USER: 'USER',
  CLIENT: 'CLIENT'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.ProjectStatus = exports.$Enums.ProjectStatus = {
  PLANNING: 'PLANNING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  ON_HOLD: 'ON_HOLD',
  CANCELLED: 'CANCELLED'
};

exports.InvoiceStatus = exports.$Enums.InvoiceStatus = {
  DRAFT: 'DRAFT',
  SENT: 'SENT',
  PAID: 'PAID',
  OVERDUE: 'OVERDUE',
  CANCELLED: 'CANCELLED'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED'
};

exports.ContactStatus = exports.$Enums.ContactStatus = {
  NEW: 'NEW',
  IN_PROGRESS: 'IN_PROGRESS',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  Category: 'Category',
  Service: 'Service',
  ServiceOption: 'ServiceOption',
  ServiceOptionFeature: 'ServiceOptionFeature',
  Technology: 'Technology',
  TeamMember: 'TeamMember',
  Client: 'Client',
  Order: 'Order',
  OrderDetail: 'OrderDetail',
  Project: 'Project',
  ProjectDocument: 'ProjectDocument',
  ProjectTask: 'ProjectTask',
  Message: 'Message',
  Contract: 'Contract',
  Invoice: 'Invoice',
  InvoiceItem: 'InvoiceItem',
  Payment: 'Payment',
  BlogPost: 'BlogPost',
  Testimonial: 'Testimonial',
  ContactForm: 'ContactForm',
  JobListing: 'JobListing',
  JobApplication: 'JobApplication',
  SiteSetting: 'SiteSetting',
  Feedback: 'Feedback',
  LegalPage: 'LegalPage',
  LegalPageSection: 'LegalPageSection',
  AboutPage: 'AboutPage',
  AboutPageSection: 'AboutPageSection',
  HeroSection: 'HeroSection',
  HeroSlide: 'HeroSlide',
  PayrollRecord: 'PayrollRecord',
  DataUploadLog: 'DataUploadLog',
  ChatbotIntent: 'ChatbotIntent',
  ChatbotKeyword: 'ChatbotKeyword',
  ChatbotResponse: 'ChatbotResponse',
  ChatbotQuickAction: 'ChatbotQuickAction'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
