import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create admin user
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
    },
  })

  console.log('✅ Created admin user')

  // Create categories
  const webDevCategory = await prisma.category.upsert({
    where: { id: 'web-development' },
    update: {},
    create: {
      id: 'web-development',
      name: 'Web Development',
      description: 'Custom web application development services',
      isActive: true,
      displayOrder: 1,
    },
  })

  const mobileDevCategory = await prisma.category.upsert({
    where: { id: 'mobile-development' },
    update: {},
    create: {
      id: 'mobile-development',
      name: 'Mobile Development',
      description: 'iOS and Android mobile application development',
      isActive: true,
      displayOrder: 2,
    },
  })

  const consultingCategory = await prisma.category.upsert({
    where: { id: 'consulting' },
    update: {},
    create: {
      id: 'consulting',
      name: 'Consulting',
      description: 'Technology consulting and strategy services',
      isActive: true,
      displayOrder: 3,
    },
  })

  console.log('✅ Created categories')

  // Create services
  const services = [
    {
      id: 'custom-web-app',
      categoryId: webDevCategory.id,
      name: 'Custom Web Application',
      description: 'Full-stack web application development using modern technologies',
      iconClass: 'fas fa-code',
      price: 15000,
      isActive: true,
      displayOrder: 1,
    },
    {
      id: 'ecommerce-platform',
      categoryId: webDevCategory.id,
      name: 'E-commerce Platform',
      description: 'Complete e-commerce solution with payment integration',
      iconClass: 'fas fa-shopping-cart',
      price: 25000,
      isActive: true,
      displayOrder: 2,
    },
    {
      id: 'mobile-app-ios',
      categoryId: mobileDevCategory.id,
      name: 'iOS Mobile App',
      description: 'Native iOS application development',
      iconClass: 'fab fa-apple',
      price: 20000,
      isActive: true,
      displayOrder: 3,
    },
    {
      id: 'mobile-app-android',
      categoryId: mobileDevCategory.id,
      name: 'Android Mobile App',
      description: 'Native Android application development',
      iconClass: 'fab fa-android',
      price: 18000,
      isActive: true,
      displayOrder: 4,
    },
    {
      id: 'tech-consulting',
      categoryId: consultingCategory.id,
      name: 'Technology Consulting',
      description: 'Strategic technology consulting and architecture planning',
      iconClass: 'fas fa-lightbulb',
      price: 200,
      isActive: true,
      displayOrder: 5,
    },
  ]

  for (const service of services) {
    await prisma.service.upsert({
      where: { id: service.id },
      update: {},
      create: service,
    })
  }

  console.log('✅ Created services')

  // Create technologies
  const technologies = [
    {
      id: 'nextjs',
      name: 'Next.js',
      description: 'React framework for production',
      iconUrl: '/icons/nextjs.svg',
      displayOrder: 1,
    },
    {
      id: 'react',
      name: 'React',
      description: 'JavaScript library for building user interfaces',
      iconUrl: '/icons/react.svg',
      displayOrder: 2,
    },
    {
      id: 'typescript',
      name: 'TypeScript',
      description: 'Typed superset of JavaScript',
      iconUrl: '/icons/typescript.svg',
      displayOrder: 3,
    },
    {
      id: 'nodejs',
      name: 'Node.js',
      description: 'JavaScript runtime built on Chrome\'s V8 JavaScript engine',
      iconUrl: '/icons/nodejs.svg',
      displayOrder: 4,
    },
    {
      id: 'postgresql',
      name: 'PostgreSQL',
      description: 'Advanced open source relational database',
      iconUrl: '/icons/postgresql.svg',
      displayOrder: 5,
    },
    {
      id: 'prisma',
      name: 'Prisma',
      description: 'Next-generation ORM for Node.js and TypeScript',
      iconUrl: '/icons/prisma.svg',
      displayOrder: 6,
    },
  ]

  for (const tech of technologies) {
    await prisma.technology.upsert({
      where: { id: tech.id },
      update: {},
      create: tech,
    })
  }

  console.log('✅ Created technologies')

  // Create team members
  const teamMembers = [
    {
      id: 'john-doe',
      firstName: 'John',
      lastName: 'Doe',
      position: 'Senior Full-Stack Developer',
      department: 'Development',
      phone: '555-0101',
      email: '<EMAIL>',
      bio: 'Experienced full-stack developer with expertise in React, Node.js, and cloud technologies.',
      displayOrder: 1,
    },
    {
      id: 'jane-smith',
      firstName: 'Jane',
      lastName: 'Smith',
      position: 'UI/UX Designer',
      department: 'Design',
      phone: '555-0102',
      email: '<EMAIL>',
      bio: 'Creative designer focused on user experience and modern interface design.',
      displayOrder: 2,
    },
    {
      id: 'mike-johnson',
      firstName: 'Mike',
      lastName: 'Johnson',
      position: 'DevOps Engineer',
      department: 'Operations',
      phone: '555-0103',
      email: '<EMAIL>',
      bio: 'DevOps specialist with expertise in cloud infrastructure and CI/CD pipelines.',
      displayOrder: 3,
    },
  ]

  for (const member of teamMembers) {
    await prisma.teamMember.upsert({
      where: { id: member.id },
      update: {},
      create: member,
    })
  }

  console.log('✅ Created team members')

  // Create sample client
  const sampleClient = await prisma.client.upsert({
    where: { id: 'acme-corp' },
    update: {},
    create: {
      id: 'acme-corp',
      companyName: 'Acme Corporation',
      contactName: 'Alice Johnson',
      contactEmail: '<EMAIL>',
      contactPhone: '555-0200',
      address: '123 Business St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA',
    },
  })

  console.log('✅ Created sample client')

  // Create sample order
  const sampleOrder = await prisma.order.upsert({
    where: { id: 'order-001' },
    update: {},
    create: {
      id: 'order-001',
      clientId: sampleClient.id,
      orderNumber: 'ORD-2024-001',
      description: 'Custom web application development',
      totalAmount: 15000,
      status: 'CONFIRMED',
    },
  })

  console.log('✅ Created sample order')

  // Create sample project
  const sampleProject = await prisma.project.upsert({
    where: { id: 'project-001' },
    update: {},
    create: {
      id: 'project-001',
      orderId: sampleOrder.id,
      clientId: sampleClient.id,
      name: 'Acme Corp Web Platform',
      description: 'Modern web platform for Acme Corporation with user management and analytics',
      goals: 'Create a scalable web platform to improve customer engagement',
      status: 'IN_PROGRESS',
      estimatedCost: 15000,
      estimatedTime: '3 months',
      isFeatured: true,
      displayOrder: 1,
    },
  })

  console.log('✅ Created sample project')

  // Create testimonials
  const testimonials = [
    {
      id: 'testimonial-1',
      clientName: 'Alice Johnson',
      clientTitle: 'CTO',
      clientCompany: 'Acme Corporation',
      content: 'Technoloway delivered an exceptional web platform that exceeded our expectations. Their team was professional, responsive, and delivered on time.',
      rating: 5,
      isFeatured: true,
      displayOrder: 1,
    },
    {
      id: 'testimonial-2',
      clientName: 'Bob Wilson',
      clientTitle: 'CEO',
      clientCompany: 'StartupXYZ',
      content: 'The mobile app they built for us has been a game-changer for our business. Highly recommend their services!',
      rating: 5,
      isFeatured: true,
      displayOrder: 2,
    },
  ]

  for (const testimonial of testimonials) {
    await prisma.testimonial.upsert({
      where: { id: testimonial.id },
      update: {},
      create: testimonial,
    })
  }

  console.log('✅ Created testimonials')

  // Create blog posts
  const blogPosts = [
    {
      id: 'blog-1',
      authorId: adminUser.id,
      title: 'The Future of Web Development',
      slug: 'future-of-web-development',
      content: 'Web development is constantly evolving with new technologies and frameworks...',
      excerpt: 'Exploring the latest trends and technologies shaping the future of web development.',
      isPublished: true,
      publishedAt: new Date(),
    },
    {
      id: 'blog-2',
      authorId: adminUser.id,
      title: 'Building Scalable Applications with Next.js',
      slug: 'building-scalable-applications-nextjs',
      content: 'Next.js has become one of the most popular React frameworks for building modern web applications...',
      excerpt: 'Learn how to build scalable and performant applications using Next.js.',
      isPublished: true,
      publishedAt: new Date(),
    },
  ]

  for (const post of blogPosts) {
    await prisma.blogPost.upsert({
      where: { id: post.id },
      update: {},
      create: post,
    })
  }

  console.log('✅ Created blog posts')

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
