import { createMocks } from 'node-mocks-http'
import { GET, POST } from '@/app/api/services/route'
import { prisma } from '@/lib/prisma'

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    service: {
      count: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      updateMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    category: {
      findUnique: jest.fn(),
    },
  },
}))

// Mock auth
jest.mock('@/lib/api-utils', () => ({
  ...jest.requireActual('@/lib/api-utils'),
  requireAdmin: jest.fn().mockResolvedValue({
    id: 'admin-id',
    email: '<EMAIL>',
    role: 'ADMIN',
  }),
}))

describe('/api/services', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/services', () => {
    it('should return paginated services', async () => {
      const mockServices = [
        {
          id: '1',
          name: 'Web Development',
          description: 'Custom web applications',
          price: 5000,
          category: { id: 'cat1', name: 'Development' },
          _count: { projects: 5, orderDetails: 3 },
        },
      ]

      ;(prisma.service.count as jest.Mock).mockResolvedValue(1)
      ;(prisma.service.findMany as jest.Mock).mockResolvedValue(mockServices)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/services?page=1&limit=10',
      })

      const response = await GET(req as any)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockServices)
      expect(data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      })
    })

    it('should handle search queries', async () => {
      ;(prisma.service.count as jest.Mock).mockResolvedValue(0)
      ;(prisma.service.findMany as jest.Mock).mockResolvedValue([])

      const { req } = createMocks({
        method: 'GET',
        url: '/api/services?search=web',
      })

      await GET(req as any)

      expect(prisma.service.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              { name: { contains: 'web', mode: 'insensitive' } },
              { description: { contains: 'web', mode: 'insensitive' } },
            ]),
          }),
        })
      )
    })
  })

  describe('POST /api/services', () => {
    it('should create a new service', async () => {
      const newService = {
        categoryId: 'cat1',
        name: 'Mobile App Development',
        description: 'iOS and Android apps',
        price: 8000,
      }

      const mockCategory = { id: 'cat1', name: 'Development' }
      const mockCreatedService = {
        id: 'service1',
        ...newService,
        category: mockCategory,
      }

      ;(prisma.category.findUnique as jest.Mock).mockResolvedValue(mockCategory)
      ;(prisma.service.create as jest.Mock).mockResolvedValue(mockCreatedService)

      const { req } = createMocks({
        method: 'POST',
        body: newService,
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data).toEqual(mockCreatedService)
      expect(data.message).toBe('Service created successfully')
    })

    it('should return error if category not found', async () => {
      const newService = {
        categoryId: 'invalid-cat',
        name: 'Test Service',
        description: 'Test description',
        price: 1000,
      }

      ;(prisma.category.findUnique as jest.Mock).mockResolvedValue(null)

      const { req } = createMocks({
        method: 'POST',
        body: newService,
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Category not found')
    })

    it('should validate required fields', async () => {
      const invalidService = {
        name: '', // Invalid: empty name
        description: 'Test description',
        price: -100, // Invalid: negative price
      }

      const { req } = createMocks({
        method: 'POST',
        body: invalidService,
      })

      const response = await POST(req as any)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Validation error')
    })
  })
})

describe('Service validation', () => {
  it('should validate service data correctly', () => {
    const { createServiceSchema } = require('@/lib/validations')

    const validService = {
      categoryId: 'cat1',
      name: 'Web Development',
      description: 'Custom web applications',
      price: 5000,
    }

    const result = createServiceSchema.safeParse(validService)
    expect(result.success).toBe(true)
  })

  it('should reject invalid service data', () => {
    const { createServiceSchema } = require('@/lib/validations')

    const invalidService = {
      categoryId: '', // Invalid: empty
      name: '', // Invalid: empty
      description: 'Test',
      price: -100, // Invalid: negative
    }

    const result = createServiceSchema.safeParse(invalidService)
    expect(result.success).toBe(false)
    expect(result.error?.issues).toHaveLength(3)
  })
})
