import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin,
  generateSlug
} from '@/lib/api-utils'
import { createBlogPostSchema, updateBlogPostSchema } from '@/lib/validations'

// GET /api/blog - List all blog posts with pagination and search
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, ['title', 'content', 'excerpt']))
  }
  
  // Add filter for published/draft posts
  if (filter === 'published') {
    where.isPublished = true
  } else if (filter === 'draft') {
    where.isPublished = false
  }

  // Get total count for pagination
  const total = await prisma.blogPost.count({ where })

  // Get blog posts with pagination
  const posts = await prisma.blogPost.findMany({
    where,
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  return paginatedResponse(posts, page, limit, total)
})

// POST /api/blog - Create a new blog post
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createBlogPostSchema)
  const data = await validate(request)

  // Generate slug if not provided
  if (!data.slug) {
    data.slug = generateSlug(data.title)
  }

  // Check if slug already exists
  const existingPost = await prisma.blogPost.findUnique({
    where: { slug: data.slug },
  })

  if (existingPost) {
    // Generate a unique slug
    let counter = 1
    let newSlug = `${data.slug}-${counter}`
    
    while (await prisma.blogPost.findUnique({ where: { slug: newSlug } })) {
      counter++
      newSlug = `${data.slug}-${counter}`
    }
    
    data.slug = newSlug
  }

  // Check if author exists
  const author = await prisma.user.findUnique({
    where: { id: data.authorId },
  })

  if (!author) {
    throw new Error('Author not found')
  }

  const post = await prisma.blogPost.create({
    data,
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
    },
  })

  return successResponse(post, 'Blog post created successfully', 201)
})

// PUT /api/blog - Bulk update blog posts (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid blog post IDs provided')
  }

  const validate = validateRequest(updateBlogPostSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedPosts = await prisma.blogPost.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedPosts.count },
    `${updatedPosts.count} blog posts updated successfully`
  )
})

// DELETE /api/blog - Bulk delete blog posts (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid blog post IDs provided')
  }

  const deletedPosts = await prisma.blogPost.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedPosts.count },
    `${deletedPosts.count} blog posts deleted successfully`
  )
})
