import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin
} from '@/lib/api-utils'

// GET /api/dashboard/stats - Get dashboard statistics
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  // Get current date for time-based queries
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfYear = new Date(now.getFullYear(), 0, 1)

  // Parallel queries for better performance
  const [
    totalClients,
    totalProjects,
    totalServices,
    totalTeamMembers,
    activeProjects,
    completedProjects,
    pendingInvoices,
    totalRevenue,
    monthlyRevenue,
    recentProjects,
    recentClients,
    projectsByStatus,
    invoicesByStatus,
    topServices,
    upcomingDeadlines,
  ] = await Promise.all([
    // Basic counts
    prisma.client.count(),
    prisma.project.count(),
    prisma.service.count({ where: { isActive: true } }),
    prisma.teamMember.count({ where: { isActive: true } }),
    
    // Project statistics
    prisma.project.count({
      where: { status: { in: ['PLANNING', 'IN_PROGRESS'] } }
    }),
    prisma.project.count({
      where: { status: 'COMPLETED' }
    }),
    
    // Financial statistics
    prisma.invoice.count({
      where: { status: { in: ['SENT', 'OVERDUE'] } }
    }),
    prisma.invoice.aggregate({
      where: { status: 'PAID' },
      _sum: { totalAmount: true }
    }),
    prisma.invoice.aggregate({
      where: { 
        status: 'PAID',
        paidAt: { gte: startOfMonth }
      },
      _sum: { totalAmount: true }
    }),
    
    // Recent data
    prisma.project.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        client: {
          select: {
            companyName: true,
          }
        }
      }
    }),
    prisma.client.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        companyName: true,
        contactName: true,
        contactEmail: true,
        createdAt: true,
      }
    }),
    
    // Analytics data
    prisma.project.groupBy({
      by: ['status'],
      _count: { status: true }
    }),
    prisma.invoice.groupBy({
      by: ['status'],
      _count: { status: true },
      _sum: { totalAmount: true }
    }),
    
    // Top services by usage
    prisma.service.findMany({
      include: {
        _count: {
          select: {
            projects: true,
            orderDetails: true,
          }
        }
      },
      orderBy: {
        projects: {
          _count: 'desc'
        }
      },
      take: 5
    }),
    
    // Upcoming project deadlines
    prisma.project.findMany({
      where: {
        completionDate: {
          gte: now,
          lte: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
        },
        status: { in: ['PLANNING', 'IN_PROGRESS'] }
      },
      include: {
        client: {
          select: {
            companyName: true,
          }
        }
      },
      orderBy: {
        completionDate: 'asc'
      },
      take: 10
    }),
  ])

  // Calculate growth rates (simplified - you might want to implement proper month-over-month comparison)
  const projectGrowth = activeProjects > 0 ? ((activeProjects / totalProjects) * 100) : 0
  const clientGrowth = totalClients > 0 ? 5.2 : 0 // Placeholder - implement actual calculation

  const totalRevenueAmount = totalRevenue._sum.totalAmount ? Number(totalRevenue._sum.totalAmount) : 0
  const monthlyRevenueAmount = monthlyRevenue._sum.totalAmount ? Number(monthlyRevenue._sum.totalAmount) : 0
  const revenueGrowth = totalRevenueAmount > 0 && monthlyRevenueAmount > 0
    ? ((monthlyRevenueAmount / totalRevenueAmount) * 100)
    : 0

  // Format the response
  const stats = {
    overview: {
      totalClients,
      totalProjects,
      totalServices,
      totalTeamMembers,
      activeProjects,
      completedProjects,
      pendingInvoices,
      totalRevenue: totalRevenueAmount,
      monthlyRevenue: monthlyRevenueAmount,
    },
    growth: {
      projects: projectGrowth,
      clients: clientGrowth,
      revenue: revenueGrowth,
    },
    charts: {
      projectsByStatus: projectsByStatus.map(item => ({
        status: item.status,
        count: item._count.status,
      })),
      invoicesByStatus: invoicesByStatus.map(item => ({
        status: item.status,
        count: item._count.status,
        amount: item._sum.totalAmount ? Number(item._sum.totalAmount) : 0,
      })),
    },
    recent: {
      projects: recentProjects.map(project => ({
        id: project.id,
        name: project.name,
        status: project.status,
        clientName: project.client?.companyName,
        createdAt: project.createdAt,
      })),
      clients: recentClients,
    },
    insights: {
      topServices: topServices.map(service => ({
        id: service.id,
        name: service.name,
        projectCount: service._count.projects,
        orderCount: service._count.orderDetails,
        price: Number(service.price),
      })),
      upcomingDeadlines: upcomingDeadlines.map(project => ({
        id: project.id,
        name: project.name,
        clientName: project.client?.companyName,
        deadline: project.completionDate,
        status: project.status,
      })),
    },
  }

  return successResponse(stats, 'Dashboard statistics retrieved successfully')
})
