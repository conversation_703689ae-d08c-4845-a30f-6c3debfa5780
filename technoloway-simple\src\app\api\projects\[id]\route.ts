import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse, 
  validateRequest,
  validateMethod,
  requireAdmin,
  ApiError
} from '@/lib/api-utils'
import { updateProjectSchema } from '@/lib/validations'

interface RouteContext {
  params: {
    id: string
  }
}

// GET /api/projects/[id] - Get a specific project
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteContext) => {
  const { id } = params

  const project = await prisma.project.findUnique({
    where: { id },
    include: {
      client: {
        select: {
          id: true,
          companyName: true,
          contactName: true,
          contactEmail: true,
          contactPhone: true,
          address: true,
          city: true,
          state: true,
          country: true,
        },
      },
      order: {
        select: {
          id: true,
          orderNumber: true,
          totalAmount: true,
          status: true,
          orderDate: true,
        },
      },
      services: {
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      technologies: {
        select: {
          id: true,
          name: true,
          description: true,
          iconUrl: true,
        },
        orderBy: {
          displayOrder: 'asc',
        },
      },
      tasks: {
        include: {
          teamMember: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              position: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      messages: {
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 20, // Limit recent messages
      },
      documents: {
        orderBy: {
          createdAt: 'desc',
        },
      },
      invoices: {
        select: {
          id: true,
          invoiceNumber: true,
          totalAmount: true,
          status: true,
          issueDate: true,
          dueDate: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      contracts: {
        select: {
          id: true,
          title: true,
          totalAmount: true,
          status: true,
          startDate: true,
          endDate: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      feedbacks: {
        include: {
          client: {
            select: {
              id: true,
              companyName: true,
              contactName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      _count: {
        select: {
          messages: true,
          tasks: true,
          documents: true,
          invoices: true,
          contracts: true,
          feedbacks: true,
        },
      },
    },
  })

  if (!project) {
    throw new ApiError('Project not found', 404, 'NOT_FOUND')
  }

  return successResponse(project)
})

// PUT /api/projects/[id] - Update a specific project
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const { id } = params
  const validate = validateRequest(updateProjectSchema)
  const data = await validate(request)

  // Check if project exists
  const existingProject = await prisma.project.findUnique({
    where: { id },
  })

  if (!existingProject) {
    throw new ApiError('Project not found', 404, 'NOT_FOUND')
  }

  // If orderId is being updated, check if the new order exists
  if (data.orderId) {
    const order = await prisma.order.findUnique({
      where: { id: data.orderId },
    })

    if (!order) {
      throw new ApiError('Order not found', 404, 'ORDER_NOT_FOUND')
    }
  }

  // If clientId is being updated, check if the new client exists
  if (data.clientId) {
    const client = await prisma.client.findUnique({
      where: { id: data.clientId },
    })

    if (!client) {
      throw new ApiError('Client not found', 404, 'CLIENT_NOT_FOUND')
    }
  }

  const updatedProject = await prisma.project.update({
    where: { id },
    data,
    include: {
      client: {
        select: {
          id: true,
          companyName: true,
          contactName: true,
          contactEmail: true,
        },
      },
      order: {
        select: {
          id: true,
          orderNumber: true,
          totalAmount: true,
          status: true,
        },
      },
      services: {
        select: {
          id: true,
          name: true,
          price: true,
        },
      },
      technologies: {
        select: {
          id: true,
          name: true,
          iconUrl: true,
        },
      },
      _count: {
        select: {
          messages: true,
          tasks: true,
          documents: true,
          invoices: true,
        },
      },
    },
  })

  return successResponse(updatedProject, 'Project updated successfully')
})

// DELETE /api/projects/[id] - Delete a specific project
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const { id } = params

  // Check if project exists
  const existingProject = await prisma.project.findUnique({
    where: { id },
    include: {
      invoices: { select: { id: true } },
      contracts: { select: { id: true } },
    },
  })

  if (!existingProject) {
    throw new ApiError('Project not found', 404, 'NOT_FOUND')
  }

  // Check if project has invoices or contracts
  if (existingProject.invoices.length > 0) {
    throw new ApiError(
      'Cannot delete project with invoices. Please handle invoices first.',
      400,
      'PROJECT_HAS_INVOICES'
    )
  }

  if (existingProject.contracts.length > 0) {
    throw new ApiError(
      'Cannot delete project with contracts. Please handle contracts first.',
      400,
      'PROJECT_HAS_CONTRACTS'
    )
  }

  // Delete related data first
  await prisma.projectTask.deleteMany({
    where: { projectId: id },
  })

  await prisma.projectDocument.deleteMany({
    where: { projectId: id },
  })

  await prisma.message.deleteMany({
    where: { projectId: id },
  })

  await prisma.feedback.deleteMany({
    where: { projectId: id },
  })

  // Delete the project
  await prisma.project.delete({
    where: { id },
  })

  return successResponse(null, 'Project deleted successfully')
})

// PATCH /api/projects/[id] - Partial update (status changes, etc.)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  await requireAdmin(request)
  validateMethod(request, ['PATCH'])
  
  const { id } = params
  const body = await request.json()

  // Check if project exists
  const existingProject = await prisma.project.findUnique({
    where: { id },
  })

  if (!existingProject) {
    throw new ApiError('Project not found', 404, 'NOT_FOUND')
  }

  // Handle specific patch operations
  const allowedFields = ['status', 'isFeatured', 'displayOrder']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (field in body) {
      updateData[field] = body[field]
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400, 'NO_VALID_FIELDS')
  }

  const updatedProject = await prisma.project.update({
    where: { id },
    data: updateData,
    include: {
      client: {
        select: {
          id: true,
          companyName: true,
        },
      },
      order: {
        select: {
          id: true,
          orderNumber: true,
        },
      },
    },
  })

  return successResponse(updatedProject, 'Project updated successfully')
})
