import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createProjectSchema, updateProjectSchema } from '@/lib/validations'

// GET /api/projects - List all projects with pagination and search
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, ['name', 'description', 'goals']))
  }
  
  // Add filter for project status
  if (filter && ['PLANNING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED'].includes(filter)) {
    where.status = filter
  } else if (filter === 'featured') {
    where.isFeatured = true
  } else if (filter === 'active') {
    where.status = { in: ['PLANNING', 'IN_PROGRESS'] }
  }

  // Get total count for pagination
  const total = await prisma.project.count({ where })

  // Get projects with pagination
  const projects = await prisma.project.findMany({
    where,
    include: {
      client: {
        select: {
          id: true,
          companyName: true,
          contactName: true,
          contactEmail: true,
        },
      },
      order: {
        select: {
          id: true,
          orderNumber: true,
          totalAmount: true,
        },
      },
      services: {
        select: {
          id: true,
          name: true,
          price: true,
        },
      },
      technologies: {
        select: {
          id: true,
          name: true,
          iconUrl: true,
        },
      },
      _count: {
        select: {
          messages: true,
          tasks: true,
          documents: true,
          invoices: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  return paginatedResponse(projects, page, limit, total)
})

// POST /api/projects - Create a new project
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createProjectSchema)
  const data = await validate(request)

  // Check if order exists
  const order = await prisma.order.findUnique({
    where: { id: data.orderId },
  })

  if (!order) {
    throw new Error('Order not found')
  }

  // Check if client exists (if provided)
  if (data.clientId) {
    const client = await prisma.client.findUnique({
      where: { id: data.clientId },
    })

    if (!client) {
      throw new Error('Client not found')
    }
  }

  const project = await prisma.project.create({
    data,
    include: {
      client: {
        select: {
          id: true,
          companyName: true,
          contactName: true,
          contactEmail: true,
        },
      },
      order: {
        select: {
          id: true,
          orderNumber: true,
          totalAmount: true,
        },
      },
    },
  })

  return successResponse(project, 'Project created successfully', 201)
})

// PUT /api/projects - Bulk update projects (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid project IDs provided')
  }

  const validate = validateRequest(updateProjectSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedProjects = await prisma.project.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedProjects.count },
    `${updatedProjects.count} projects updated successfully`
  )
})

// DELETE /api/projects - Bulk delete projects (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid project IDs provided')
  }

  // Check if any projects have associated data that should be preserved
  const projectsWithData = await prisma.project.findMany({
    where: {
      id: { in: ids },
      OR: [
        { invoices: { some: {} } },
        { contracts: { some: {} } },
      ],
    },
    select: { id: true, name: true },
  })

  if (projectsWithData.length > 0) {
    const projectNames = projectsWithData.map(p => p.name).join(', ')
    throw new Error(
      `Cannot delete projects with invoices or contracts: ${projectNames}. Please handle these records first.`
    )
  }

  // Delete related data first
  await prisma.projectTask.deleteMany({
    where: { projectId: { in: ids } },
  })

  await prisma.projectDocument.deleteMany({
    where: { projectId: { in: ids } },
  })

  await prisma.message.deleteMany({
    where: { projectId: { in: ids } },
  })

  const deletedProjects = await prisma.project.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedProjects.count },
    `${deletedProjects.count} projects deleted successfully`
  )
})
