import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createServiceSchema, updateServiceSchema } from '@/lib/validations'

// GET /api/services - List all services with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, ['name', 'description']))
  }
  
  // Add filter for active/inactive services
  if (filter === 'active') {
    where.isActive = true
  } else if (filter === 'inactive') {
    where.isActive = false
  }

  // Get total count for pagination
  const total = await prisma.service.count({ where })

  // Get services with pagination
  const services = await prisma.service.findMany({
    where,
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          projects: true,
          orderDetails: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  return paginatedResponse(services, page, limit, total)
})

// POST /api/services - Create a new service
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createServiceSchema)
  const data = await validate(request)

  // Check if category exists
  const category = await prisma.category.findUnique({
    where: { id: data.categoryId },
  })

  if (!category) {
    throw new Error('Category not found')
  }

  const service = await prisma.service.create({
    data,
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  return successResponse(service, 'Service created successfully', 201)
})

// PUT /api/services - Bulk update services (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid service IDs provided')
  }

  const validate = validateRequest(updateServiceSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedServices = await prisma.service.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedServices.count },
    `${updatedServices.count} services updated successfully`
  )
})

// DELETE /api/services - Bulk delete services (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid service IDs provided')
  }

  // Check if any services are being used in projects or orders
  const servicesInUse = await prisma.service.findMany({
    where: {
      id: { in: ids },
      OR: [
        { projects: { some: {} } },
        { orderDetails: { some: {} } },
      ],
    },
    select: { id: true, name: true },
  })

  if (servicesInUse.length > 0) {
    const serviceNames = servicesInUse.map(s => s.name).join(', ')
    throw new Error(
      `Cannot delete services that are in use: ${serviceNames}. Please remove them from projects and orders first.`
    )
  }

  const deletedServices = await prisma.service.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedServices.count },
    `${deletedServices.count} services deleted successfully`
  )
})
