import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createTeamMemberSchema, updateTeamMemberSchema } from '@/lib/validations'

// GET /api/team - List all team members with pagination and search
export const GET = with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, [
      'firstName', 
      'lastName', 
      'position', 
      'department', 
      'email'
    ]))
  }
  
  // Add filter for active/inactive team members
  if (filter === 'active') {
    where.isActive = true
  } else if (filter === 'inactive') {
    where.isActive = false
  }

  // Get total count for pagination
  const total = await prisma.teamMember.count({ where })

  // Get team members with pagination
  const teamMembers = await prisma.teamMember.findMany({
    where,
    include: {
      tasks: {
        select: {
          id: true,
          description: true,
          status: true,
          project: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 5, // Limit to recent tasks
      },
      _count: {
        select: {
          tasks: true,
          payrollRecords: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  return paginatedResponse(teamMembers, page, limit, total)
})

// POST /api/team - Create a new team member
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createTeamMemberSchema)
  const data = await validate(request)

  // Check if a team member with the same email already exists
  if (data.email) {
    const existingMember = await prisma.teamMember.findFirst({
      where: {
        email: data.email,
      },
    })

    if (existingMember) {
      throw new Error('A team member with this email already exists')
    }
  }

  const teamMember = await prisma.teamMember.create({
    data,
    include: {
      _count: {
        select: {
          tasks: true,
          payrollRecords: true,
        },
      },
    },
  })

  return successResponse(teamMember, 'Team member created successfully', 201)
})

// PUT /api/team - Bulk update team members (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid team member IDs provided')
  }

  const validate = validateRequest(updateTeamMemberSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedMembers = await prisma.teamMember.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedMembers.count },
    `${updatedMembers.count} team members updated successfully`
  )
})

// DELETE /api/team - Bulk delete team members (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid team member IDs provided')
  }

  // Check if any team members have associated data that should be preserved
  const membersWithData = await prisma.teamMember.findMany({
    where: {
      id: { in: ids },
      OR: [
        { tasks: { some: {} } },
        { payrollRecords: { some: {} } },
      ],
    },
    select: { id: true, firstName: true, lastName: true },
  })

  if (membersWithData.length > 0) {
    const memberNames = membersWithData.map(m => `${m.firstName} ${m.lastName}`).join(', ')
    throw new Error(
      `Cannot delete team members with associated data: ${memberNames}. Please handle their tasks and payroll records first.`
    )
  }

  const deletedMembers = await prisma.teamMember.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedMembers.count },
    `${deletedMembers.count} team members deleted successfully`
  )
})
