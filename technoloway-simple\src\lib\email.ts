import nodemailer from 'nodemailer'
import { BRANDING } from './branding'

// Email configuration
const EMAIL_CONFIG = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
}

// Create transporter
let transporter: nodemailer.Transporter | null = null

function getTransporter() {
  if (!transporter) {
    if (!EMAIL_CONFIG.auth.user || !EMAIL_CONFIG.auth.pass) {
      console.warn('Email credentials not configured. Email notifications will be disabled.')
      return null
    }
    
    transporter = nodemailer.createTransporter(EMAIL_CONFIG)
  }
  return transporter
}

// Email templates
export const EMAIL_TEMPLATES = {
  contactForm: {
    subject: 'New Contact Form Submission - {{name}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${BRANDING.theme.primaryColor};">New Contact Form Submission</h2>
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> {{name}}</p>
          <p><strong>Email:</strong> {{email}}</p>
          <p><strong>Phone:</strong> {{phone}}</p>
          <p><strong>Company:</strong> {{company}}</p>
          <p><strong>Subject:</strong> {{subject}}</p>
          <div style="margin-top: 20px;">
            <strong>Message:</strong>
            <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
              {{message}}
            </div>
          </div>
        </div>
        <p style="color: #666; font-size: 14px;">
          This message was sent from the ${BRANDING.company.name} contact form.
        </p>
      </div>
    `,
  },
  contactConfirmation: {
    subject: 'Thank you for contacting {{companyName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${BRANDING.theme.primaryColor};">Thank You for Your Message</h2>
        <p>Dear {{name}},</p>
        <p>Thank you for contacting ${BRANDING.company.name}. We have received your message and will get back to you within 24 hours.</p>
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Message:</h3>
          <p><strong>Subject:</strong> {{subject}}</p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            {{message}}
          </div>
        </div>
        <p>If you have any urgent questions, please don't hesitate to call us at ${BRANDING.contact.phone}.</p>
        <p>Best regards,<br>The ${BRANDING.company.name} Team</p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 14px;">
          ${BRANDING.company.name}<br>
          ${BRANDING.contact.address}<br>
          ${BRANDING.contact.email} | ${BRANDING.contact.phone}
        </p>
      </div>
    `,
  },
  projectUpdate: {
    subject: 'Project Update: {{projectName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${BRANDING.theme.primaryColor};">Project Update</h2>
        <p>Dear {{clientName}},</p>
        <p>We have an update on your project: <strong>{{projectName}}</strong></p>
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Status:</strong> {{status}}</p>
          <p><strong>Progress:</strong> {{progress}}%</p>
          <div style="background: #e5e7eb; border-radius: 4px; height: 8px; margin: 10px 0;">
            <div style="background: ${BRANDING.theme.primaryColor}; height: 8px; border-radius: 4px; width: {{progress}}%;"></div>
          </div>
          <div style="margin-top: 20px;">
            <strong>Update:</strong>
            <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
              {{updateMessage}}
            </div>
          </div>
        </div>
        <p>If you have any questions, please don't hesitate to reach out.</p>
        <p>Best regards,<br>The ${BRANDING.company.name} Team</p>
      </div>
    `,
  },
  invoiceNotification: {
    subject: 'New Invoice: {{invoiceNumber}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${BRANDING.theme.primaryColor};">New Invoice</h2>
        <p>Dear {{clientName}},</p>
        <p>A new invoice has been generated for your account.</p>
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Invoice Number:</strong> {{invoiceNumber}}</p>
          <p><strong>Amount:</strong> ${{amount}}</p>
          <p><strong>Due Date:</strong> {{dueDate}}</p>
          <p><strong>Project:</strong> {{projectName}}</p>
        </div>
        <div style="text-align: center; margin: 30px 0;">
          <a href="{{invoiceUrl}}" style="background: ${BRANDING.theme.primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            View Invoice
          </a>
        </div>
        <p>Please process payment by the due date to avoid any service interruptions.</p>
        <p>Best regards,<br>The ${BRANDING.company.name} Team</p>
      </div>
    `,
  },
}

// Template rendering function
function renderTemplate(template: string, variables: Record<string, any>): string {
  let rendered = template
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, 'g')
    rendered = rendered.replace(regex, String(value))
  }
  return rendered
}

// Email sending functions
export async function sendEmail(
  to: string | string[],
  subject: string,
  html: string,
  from?: string
): Promise<boolean> {
  const emailTransporter = getTransporter()
  
  if (!emailTransporter) {
    console.warn('Email transporter not configured. Skipping email send.')
    return false
  }

  try {
    const mailOptions = {
      from: from || `"${BRANDING.company.name}" <${EMAIL_CONFIG.auth.user}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
    }

    const result = await emailTransporter.sendMail(mailOptions)
    console.log('Email sent successfully:', result.messageId)
    return true
  } catch (error) {
    console.error('Failed to send email:', error)
    return false
  }
}

export async function sendContactFormNotification(data: {
  name: string
  email: string
  phone?: string
  company?: string
  subject: string
  message: string
}): Promise<boolean> {
  const template = EMAIL_TEMPLATES.contactForm
  const subject = renderTemplate(template.subject, data)
  const html = renderTemplate(template.html, {
    ...data,
    phone: data.phone || 'Not provided',
    company: data.company || 'Not provided',
  })

  // Send to admin
  const adminEmail = process.env.ADMIN_EMAIL || BRANDING.contact.email
  const adminSuccess = await sendEmail(adminEmail, subject, html)

  // Send confirmation to user
  const confirmationTemplate = EMAIL_TEMPLATES.contactConfirmation
  const confirmationSubject = renderTemplate(confirmationTemplate.subject, {
    companyName: BRANDING.company.name,
  })
  const confirmationHtml = renderTemplate(confirmationTemplate.html, {
    ...data,
    companyName: BRANDING.company.name,
  })
  
  const userSuccess = await sendEmail(data.email, confirmationSubject, confirmationHtml)

  return adminSuccess && userSuccess
}

export async function sendProjectUpdateNotification(data: {
  clientName: string
  clientEmail: string
  projectName: string
  status: string
  progress: number
  updateMessage: string
}): Promise<boolean> {
  const template = EMAIL_TEMPLATES.projectUpdate
  const subject = renderTemplate(template.subject, data)
  const html = renderTemplate(template.html, data)

  return await sendEmail(data.clientEmail, subject, html)
}

export async function sendInvoiceNotification(data: {
  clientName: string
  clientEmail: string
  invoiceNumber: string
  amount: number
  dueDate: string
  projectName: string
  invoiceUrl: string
}): Promise<boolean> {
  const template = EMAIL_TEMPLATES.invoiceNotification
  const subject = renderTemplate(template.subject, data)
  const html = renderTemplate(template.html, {
    ...data,
    amount: data.amount.toFixed(2),
  })

  return await sendEmail(data.clientEmail, subject, html)
}

// Test email function
export async function sendTestEmail(to: string): Promise<boolean> {
  const subject = `Test Email from ${BRANDING.company.name}`
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: ${BRANDING.theme.primaryColor};">Email Configuration Test</h2>
      <p>This is a test email to verify that your email configuration is working correctly.</p>
      <p>If you received this email, your SMTP settings are properly configured!</p>
      <p>Best regards,<br>The ${BRANDING.company.name} Team</p>
    </div>
  `

  return await sendEmail(to, subject, html)
}
