import { z } from 'zod'

// User schemas
export const createUserSchema = z.object({
  email: z.string().email(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  imageUrl: z.string().url().optional(),
  role: z.enum(['ADMIN', 'USER', 'CLIENT']).default('USER'),
})

export const updateUserSchema = createUserSchema.partial()

// Service schemas
export const createServiceSchema = z.object({
  categoryId: z.string(),
  name: z.string().min(1).max(255),
  description: z.string().min(1),
  iconClass: z.string().max(100).optional(),
  price: z.number().positive(),
  discountRate: z.number().int().min(0).max(100).optional(),
  totalDiscount: z.number().optional(),
  manager: z.string().max(50).optional(),
  isActive: z.boolean().default(true),
  displayOrder: z.number().int().default(0),
})

export const updateServiceSchema = createServiceSchema.partial()

// Project schemas
export const createProjectSchema = z.object({
  orderId: z.string(),
  clientId: z.string().optional(),
  name: z.string().min(1),
  description: z.string().min(1),
  goals: z.string().optional(),
  manager: z.string().max(10).optional(),
  startDate: z.date().optional(),
  completionDate: z.date().optional(),
  estimatedCost: z.number().positive().optional(),
  estimatedTime: z.string().max(10).optional(),
  estimatedEffort: z.string().max(10).optional(),
  status: z.enum(['PLANNING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED']).default('PLANNING'),
  isFeatured: z.boolean().default(false),
  displayOrder: z.number().int().default(0),
  imageUrl: z.string().url().optional(),
  projectUrl: z.string().url().optional(),
  githubUrl: z.string().url().optional(),
})

export const updateProjectSchema = createProjectSchema.partial()

// Client schemas
export const createClientSchema = z.object({
  userId: z.string().optional(),
  companyName: z.string().min(1).max(200),
  contactName: z.string().min(1).max(100),
  contactPosition: z.string().max(100).optional(),
  contactEmail: z.string().email().max(100),
  contactPhone: z.string().max(20).optional(),
  contactFax: z.string().max(20).optional(),
  companyWebsite: z.string().max(100).optional(),
  address: z.string().min(1).max(200),
  city: z.string().min(1).max(100),
  state: z.string().min(1).max(50),
  zipCode: z.string().min(1).max(20),
  country: z.string().min(1).max(100),
  logoUrl: z.string().max(500).optional(),
  notes: z.string().optional(),
})

export const updateClientSchema = createClientSchema.partial()

// Blog post schemas
export const createBlogPostSchema = z.object({
  authorId: z.string(),
  title: z.string().min(1).max(255),
  slug: z.string().min(1).max(255),
  content: z.string().min(1),
  excerpt: z.string().optional(),
  featuredImageUrl: z.string().max(500).optional(),
  isPublished: z.boolean().default(false),
  publishedAt: z.date().optional(),
  categories: z.string().optional(),
  tags: z.string().optional(),
})

export const updateBlogPostSchema = createBlogPostSchema.partial()

// Team member schemas
export const createTeamMemberSchema = z.object({
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  position: z.string().min(1),
  department: z.string().optional(),
  phone: z.string().min(1).max(12),
  email: z.string().email().max(255).optional(),
  salary: z.number().positive().optional(),
  payrollMethod: z.string().max(10).optional(),
  resumeUrl: z.string().max(500).optional(),
  notes: z.string().optional(),
  bio: z.string().optional(),
  photoUrl: z.string().max(500).optional(),
  linkedInUrl: z.string().max(500).optional(),
  twitterUrl: z.string().max(500).optional(),
  githubUrl: z.string().max(500).optional(),
  displayOrder: z.number().int().default(0),
  isActive: z.boolean().default(true),
})

export const updateTeamMemberSchema = createTeamMemberSchema.partial()

// Technology schemas
export const createTechnologySchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  iconUrl: z.string().url().optional(),
  displayOrder: z.number().int().default(0),
  isActive: z.boolean().default(true),
})

export const updateTechnologySchema = createTechnologySchema.partial()

// Testimonial schemas
export const createTestimonialSchema = z.object({
  clientName: z.string().min(1).max(100),
  clientTitle: z.string().min(1).max(100),
  clientCompany: z.string().min(1).max(100),
  clientPhotoUrl: z.string().max(500).optional(),
  content: z.string().min(1),
  rating: z.number().int().min(1).max(5).default(5),
  isFeatured: z.boolean().default(false),
  displayOrder: z.number().int().default(0),
})

export const updateTestimonialSchema = createTestimonialSchema.partial()

// Contact form schemas
export const createContactFormSchema = z.object({
  userId: z.string().optional(),
  name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().optional(),
  company: z.string().optional(),
  subject: z.string().min(1),
  message: z.string().min(1),
  status: z.enum(['NEW', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']).default('NEW'),
})

export const updateContactFormSchema = createContactFormSchema.partial()

// Category schemas
export const createCategorySchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  parentId: z.string().optional(),
  isActive: z.boolean().default(true),
  displayOrder: z.number().int().default(0),
})

export const updateCategorySchema = createCategorySchema.partial()

// Order schemas
export const createOrderSchema = z.object({
  clientId: z.string(),
  orderNumber: z.string().min(1),
  description: z.string().optional(),
  totalAmount: z.number().positive(),
  status: z.enum(['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PENDING'),
  orderDate: z.date().default(() => new Date()),
})

export const updateOrderSchema = createOrderSchema.partial()

// Invoice schemas
export const createInvoiceSchema = z.object({
  clientId: z.string(),
  projectId: z.string().optional(),
  orderId: z.string().optional(),
  contractId: z.string().optional(),
  invoiceNumber: z.string().min(1),
  description: z.string().optional(),
  subtotal: z.number().positive(),
  taxAmount: z.number().default(0),
  totalAmount: z.number().positive(),
  status: z.enum(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED']).default('DRAFT'),
  issueDate: z.date().default(() => new Date()),
  dueDate: z.date(),
  paidAt: z.date().optional(),
})

export const updateInvoiceSchema = createInvoiceSchema.partial()

// Job listing schemas
export const createJobListingSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(1),
  requirements: z.string().min(1),
  location: z.string().min(1),
  employmentType: z.string().min(1),
  salaryMin: z.number().positive().optional(),
  salaryMax: z.number().positive().optional(),
  salaryCurrency: z.string().default('USD'),
  isRemote: z.boolean().default(false),
  isActive: z.boolean().default(true),
  expiresAt: z.date().optional(),
})

export const updateJobListingSchema = createJobListingSchema.partial()

// Export all schemas as a single object for easier imports
export const schemas = {
  user: { create: createUserSchema, update: updateUserSchema },
  service: { create: createServiceSchema, update: updateServiceSchema },
  project: { create: createProjectSchema, update: updateProjectSchema },
  client: { create: createClientSchema, update: updateClientSchema },
  blogPost: { create: createBlogPostSchema, update: updateBlogPostSchema },
  teamMember: { create: createTeamMemberSchema, update: updateTeamMemberSchema },
  technology: { create: createTechnologySchema, update: updateTechnologySchema },
  testimonial: { create: createTestimonialSchema, update: updateTestimonialSchema },
  contactForm: { create: createContactFormSchema, update: updateContactFormSchema },
  category: { create: createCategorySchema, update: updateCategorySchema },
  order: { create: createOrderSchema, update: updateOrderSchema },
  invoice: { create: createInvoiceSchema, update: updateInvoiceSchema },
  jobListing: { create: createJobListingSchema, update: updateJobListingSchema },
}
